package jwts

import (
	"fmt"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// mySigningKey defines the secret key for signing the JWT tokens.
// TODO: Consider moving this to a configuration file or environment variable for better security and flexibility.
var mySigningKey = []byte("hello-hungry-abtest")

// GenerateToken generates a JWT token for a given user ID and admin status.
func GenerateToken(uid int, admin bool) (string, error) {
	// Create the Claims
	claims := &jwt.MapClaims{
		"sub":   "abtest",                                     // Subject (业务标识)
		"iat":   jwt.NewNumericDate(time.Now()),               // Issued at (签发时间)
		"exp":   jwt.NewNumericDate(time.Now().Add(24 * time.Hour)), // Expiration time (过期时间，例如24小时后)
		"uid":   fmt.Sprintf("%d", uid),                       // User ID
		"admin": fmt.Sprintf("%t", admin),                     // Is admin
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	s, err := token.SignedString(mySigningKey)
	if err != nil {
		return "", err
	}
	return s, nil
}

// Verify 校验 JWT 签名
func Verify(token string, key interface{}) (*jwt.Token, error) {
	parsedToken, err := jwt.Parse(token, func(t *jwt.Token) (interface{}, error) { return key, nil })
	if err != nil {
		return nil, err
	}

	tokens := strings.Split(token, ".")

	sig, err := jwt.NewParser().DecodeSegment(tokens[2])
	if err != nil {
		return nil, err
	}

	return parsedToken, parsedToken.Method.Verify(strings.Join(tokens[0:2], "."), sig, key)
}
