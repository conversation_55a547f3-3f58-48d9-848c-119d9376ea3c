package jwts

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/stretchr/testify/assert"
)

func TestJwts(t *testing.T) {
	mySigningKey := []byte("hello-hungry-abtest")

	// Create the Claims
	claims := &jwt.MapClaims{
		"sub":   "abtest",                                     // 业务标识
		"iat":   jwt.NewNumericDate(time.Unix(1500000000, 0)), // 签发时间
		"exp":   jwt.NewNumericDate(time.Unix(2000000000, 0)), // 过期时间
		"uid":   "335",                                        // 用户ID
		"admin": "true",                                       // 是否管理员
	}
	s1, e1 := json.Marshal(claims)
	t.Log(string(s1), e1)

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	s, err := token.SignedString(mySigningKey)
	assert.Nil(t, err)
	t.Log(s)

	// verify
	parsedToken, err := Verify(s, mySigningKey)
	assert.Nil(t, err)

	assert.Equal(t, "JWT", parsedToken.Header["typ"])
	assert.Equal(t, "HS256", parsedToken.Header["alg"])

	payload := parsedToken.Claims.(jwt.MapClaims)
	assert.Equal(t, "abtest", payload["sub"])
	assert.Equal(t, "335", payload["uid"].(string))
	assert.Equal(t, "true", payload["admin"])
}

func TestGenerateToken(t *testing.T) {
	uid := 123
	admin := true

	tokenString, err := GenerateToken(uid, admin)
	assert.Nil(t, err, "GenerateToken should not return an error")
	assert.NotEmpty(t, tokenString, "Generated token string should not be empty")

	// Use the same signing key as in GenerateToken for verification
	// Note: mySigningKey is a package-level variable in jwts.go
	parsedToken, err := Verify(tokenString, mySigningKey)
	assert.Nil(t, err, "Verify should not return an error")
	assert.NotNil(t, parsedToken, "Parsed token should not be nil")

	assert.True(t, parsedToken.Valid, "Token should be valid")

	payload, ok := parsedToken.Claims.(jwt.MapClaims)
	assert.True(t, ok, "Claims should be of type jwt.MapClaims")

	assert.Equal(t, "abtest", payload["sub"], "Subject should be 'abtest'")
	assert.Equal(t, "123", payload["uid"], "UID should match")
	assert.Equal(t, "true", payload["admin"], "Admin status should match")

	// Check iat and exp types and basic validity (exp > iat)
	iat, iatOk := payload["iat"].(float64) // jwt.NumericDate is often parsed as float64
	exp, expOk := payload["exp"].(float64)
	assert.True(t, iatOk, "iat should be present and be a number")
	assert.True(t, expOk, "exp should be present and be a number")
	assert.Greater(t, exp, iat, "Expiration time should be after issued-at time")
}
