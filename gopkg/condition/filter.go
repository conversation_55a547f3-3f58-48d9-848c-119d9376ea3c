package condition

import (
	"reflect"
)

// Filter 过滤器(实验策略的规则)
type Filter struct {
	Logic      LogicOperatorType `json:"logic,omitempty"`
	Conditions []*Filter         `json:"conditions,omitempty"`
	Key        string            `json:"key,omitempty"`
	Op         OpType            `json:"op,omitempty"`
	Value      interface{}       `json:"value,omitempty"`
	Type       ValueType         `json:"type,omitempty"`
}

type Attr func(key string) (any, bool)

func MapAttr(m map[string]any) Attr {
	return func(key string) (any, bool) {
		v, ok := m[key]
		return v, ok
	}
}

// EvaluateFilter 实验策略解析，key，value对应Filter的Key、Value
func (f *Filter) EvaluateFilter(attributes map[string]interface{}) bool {
	return f.Evaluate(MapAttr(attributes))
}

func (f *Filter) Evaluate(attributes Attr) bool {
	if f == nil {
		return true
	}

	if len(f.Conditions) == 0 {
		return f.evaluate(attributes)
	}

	logicFn := LogicFunc(f.Logic)
	if logicFn == nil {
		return false
	}

	var result bool
	switch f.Logic {
	case AND, AndSymbol:
		result = true
	}

	// 根据逻辑运算符将初始结果与子过滤器结果合并得到最终结果
	for _, subFilter := range f.Conditions {
		result = logicFn(result, subFilter.Evaluate(attributes))
	}
	return result
}

// Evaluate 实验策略解析，attributes为用户传递的值，key，value对应Filter的Key、Value
func (f *Filter) evaluate(attributes Attr) bool {
	defer func() {
		if err := recover(); err != nil {
			//log.WithFields(log.Fields{"err": err}).Error("Evaluate error")
		}
	}()
	if len(f.Key) == 0 {
		return true
	}

	attrValue, ok := attributes(f.Key)

	if f.Op == IsNull || f.Op == IsNotNull {
		return MathFunc(f.Op)(attrValue, nil, f.Type)
	}

	if !ok || attrValue == nil {
		return false
	}

	valueTyper := f.getValueTyper()
	if valueTyper == nil {
		//log.WithFields(log.Fields{"type": c.Type}).Error("not support type")
		return false
	}

	if f.Op == Contain || f.Op == ContainSymbol || f.Op == NotContain || f.Op == NotContainSymbol ||
		f.Op == ContainAny || f.Op == ExcludeAll {
		adaptedConditions, adaptedAttrs, err := valueTyper.AdaptValue(f.Value, attrValue)
		if err != nil {
			return false
		}

		return MathFunc(f.Op)(adaptedAttrs, adaptedConditions, f.Type)
	}

	if f.Op == IN || f.Op == InSymbol || f.Op == NotIn || f.Op == NotInSymbol {
		condValue := reflect.ValueOf(f.Value)
		condKind := condValue.Kind()
		if condKind != reflect.Slice && condKind != reflect.Array {
			condValue = reflect.ValueOf([]interface{}{f.Value})
		}
		adaptedConditions := make([]interface{}, 0)
		var adaptedAttrs interface{}
		for i := 0; i < condValue.Len(); i++ {
			adaptedCond, adaptedAttr, err := valueTyper.AdaptValue(condValue.Index(i).Interface(), attrValue)
			if err != nil {
				//log.WithFields(log.Fields{"err": err}).Error("cond value adapt occur an error")
				return false
			}
			adaptedConditions = append(adaptedConditions, adaptedCond)
			adaptedAttrs = adaptedAttr
		}
		return MathFunc(f.Op)(adaptedAttrs, adaptedConditions, f.Type)
	}

	if valueTyper == stringValueTyper {
		return MathFunc(f.Op)(attrValue, f.Value, f.Type)
	}

	return f.evaluateAttrValue(f.Value, attrValue, valueTyper)
}

// getValueTyper 获取Value值对应的类型
func (f *Filter) getValueTyper() ConditionValueTyper {
	switch f.Type {
	case INT, FLOAT:
		return numberValueTyper
	case BOOL:
		return boolValueTyper
	case STRING, StringVersion:
		return stringValueTyper
	case ListInt, ListString, ListStringDefault, RangeInt, RangeFloat, RangeIntDefault:
		return complexValueTyper
	case Timestamp:
		return numberValueTyper
	default:
		return nil
	}
}

// evaluateAttrValue 属性解析
func (f *Filter) evaluateAttrValue(condValue, attrValue interface{}, valueTyper ConditionValueTyper) bool {
	adaptedCond, adaptedAttr, err := valueTyper.AdaptValue(condValue, attrValue)
	if err != nil {
		//log.WithFields(log.Fields{"err": err}).Error("cond value adapt occur an error")
		return false
	}
	return MathFunc(f.Op)(adaptedAttr, adaptedCond, f.Type)
}
