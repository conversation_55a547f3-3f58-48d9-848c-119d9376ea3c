package condition

import (
	"reflect"
	"strconv"

	"github.com/cockroachdb/errors"
)

type ValueType string

// 如果增加类型，请一定修改 getValueTyper()
const (
	INT           ValueType = "int"
	FLOAT         ValueType = "float"
	STRING        ValueType = "string"
	StringVersion ValueType = "string.version"
	BOOL          ValueType = "boolean"
	Timestamp     ValueType = "timestamp"
	// range类型是 数字逗号数字格式，且要求严格递增
	RangeInt          ValueType = "range.int"
	RangeIntDefault   ValueType = "range.int.default"
	RangeFloat        ValueType = "range.float"
	ListInt           ValueType = "list.int"
	ListIntDefault    ValueType = "list.int.default"
	ListFloat         ValueType = "list.float"
	ListBool          ValueType = "list.bool"
	ListString        ValueType = "list.string"
	ListStringDefault ValueType = "list.string.default"
	MapString         ValueType = "map.string"
	MapInt            ValueType = "map.int"
	MapFloat          ValueType = "map.float"
	MapBool           ValueType = "map.bool"
)

var (
	numberValueTyper  = NumberTyper{}
	boolValueTyper    = BoolTyper{}
	stringValueTyper  = StringTyper{}
	complexValueTyper = ComplexTyper{}
)

type ConditionValueTyper interface {
	EvaluateKind(v reflect.Value) bool
	AdaptValue(condValue, attrValue interface{}) (adaptedCond interface{}, adaptedAttr interface{}, err error)
}

type NumberTyper struct {
}

func (n NumberTyper) EvaluateKind(v reflect.Value) bool {
	switch v.Kind() {
	case reflect.Invalid:
		return false
	case reflect.Int, reflect.Int8, reflect.Int16,
		reflect.Int32, reflect.Int64:
		return true
	case reflect.Uint, reflect.Uint8, reflect.Uint16,
		reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		return true
	case reflect.Float32, reflect.Float64:
		return true
	case reflect.String:
		_, err := strconv.ParseFloat(v.String(), 64)
		return err == nil
	default:
		return false
	}
}

func (n NumberTyper) AdaptValue(condValue, attrValue interface{}) (float64Cond, float64Attr interface{}, err error) {
	if condValueStr, ok := condValue.(string); ok {
		float64Cond, err = strconv.ParseFloat(condValueStr, 64)
	} else {
		float64Cond, err = ToFloat64(condValue)
	}
	float64Attr, err = ToFloat64(attrValue)
	return float64Cond, float64Attr, err
}

type BoolTyper struct {
}

func (b BoolTyper) EvaluateKind(v reflect.Value) bool {
	switch v.Kind() {
	case reflect.Invalid:
		return false
	case reflect.Bool:
		return true
	case reflect.String:
		strValue := v.String()
		return strValue == "true" || strValue == "false"
	default:
		return false
	}
}

func (b BoolTyper) AdaptValue(condValue, attrValue interface{}) (boolCond interface{}, boolAttr interface{}, err error) {
	if condValueStr, ok := condValue.(string); ok {
		boolCond, err = strconv.ParseBool(condValueStr)
	} else {
		boolCond = condValue.(bool)
	}
	boolAttr = attrValue.(bool)
	return boolCond, boolAttr, err
}

type StringTyper struct {
}

func (s StringTyper) EvaluateKind(v reflect.Value) bool {
	switch v.Kind() {
	case reflect.Invalid:
		return false
	case reflect.String:
		return true
	default:
		return false
	}
}

func (s StringTyper) AdaptValue(condValue, attrValue interface{}) (stringCond interface{}, stringAttr interface{}, err error) {
	return condValue.(string), attrValue.(string), nil
}

type ComplexTyper struct {
}

func (c ComplexTyper) EvaluateKind(v reflect.Value) bool {
	switch v.Kind() {
	case reflect.Invalid:
		return false
	case reflect.Slice, reflect.Map, reflect.Array:
		return true
	default:
		return false
	}
}

// AdaptValue 用于处理复杂类型（如map、数组等），递归地将其元素转换为合适的基本类型
func (c ComplexTyper) AdaptValue(condValue, attrValue interface{}) (adaptedCond interface{}, adaptedAttr interface{}, err error) {
	adaptedCond, err = AdaptComplexValue(condValue)
	if err != nil {
		return nil, nil, err
	}
	adaptedAttr, err = AdaptComplexValue(attrValue)
	return adaptedCond, adaptedAttr, err
}

// AdaptComplexValue 用于处理复杂类型（如map、数组等），递归地将其元素转换为合适的基本类型
func AdaptComplexValue(value interface{}) (interface{}, error) {
	valueType := reflect.ValueOf(value).Kind()
	switch valueType {
	case reflect.Map:
		resultMap := make(map[string]interface{})
		mapValue := reflect.ValueOf(value)
		for _, key := range mapValue.MapKeys() {
			elemValue := mapValue.MapIndex(key)
			adaptedElem, err := AdaptComplexValue(elemValue.Interface())
			if err != nil {
				return nil, err
			}
			resultMap[key.String()] = adaptedElem
		}
		return resultMap, nil
	case reflect.Slice, reflect.Array:
		resultSlice := make([]interface{}, 0)
		sliceValue := reflect.ValueOf(value)
		for i := 0; i < sliceValue.Len(); i++ {
			elemValue := sliceValue.Index(i).Interface()
			adaptedElem, err := AdaptComplexValue(elemValue)
			if err != nil {
				return nil, err
			}
			resultSlice = append(resultSlice, adaptedElem)
		}
		return resultSlice, nil
	default:
		return value, nil
	}
}

// ToFloat64 attempts to convert the given value to a float64
func ToFloat64(value interface{}) (float64, error) {
	if value == nil {
		return 0, errors.New("cond value is nil")
	}
	var floatType = reflect.TypeOf(float64(0))
	v := reflect.ValueOf(value)
	v = reflect.Indirect(v)

	if v.Type().String() == "float64" || v.Type().ConvertibleTo(floatType) {
		floatValue := v.Convert(floatType).Float()
		return floatValue, nil
	}
	return 0, errors.New("cond value can not convert to number")
}
