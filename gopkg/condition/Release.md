### 规则
结构取值
```go
package release

type ConditionOpType string

const (
	GreaterThan        ConditionOpType = ">"
	GreaterThanOrEqual ConditionOpType = ">="
	LessThan           ConditionOpType = "<"
	LessThanOrEqual    ConditionOpType = "<="
	IN                 ConditionOpType = "in"
	NotIn              ConditionOpType = "ni"
	IsNull             ConditionOpType = "is_null"
	IsNotNull          ConditionOpType = "is_not_null"
	Contain            ConditionOpType = "contain"
)

type ConditionValueType string

const (
	INT    ConditionValueType = "int"
	FLOAT  ConditionValueType = "float"
	NUMBER ConditionValueType = "number"
	STRING ConditionValueType = "string"
	BOOL   ConditionValueType = "boolean"
)

type LogicOperatorType string

const (
	AND LogicOperatorType = "&&"
	OR  LogicOperatorType = "||"
)

type StringCompareMethod = string

// 当版本比较时，用version，其他目前来看可以省略
const (
	DictCompare   StringCompareMethod = "dict"
	SemverCompare StringCompareMethod = "version"
)

type Condition struct {
	Key           string              `json:"key"`
	Op            ConditionOpType     `json:"op"`
	LogicOperator LogicOperatorType   `json:"logic_operator"`
	Value         interface{}         `json:"value"`
	Type          ConditionValueType  `json:"type"`
	Method        StringCompareMethod `json:"method"`
}

type Filter struct {
	Conditions    []Condition       `json:"conditions"`
	LogicOperator LogicOperatorType `json:"logic_operator"`
}
```
json示例
```json
[
  {
    "conditions": [
      {
        "key": "resVersion",
        "op": ">=",
        "logic_operator": "&&",
        "Value": "2.1.1",
        "methhod": "version"
      },
      {
        "key": "country",
        "op": "in",
        "logic_operator": "&&",
        "Value": ["us","cn"]
      }
    ],
    "logic_operator": "||"
  },
  {
    "conditions": [
      {
        "key": "sdkVersion",
        "op": ">=",
        "logic_operator": "&&",
        "Value": "5.1.1",
        "methhod": "version"
      }
    ],
    "logic_operator": "||"
  }
]
```