package condition

type OpType string

// eq（==）、ne（!=）、gt（>）、lt（<）、ge（>=）、le（<=）、in（∈）、ni（∉）、contain（⊆）、not_contain（⊈）
const (
	Equal              OpType = "eq"
	NotEqual           OpType = "ne"
	GreaterThan        OpType = "gt"
	GreaterThanOrEqual OpType = "ge"
	LessThan           OpType = "lt"
	LessThanOrEqual    OpType = "le"
	IN                 OpType = "in"
	NotIn              OpType = "ni"
	IsNull             OpType = "is_null"
	IsNotNull          OpType = "is_not_null"
	Contain            OpType = "contain"
	NotContain         OpType = "not_contain"
	ContainAny         OpType = "contain_any"
	ExcludeAll         OpType = "exclude_all"
)

const (
	EqualSymbol              OpType = "=="
	EqualAssignment          OpType = "="
	NotEqualSymbol           OpType = "!="
	GreaterThanSymbol        OpType = ">"
	GreaterThanOrEqualSymbol OpType = ">="
	LessThanSymbol           OpType = "<"
	LessThanOrEqualSymbol    OpType = "<="
	InSymbol                 OpType = "∈"
	NotInSymbol              OpType = "∉"
	ContainSymbol            OpType = "⊆"
	NotContainSymbol         OpType = "⊈"
)

// IsRelationOp 校验操作符是否合法
func IsRelationOp(relation OpType) bool {
	switch relation {
	case Equal, EqualSymbol, EqualAssignment,
		NotEqual, NotEqualSymbol,
		GreaterThan, GreaterThanSymbol,
		GreaterThanOrEqual, GreaterThanOrEqualSymbol,
		LessThan, LessThanSymbol,
		LessThanOrEqual, LessThanOrEqualSymbol,
		IN, InSymbol,
		NotIn, NotInSymbol,
		IsNull,
		IsNotNull,
		Contain, ContainSymbol,
		NotContain, NotContainSymbol,
		ContainAny,
		ExcludeAll:
		return true
	default:
		return false
	}
}
