package condition

type LogicOperatorType string

const (
	AND LogicOperatorType = "and"
	OR  LogicOperatorType = "or"
)

const (
	AndSymbol LogicOperatorType = "&&"
	OrSymbol  LogicOperatorType = "||"
)

type LogicFn func(x, y bool) bool

func LogicFunc(logic LogicOperatorType) LogicFn {
	switch logic {
	case AND, AndSymbol:
		return func(x, y bool) bool {
			return x && y
		}
	case OR, OrSymbol:
		return func(x, y bool) bool {
			return x || y
		}
	default:
		return nil
	}
}

// IsLogicOp 校验操作符是否合法
func IsLogicOp(logic LogicOperatorType) bool {
	switch logic {
	case AND, AndSymbol, OR, OrSymbol:
		return true
	default:
		return false
	}
}
