package condition

import (
	"encoding/json"
	"testing"
	"time"
)

func TestFilter(t *testing.T) {
	// country in ("us", "cn") and (...)
	filter := Filter{
		Logic: AND,
		Conditions: []*Filter{
			{
				Key:   "now_ts",
				Op:    GreaterThanOrEqual,
				Value: 1,
				Type:  Timestamp,
			},
			{
				Key:   "now_ts",
				Op:    LessThan,
				Value: 2737043200,
				Type:  Timestamp,
			},
			{
				Key:   "country",
				Op:    IN,
				Value: []string{"us", "cn"},
				Type:  STRING,
			},
			{
				Key:   "client_version",
				Op:    GreaterThanOrEqual,
				Value: "3.2.2",
				Type:  STRING,
			},
			{
				Key:   "sdkVersion",
				Op:    GreaterThanOrEqual,
				Value: "5.8.1",
				Type:  STRING,
			},
		},
	}
	attributes := map[string]any{"now_ts": time.Now().Unix(), "country": "us", "client_version": "3.2.3", "sdkVersion": "5.8.1"}
	if ok := filter.EvaluateFilter(attributes); ok {
		t.Logf("test filter ok")
	} else {
		t.<PERSON>("test filter failed")
	}
	//filter2 := Filter{
	//	Logic: AND,
	//	Conditions: []Condition{
	//		{
	//			Key:           "sdkVersion",
	//			Op:            LessThanOrEqual,
	//			Logic: AND,
	//			Value:         "5.8.1",
	//			Type:          STRING,
	//			Method:        SemverCompare,
	//		},
	//	},
	//}
	//filters := Filters{filter1, filter2}
	//if ok := filters.Evaluate(attributes); ok {
	//	t.Logf("test filters ok")
	//} else {
	//	t.Errorf("test filters failed")
	//}
}

func BenchmarkFilter(b *testing.B) {
	//var release *Release
	//filter1 := Filter{
	//	Logic: OR,
	//	Conditions: []*Filter{
	//		{
	//			Key:   "country",
	//			Op:    IN,
	//			Value: []string{"us", "cn"},
	//			Type:  STRING,
	//		},
	//		{
	//			Key:    "client_version",
	//			Op:     GreaterThanOrEqual,
	//			Value:  "3.2.2",
	//			Type:   STRING,
	//
	//		},
	//	},
	//}
	//
	//release = &Release{
	//	Filters: []Filter{filter1},
	//}
	//
	//attributes := map[string]any{"country": "us", "client_version": "3.2.3", "sdkVersion": "1.1.1", "score": 20000}
	//
	//b.ResetTimer()
	//b.Run("filter1", func(b *testing.B) {
	//	for i := 0; i < b.N; i++ {
	//		_ = release.EvaluateFilters(attributes)
	//	}
	//})
	//
	//filter2 := Filter{
	//	Logic: OR,
	//	Conditions: []*Filter{
	//		{
	//			Key:    "sdkVersion",
	//			Op:     GreaterThanOrEqual,
	//			Value:  "2.0.1",
	//			Type:   STRING,
	//
	//		},
	//		{
	//			Key:   "score",
	//			Op:    GreaterThanOrEqual,
	//			Value: 10000,
	//			Type:  INT,
	//		},
	//	},
	//}
	//attributes = map[string]any{"country": "us", "client_version": "3.2.3", "sdkVersion": "2.2", "score": 20000}
	//release = &Release{
	//	Filters: []Filter{filter1, filter2},
	//}
	//b.ResetTimer()
	//b.Run("filters", func(b *testing.B) {
	//	for i := 0; i < b.N; i++ {
	//		_ = release.EvaluateFilters(attributes)
	//	}
	//})
}

func TestFilter_EvaluateFilter(t *testing.T) {
	data := `{"conditions":[{"key":"history_exp_ids","op":"contain","type":"list.int","value":["837"]},{"key":"history_grp_ids","op":"contain","type":"list.int","value":["1692"]}],"logic":"and"}`
	containFilter1 := &Filter{}
	err := json.Unmarshal([]byte(data), containFilter1)
	if err != nil {
		panic(err)
	}
	// country in ("us", "cn") and client_version >= '3.2.2' and sdkVersion >= '5.8.1'
	filter10 := &Filter{
		Logic: AND,
		Conditions: []*Filter{
			{
				Key:   "country",
				Op:    IN,
				Value: []string{"us", "cn"},
				Type:  ListString,
			},
			{
				Key:   "client_version",
				Op:    GreaterThanOrEqual,
				Value: "3.2.2",
				Type:  StringVersion,
			},
			{
				Key:   "sdkVersion",
				Op:    GreaterThanOrEqual,
				Value: "5.8.1",
				Type:  StringVersion,
			},
			{
				Key:   "manufacturers",
				Op:    IN,
				Value: "1,2",
				Type:  STRING,
			},
		},
	}

	// （country in ("us", "cn") and client_version >= '3.2.2'）or ( client_version >= '3.2.2' and sdkVersion >= '5.8.1')
	filter20 := &Filter{
		Logic: OR,
		Conditions: []*Filter{
			{
				Conditions: []*Filter{
					{
						Key:   "country",
						Op:    IN,
						Value: []string{"us", "cn"},
						Type:  ListString,
					},
					{
						Key:   "client_version",
						Op:    GreaterThanOrEqual,
						Value: "3.2.2",
						Type:  StringVersion,
					},
				},
				Logic: AND,
			},
			{
				Conditions: []*Filter{
					{
						Key:   "client_version",
						Op:    GreaterThanOrEqual,
						Value: "3.2.2",
						Type:  StringVersion,
					},
					{
						Key:   "sdkVersion",
						Op:    GreaterThanOrEqual,
						Value: "5.8.1",
						Type:  StringVersion,
					},
				},
				Logic: AND,
			},
		},
	}

	// （country in ("us", "cn") or client_version >= '3.2.2'）and (client_version >= '3.2.2' or sdkVersion >= '5.8.1')
	filter21 := &Filter{
		Logic: AND,
		Conditions: []*Filter{
			{
				Conditions: []*Filter{
					{
						Key:   "country",
						Op:    IN,
						Value: []string{"us", "cn"},
						Type:  ListString,
					},
					{
						Key:   "client_version",
						Op:    GreaterThanOrEqual,
						Value: "3.2.2",
						Type:  StringVersion,
					},
				},
				Logic: OR,
			},
			{
				Conditions: []*Filter{
					{
						Key:   "client_version",
						Op:    GreaterThanOrEqual,
						Value: "3.2.2",
						Type:  StringVersion,
					},
					{
						Key:   "sdkVersion",
						Op:    GreaterThanOrEqual,
						Value: "5.8.1",
						Type:  StringVersion,
					},
				},
				Logic: OR,
			},
		},
	}

	// （country in ("us", "cn") and client_version >= '3.2.2' and sdkVersion >= '5.8.1'） or (trueKey >= 1)
	filter30 := &Filter{
		Logic: OR,
		Conditions: []*Filter{
			filter10,
			{
				Key:   "trueKey",
				Op:    GreaterThanOrEqual,
				Type:  INT,
				Value: 1,
			},
		},
	}

	countryFilter := &Filter{}
	json.Unmarshal([]byte(`{"op": "in", "key": "country", "type": "list.string", "value": ["kr", "tw", "jp", "cn", "gb"]}`), countryFilter)

	type args struct {
		attributes map[string]interface{}
	}
	tests := []struct {
		name   string
		filter *Filter
		args   args
		want   bool
	}{
		/*********************************** 一层 ***********************************/
		// 单个case
		/**************************** country in  ****************************/
		{
			//country in ("us", "cn")
			name: "test country true",
			args: args{attributes: map[string]any{"country": "us"}},
			filter: &Filter{
				Key:   "country",
				Op:    IN,
				Value: []string{"us", "cn"},
				Type:  ListString,
			},
			want: true,
		}, {
			//country in ("us", "cn")
			name: "test country true",
			args: args{attributes: map[string]any{"country": "cn"}},
			filter: &Filter{
				Key:   "country",
				Op:    IN,
				Value: []string{"us", "cn"},
				Type:  ListString,
			},
			want: true,
		},
		{
			//country in ("us", "cn")
			name: "test country false",
			args: args{attributes: map[string]any{"country": "u1s"}},
			filter: &Filter{
				Key:   "country",
				Op:    IN,
				Value: []string{"us", "cn"},
				Type:  ListString,
			},
			want: false,
		},
		/**************************** version gte ****************************/
		{
			// version >= '3.2.2'
			name: "test version true",
			args: args{attributes: map[string]any{"client_version": "4.0.0"}},
			filter: &Filter{
				Key:   "client_version",
				Op:    GreaterThanOrEqual,
				Value: "3.2.2",
				Type:  StringVersion,
			},
			want: true,
		},
		{
			// version >= '3.2.2'
			name: "test version true",
			args: args{attributes: map[string]any{"client_version": "3.3.0"}},
			filter: &Filter{
				Key:   "client_version",
				Op:    GreaterThanOrEqual,
				Value: "3.2.2",
				Type:  StringVersion,
			},
			want: true,
		},
		{
			// version >= '3.2.2'
			name: "test version true",
			args: args{attributes: map[string]any{"client_version": "3.2.3"}},
			filter: &Filter{
				Key:   "client_version",
				Op:    GreaterThanOrEqual,
				Value: "3.2.2",
				Type:  StringVersion,
			},
			want: true,
		},
		{
			// version >= '3.2.2'
			name: "test version true",
			args: args{attributes: map[string]any{"client_version": "10.0.0"}},
			filter: &Filter{
				Key:   "client_version",
				Op:    GreaterThanOrEqual,
				Value: "3.2.2",
				Type:  StringVersion,
			},
			want: true,
		},
		{
			// version >= '3.2.2'
			name: "test version true",
			args: args{attributes: map[string]any{"client_version": "3.10.0"}},
			filter: &Filter{
				Key:   "client_version",
				Op:    GreaterThanOrEqual,
				Value: "3.2.2",
				Type:  StringVersion,
			},
			want: true,
		},
		{
			// version >= '3.2.2'
			name: "test version true",
			args: args{attributes: map[string]any{"client_version": "3.2.10"}},
			filter: &Filter{
				Key:   "client_version",
				Op:    GreaterThanOrEqual,
				Value: "3.2.2",
				Type:  StringVersion,
			},
			want: true,
		},
		{
			// version >= '3.2.2'
			name: "test version true",
			args: args{attributes: map[string]any{"client_version": "3.2.3"}},
			filter: &Filter{
				Key:   "client_version",
				Op:    GreaterThanOrEqual,
				Value: "3.2.2",
				Type:  StringVersion,
			},
			want: true,
		},
		{
			// version >= '3.2.2'
			name: "test version false",
			args: args{attributes: map[string]any{"client_version": "3.2.0"}},
			filter: &Filter{
				Key:   "client_version",
				Op:    GreaterThanOrEqual,
				Value: "3.2.2",
				Type:  StringVersion,
			},
			want: false,
		},
		{
			// version >= 'v22'
			name: "test version true",
			args: args{attributes: map[string]any{"client_version": "v30"}},
			filter: &Filter{
				Key:   "client_version",
				Op:    GreaterThanOrEqual,
				Value: "v22",
				Type:  StringVersion,
			},
			want: true,
		},
		{
			// version >= 'v22'
			name: "test version false",
			args: args{attributes: map[string]any{"client_version": "v20"}},
			filter: &Filter{
				Key:   "client_version",
				Op:    GreaterThanOrEqual,
				Value: "v22",
				Type:  StringVersion,
			},
			want: false,
		},
		{
			// version >= 'v22'
			name: "test version false",
			args: args{attributes: map[string]any{"client_version": "v20.1"}},
			filter: &Filter{
				Key:   "client_version",
				Op:    GreaterThanOrEqual,
				Value: "v22",
				Type:  StringVersion,
			},
			want: false,
		},
		{
			// version >= 'v22'
			name: "test version true",
			args: args{attributes: map[string]any{"client_version": "v22.1"}},
			filter: &Filter{
				Key:   "client_version",
				Op:    GreaterThanOrEqual,
				Value: "v22",
				Type:  StringVersion,
			},
			want: true,
		},

		{
			// country in ("us", "cn") and version >= '3.2.2' and sdkVersion >= '5.8.1'
			name: "test filter10 true",
			args: args{attributes: map[string]any{"client_version": "3.2.0"}},
			filter: &Filter{
				Key:   "client_version",
				Op:    LessThanOrEqual,
				Value: "3.2.2",
				Type:  StringVersion,
			},
			want: true,
		},
		// version
		{
			// version >= v66
			name: "test version false",
			args: args{attributes: map[string]any{"country": "us", "client_version": "v20", "sdkVersion": "5.8.0"}},
			filter: &Filter{
				Key:   "client_version",
				Op:    GreaterThanOrEqual,
				Value: "v66",
				Type:  StringVersion,
			},
			want: false,
		},
		{
			// version >= v66
			name: "test version true",
			args: args{attributes: map[string]any{"client_version": "v80"}},
			filter: &Filter{
				Key:   "client_version",
				Op:    GreaterThanOrEqual,
				Value: "v66",
				Type:  StringVersion,
			},
			want: true,
		},

		// filter10
		{
			//country in ("us", "cn") and version >= '3.2.2' and sdkVersion >= '5.8.1'
			name:   "test filter10 true",
			args:   args{attributes: map[string]any{"country": "us", "client_version": "3.2.2", "sdkVersion": "5.8.1", "manufacturers": "1"}},
			filter: filter10,
			want:   true,
		},
		{
			//country in ("us", "cn") and version >= '3.2.2' and sdkVersion >= '5.8.1'
			name:   "test filter10 false",
			args:   args{attributes: map[string]any{"country": "u1s", "client_version": "3.2.3", "sdkVersion": "5.8.1"}},
			filter: filter10,
			want:   false,
		},
		{
			//country in ("us", "cn") and version >= '3.2.2' and sdkVersion >= '5.8.1'
			name:   "test filter10 false",
			args:   args{attributes: map[string]any{"country": "us", "client_version": "3.2.1", "sdkVersion": "5.8.1"}},
			filter: filter10,
			want:   false,
		},
		{
			//country in ("us", "cn") and version >= '3.2.2' and sdkVersion >= '5.8.1'
			name:   "test filter10 false",
			args:   args{attributes: map[string]any{"country": "us", "client_version": "3.2.3", "sdkVersion": "5.8.0"}},
			filter: filter10,
			want:   false,
		},

		/*********************************** 二层 ***********************************/
		//	 filter2_0
		{
			// (country in(us, cn) and version >= 3.2.2) or (version >= 3.2.2 and sdkVersion >= 5.8.1)
			name:   "test filter2_0 true",
			args:   args{attributes: map[string]any{"country": "us", "client_version": "3.2.2", "sdkVersion": "5.8.1"}},
			filter: filter20,
			want:   true,
		},
		{
			// (country in(us, cn) and version >= 3.2.2) or (version >= 3.2.2 and sdkVersion >= 5.8.1)
			name:   "test filter2_0 false",
			args:   args{attributes: map[string]any{"country": "u1s", "client_version": "3.2.0", "sdkVersion": "5.8.1"}},
			filter: filter20,
			want:   false,
		},
		{
			// (country in(us, cn) and version >= 3.2.2) or (version >= 3.2.2 and sdkVersion >= 5.8.1)
			name:   "test filter2_0 true",
			args:   args{attributes: map[string]any{"country": "us", "client_version": "3.2.3", "sdkVersion": "5.8.0"}},
			filter: filter20,
			want:   true,
		},
		{
			// (country in(us, cn) and version >= 3.2.2) or (version >= 3.2.2 and sdkVersion >= 5.8.1)
			name:   "test filter2_0 true",
			args:   args{attributes: map[string]any{"country": "u1s", "client_version": "3.2.3", "sdkVersion": "5.8.2"}},
			filter: filter20,
			want:   true,
		},

		//	filter21
		{
			// （country in ("us", "cn") or version >= '3.2.2'） and （version >= 3.2.2 or sdkVersion >= '5.8.1'）
			name:   "test filter21 false",
			args:   args{attributes: map[string]any{"country": "uk", "client_version": "3.2.0", "sdkVersion": "5.8.0"}},
			filter: filter21,
			want:   false,
		},
		{
			// （country in ("us", "cn") or version >= '3.2.2'） and （version >= 3.2.2 or sdkVersion >= '5.8.1'）
			name:   "test filter21 true country & sdkVersion",
			args:   args{attributes: map[string]any{"country": "us", "client_version": "3.2.0", "sdkVersion": "5.8.1"}},
			filter: filter21,
			want:   true,
		},
		{
			// （country in ("us", "cn") or version >= '3.2.2'） and （version >= 3.2.2 or sdkVersion >= '5.8.1'）
			name:   "test filter21 false",
			args:   args{attributes: map[string]any{"country": "us", "client_version": "3.2.0", "sdkVersion": "5.8.0"}},
			filter: filter21,
			want:   false,
		},

		/*********************************** 三层 ***********************************/
		{
			//（country in ("us", "cn") and client_version >= '3.2.2' and sdkVersion >= '5.8.1'） or (trueKey >= 1)
			name:   "test trueKey true",
			args:   args{attributes: map[string]any{"client_version": "v80", "trueKey": 2}},
			filter: filter30,
			want:   true,
		},
		{
			//（country in ("us", "cn") and client_version >= '3.2.2' and sdkVersion >= '5.8.1'） or (trueKey >= 1)
			name:   "test trueKey true",
			args:   args{attributes: map[string]any{"country": "cn", "client_version": "3.2.2", "sdkVersion": "5.8.2", "trueKey": 0}},
			filter: filter30,
			want:   false,
		},
		{
			//（country in ("us", "cn") and client_version >= '3.2.2' and sdkVersion >= '5.8.1'） or (trueKey >= 1)
			name:   "test trueKey false",
			args:   args{attributes: map[string]any{"country": "cn", "client_version": "3.2.2", "sdkVersion": "5.8.0", "trueKey": 0}},
			filter: filter30,
			want:   false,
		},

		{
			name: "test true",
			args: args{attributes: map[string]interface{}{"game_version": "3.2.9", "sdk_version": "5.8.2"}},
			filter: &Filter{
				Logic: "&&",
				Conditions: []*Filter{
					{
						Key:   "game_version",
						Op:    ">=",
						Type:  "string",
						Value: "3.2.6",
					},
					{
						Key:   "sdk_version",
						Op:    ">=",
						Type:  "string",
						Value: "5.8.1",
					},
				},
			},
			want: true,
		},
		{
			name: "test country",
			args: args{attributes: map[string]interface{}{"country": "cn"}},
			//{"op": "in", "key": "country", "type": "list.string", "value": ["kr", "tw", "jp", "cn", "gb"]}
			filter: countryFilter,
			want:   true,
		},
		{
			name:   "test country",
			args:   args{attributes: map[string]interface{}{"country": []string{"kr", "tw", "jp", "cn", "gb"}}},
			filter: countryFilter,
			want:   false,
		},

		{
			// 多个值的情况，包含
			name: "test multi values contain",
			args: args{attributes: map[string]any{"country": []string{"gb", "us"}}},
			filter: &Filter{
				Key:   "country",
				Op:    Contain,
				Value: []string{"", "us", "gb", "ph", "de"},
				Type:  ListString,
			},
			want: false,
		},
		{
			// 多个值的情况，包含
			name: "test multi values contain",
			args: args{attributes: map[string]any{"country": []string{"", "us", "gb", "ph", "de"}}},
			filter: &Filter{
				Key:   "country",
				Op:    Contain,
				Value: []string{"", "us", "gb"},
				Type:  ListString,
			},
			want: true,
		},
		{
			// 只要有一个值在条件切片中，就不满足"不包含"
			name: "test multi values not contain",
			args: args{attributes: map[string]any{"country": []string{"", "us", "gb", "ph", "de"}}},
			filter: &Filter{
				Key:   "country",
				Op:    NotContain,
				Value: []string{"gb", "us"},
				Type:  ListString,
			},
			want: false,
		},
		{
			// 只要有一个值在条件切片中，就不满足"不包含"
			name: "test multi values not contain",
			args: args{attributes: map[string]any{"country": []string{"", "us", "gb", "ph", "de"}}},
			filter: &Filter{
				Key:   "country",
				Op:    NotContain,
				Value: []string{"gb1", "us1"},
				Type:  ListString,
			},
			want: true,
		},
		{
			//  包含任一
			name: "test multi values contain any",
			args: args{attributes: map[string]any{"country": []string{"", "us", "gb", "ph", "de"}}},
			filter: &Filter{
				Key:   "country",
				Op:    ContainAny,
				Value: []string{"gb1", "us"},
				Type:  ListString,
			},
			want: true,
		},
		{
			//  包含任一
			name: "test multi values contain any",
			args: args{attributes: map[string]any{"country": []string{"", "us", "gb", "ph", "de"}}},
			filter: &Filter{
				Key:   "country",
				Op:    ContainAny,
				Value: []string{"gb1", "us1"},
				Type:  ListString,
			},
			want: false,
		},
		{
			//  排除全部
			name: "test multi values exclude all",
			args: args{attributes: map[string]any{"country": []string{"", "us", "gb", "ph", "de"}}},
			filter: &Filter{
				Key:   "country",
				Op:    ExcludeAll,
				Value: []string{"gb", "us1"},
				Type:  ListString,
			},
			want: false,
		},
		{
			//  排除全部
			name: "test multi values exclude all",
			args: args{attributes: map[string]any{"country": []string{"", "us", "gb", "ph", "de"}}},
			filter: &Filter{
				Key:   "country",
				Op:    ExcludeAll,
				Value: []string{"gb1", "us1"},
				Type:  ListString,
			},
			want: true,
		},
		{
			//  contain
			name: "test multi values contain with interface type",
			args: args{attributes: map[string]any{"active_type": 1, "app_version": "3.4.6", "exp_days": 0, "exp_joined": []int{837, 942, 837, 942, 1047}, "groups_joined": []int{1692, 2212, 1692, 2212, 2524}, "history_exp_ids": []int{837, 942, 837, 942, 1047}, "history_grp_ids": []int{1692, 2212, 1692, 2212, 2524}, "running_exp_ids": []int{942, 837, 1047}, "running_grp_ids": []int{2212, 1692, 2524}}},
			//{"conditions":[{"key":"history_exp_ids","op":"contain","type":"list.int","value":["837"]},{"key":"history_grp_ids","op":"contain","type":"list.int","value":["1692"]}],"logic":"and"}
			filter: containFilter1,
			want:   true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.filter.EvaluateFilter(tt.args.attributes); got != tt.want {
				t.Errorf("EvaluateFilter() = %v, want %v", got, tt.want)
			}
		})
	}
}
