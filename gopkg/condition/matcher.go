package condition

import (
	"reflect"
	"strings"
	"time"

	"github.com/Masterminds/semver/v3"
	"github.com/spf13/cast"
)

type MatcherFn func(attrValue interface{}, condValue interface{}, valueType ValueType) bool

func MathFunc(op OpType) MatcherFn {
	switch op {
	case Equal, EqualSymbol, EqualAssignment:
		return equal
	case NotEqual, NotEqualSymbol:
		return notEqual
	case GreaterThan, GreaterThanSymbol:
		return greaterThan
	case GreaterThanOrEqual, GreaterThanOrEqualSymbol:
		return greaterEqual
	case LessThan, LessThanSymbol:
		return lessThan
	case LessThanOrEqual, LessThanOrEqualSymbol:
		return lessEqual
	case IN, InSymbol:
		return in
	case NotIn, NotInSymbol:
		return notIn
	case IsNull:
		return isNull
	case IsNotNull:
		return notNull
	case Contain, ContainSymbol:
		return contain
	case NotContain, NotContainSymbol:
		return notContain
	case ContainAny:
		return containAny
	case ExcludeAll:
		return excludeAll

	default:
		return alwaysFalse
	}
}

func isNull(attrValue interface{}, _ interface{}, _ ValueType) bool {
	return attrValue == nil
}

func notNull(attrValue interface{}, _ interface{}, _ ValueType) bool {
	return attrValue != nil
}

func alwaysFalse(_ interface{}, _ interface{}, _ ValueType) bool {
	return false
}

func greaterThan(attrValue interface{}, condValue interface{}, valueType ValueType) bool {
	switch attrValue.(type) {
	case float64:
		attrValueFloat64, condValueFloat64 := attrValue.(float64), condValue.(float64)
		return attrValueFloat64 > condValueFloat64
	case string:
		attrValueString, condValueString := attrValue.(string), condValue.(string)
		if valueType == StringVersion {
			attrVersion, err := semver.NewVersion(attrValueString)
			if err != nil {
				return false
			}
			condVersion, err := semver.NewVersion(condValueString)
			if err != nil {
				return false
			}
			return attrVersion.GreaterThan(condVersion)
		} else {
			// 尝试解析为时间
			if attrTime, err := time.Parse(time.RFC3339, attrValueString); err == nil {
				if condTime, err := time.Parse(time.RFC3339, condValueString); err == nil {
					return attrTime.After(condTime)
				}
			}
			// 普通字符串比较
			return attrValueString > condValueString
		}
	}
	return false
}

func greaterEqual(attrValue interface{}, condValue interface{}, valueType ValueType) bool {
	return greaterThan(attrValue, condValue, valueType) || equal(attrValue, condValue, valueType)
}

func lessThan(attrValue interface{}, condValue interface{}, valueType ValueType) bool {
	switch attrValue.(type) {
	case float64:
		attrValueFloat64, condValueFloat64 := attrValue.(float64), condValue.(float64)
		return attrValueFloat64 < condValueFloat64
	case string:
		attrValueString, condValueString := attrValue.(string), condValue.(string)
		if valueType == StringVersion {
			attrVersion, err := semver.NewVersion(attrValueString)
			if err != nil {
				return false
			}
			condVersion, err := semver.NewVersion(condValueString)
			if err != nil {
				return false
			}
			return attrVersion.LessThan(condVersion)
		} else {
			// 尝试解析为时间
			if attrTime, err := time.Parse(time.RFC3339, attrValueString); err == nil {
				if condTime, err := time.Parse(time.RFC3339, condValueString); err == nil {
					return attrTime.Before(condTime)
				}
			}
			// 普通字符串比较，区分大小写
			return attrValueString < condValueString
		}
	}
	return false
}

func lessEqual(attrValue interface{}, condValue interface{}, valueType ValueType) bool {
	return lessThan(attrValue, condValue, valueType) || equal(attrValue, condValue, valueType)
}

func equal(attrValue interface{}, condValue interface{}, valueType ValueType) bool {
	// 处理 nil 情况
	if attrValue == nil && condValue == nil {
		return true
	}
	if attrValue == nil || condValue == nil {
		return false
	}

	switch condValue := condValue.(type) {
	case float64, int, int32, int64, uint, uint32, uint64, float32:
		condValueFloat, err1 := cast.ToFloat64E(condValue)
		attrValueFloat, err2 := cast.ToFloat64E(attrValue)
		if err1 == nil && err2 == nil {
			return attrValueFloat == condValueFloat
		}
		return false

	case string:
		switch attrValue := attrValue.(type) {
		case float64, int, int32, int64, uint, uint32, uint64, float32:
			condValueStr, err1 := cast.ToStringE(condValue)
			attrValueStr, err2 := cast.ToStringE(attrValue)
			if err1 == nil && err2 == nil {
				return attrValueStr == condValueStr
			}
			return false
		case string:
			if valueType == StringVersion {
				condVersion, err1 := semver.NewVersion(condValue)
				attrVersion, err2 := semver.NewVersion(attrValue)
				if err1 == nil && err2 == nil {
					return attrVersion.Equal(condVersion)
				}
				return false
			}
			return attrValue == condValue
		default:
			return attrValue == cast.ToString(condValue)
		}

	case bool:
		condValueBool, err1 := cast.ToBoolE(condValue)
		attrValueBool, err2 := cast.ToBoolE(attrValue)
		if err1 == nil && err2 == nil {
			return attrValueBool == condValueBool
		}
		return false

	case []interface{}:
		if attrValueSlice, ok := attrValue.([]interface{}); ok {
			return compareSlices(attrValueSlice, condValue, valueType)
		}

	case map[string]interface{}:
		if attrValueMap, ok := attrValue.(map[string]interface{}); ok {
			return compareMaps(attrValueMap, condValue, valueType)
		}

	case time.Time:
		if attrValueTime, ok := attrValue.(time.Time); ok {
			return condValue.Equal(attrValueTime)
		}
	}

	// 其他情况使用 reflect.DeepEqual
	return reflect.DeepEqual(attrValue, condValue)
}

func notEqual(attrValue interface{}, condValue interface{}, valueType ValueType) bool {
	return !equal(attrValue, condValue, valueType)
}

func in(attrValue interface{}, condValue interface{}, valueType ValueType) bool {
	// 如果属性值为nil，则不在任何集合中，如果条件值为nil，则没有任何元素可比较
	if attrValue == nil || condValue == nil {
		return false
	}

	switch condValue := condValue.(type) {
	case []interface{}: // 条件值是切片
		// 如果属性值是字符串，需要特殊处理
		if attrStr, ok := attrValue.(string); ok {
			for _, v := range condValue {
				if cvStr, ok := v.(string); ok {
					if strings.Contains(cvStr, attrStr) {
						return true
					}
				} else if equal(attrValue, v, valueType) {
					return true
				}
			}
			return false
		}

		// 非字符串类型的普通比较
		for _, cv := range condValue {
			if equal(attrValue, cv, valueType) {
				return true
			}
		}
		return false

	case map[string]interface{}: // 条件值是map
		// 如果属性值是字符串，需要特殊处理
		if attrStr, ok := attrValue.(string); ok {
			for _, v := range condValue {
				if cvStr, ok := v.(string); ok {
					if strings.Contains(cvStr, attrStr) {
						return true
					}
				} else if equal(attrStr, v, valueType) {
					return true
				}
			}
			return false
		}

		// 非字符串类型的普通比较
		for _, cv := range condValue {
			if equal(attrValue, cv, valueType) {
				return true
			}
		}
		return false

	case string:
		// 如果属性值是字符串，使用strings.Contains
		if attrStr, ok := attrValue.(string); ok {
			return strings.Contains(condValue, attrStr)
		}
		return equal(attrValue, condValue, valueType)

	default:
		// 直接比较相等性
		return equal(attrValue, condValue, valueType)
	}
}

func notIn(attrValue interface{}, condValue interface{}, valueType ValueType) bool {
	return !in(attrValue, condValue, valueType)
}

// contain
// attrValue := []interface{}{1, 2, 3, 4}
// condValue := []interface{}{2, 3}
// true，因为[2,3]被[1,2,3,4]包含
// contain 包含任一
func contain(attrValue interface{}, condValue interface{}, valueType ValueType) bool {
	// 如果属性值为nil，则不包含任何条件值
	if attrValue == nil {
		return false
	}

	switch attrValue := attrValue.(type) {
	case []interface{}: // 属性值是切片
		switch condValue := condValue.(type) {
		case []interface{}: // 条件值是切片
			// 检查条件切片中的每个值是否都在属性切片中
			for _, cv := range condValue {
				found := false
				for _, av := range attrValue {
					if equal(av, cv, valueType) {
						found = true
						break
					}
				}
				if !found {
					return false
				}
			}
			return true
		case map[string]interface{}: // 条件值是map
			// 检查条件map中的每个值是否都在属性切片中
			for _, cv := range condValue {
				found := false
				for _, av := range attrValue {
					if equal(av, cv, valueType) {
						found = true
						break
					}
				}
				if !found {
					return false
				}
			}
			return true
		default:
			// 条件值是单个值，检查是否在属性切片中
			for _, av := range attrValue {
				if equal(av, condValue, valueType) {
					return true
				}
			}
			return false
		}

	case string: // 属性值是字符串
		switch condValue := condValue.(type) {
		case []interface{}: // 条件值是切片
			// 检查切片中的每个字符串是否都被属性字符串包含
			for _, cv := range condValue {
				if cvStr, ok := cv.(string); !ok || !strings.Contains(cvStr, attrValue) {
					return false
				}
			}
			return true
		case string: // 条件值是字符串
			return strings.Contains(condValue, attrValue)
		default:
			return false
		}

	case map[string]interface{}: // 属性值是map
		switch condValue := condValue.(type) {
		case map[string]interface{}: // 条件值是map
			// 检查条件map的所有键值对是否都在属性map中且值相等
			for k, cv := range condValue {
				if av, exists := attrValue[k]; !exists || !equal(av, cv, valueType) {
					return false
				}
			}
			return true
		case []interface{}: // 条件值是切片
			// 检查切片中的每个值是否都在map的值中
			for _, cv := range condValue {
				found := false
				for _, av := range attrValue {
					if equal(av, cv, valueType) {
						found = true
						break
					}
				}
				if !found {
					return false
				}
			}
			return true
		default:
			// 检查单个值是否在map的值中
			for _, av := range attrValue {
				if equal(av, condValue, valueType) {
					return true
				}
			}
			return false
		}

	default:
		// 其他类型直接进行相等比较
		return equal(attrValue, condValue, valueType)
	}
}

func notContain(attrValue interface{}, condValue interface{}, valueType ValueType) bool {
	return !contain(attrValue, condValue, valueType)
}

// 检查是否包含任一元素（只要有一个匹配就返回true）
func containAny(attrValue interface{}, condValue interface{}, valueType ValueType) bool {
	// 如果属性值为nil,则不包含任何条件值
	if attrValue == nil {
		return false
	}

	switch attrValue := attrValue.(type) {
	case []interface{}: // 属性值是切片
		switch condValue := condValue.(type) {
		case []interface{}: // 条件值是切片
			// 只要属性切片中有一个值在条件切片中即可
			for _, av := range attrValue {
				for _, cv := range condValue {
					if equal(av, cv, valueType) {
						return true
					}
				}
			}
			return false
		case map[string]interface{}: // 条件值是map
			// 只要属性切片中有一个值匹配map中的某个值即可
			for _, av := range attrValue {
				for _, cv := range condValue {
					if equal(av, cv, valueType) {
						return true
					}
				}
			}
			return false
		default:
			// 条件值是单个值，检查是否在切片中
			for _, av := range attrValue {
				if equal(av, condValue, valueType) {
					return true
				}
			}
			return false
		}

	case string:
		// 字符串类型的处理
		switch condValue := condValue.(type) {
		case []interface{}: // 条件值是切片
			// 检查字符串是否包含在切片中的任意一个字符串中
			for _, cv := range condValue {
				if cvStr, ok := cv.(string); ok && strings.Contains(cvStr, attrValue) {
					return true
				}
			}
			return false
		case string: // 条件值是字符串
			return strings.Contains(condValue, attrValue)
		default:
			return equal(attrValue, condValue, valueType)
		}

	case map[string]interface{}:
		switch condValue := condValue.(type) {
		case map[string]interface{}: // 条件值是map
			// 只要有一个键值对匹配即可
			for k, av := range attrValue {
				if cv, exists := condValue[k]; exists && equal(av, cv, valueType) {
					return true
				}
			}
			return false
		case []interface{}: // 条件值是切片
			// 只要map中有一个值在切片中即可
			for _, av := range attrValue {
				for _, cv := range condValue {
					if equal(av, cv, valueType) {
						return true
					}
				}
			}
			return false
		default:
			// 检查条件值是否匹配map中的任何值
			for _, av := range attrValue {
				if equal(av, condValue, valueType) {
					return true
				}
			}
			return false
		}

	default:
		// 其他类型直接进行相等比较
		return equal(attrValue, condValue, valueType)
	}
}

// excludeAll 检查是否排除所有元素（必须全部都不匹配）
func excludeAll(attrValue interface{}, condValue interface{}, valueType ValueType) bool {
	// 如果属性值为nil,则认为排除所有
	if attrValue == nil {
		return true
	}

	switch attrValue := attrValue.(type) {
	case []interface{}: // 属性值是切片
		switch condValue := condValue.(type) {
		case []interface{}: // 条件值是切片
			// 属性切片中的所有值都不在条件切片中
			for _, av := range attrValue {
				for _, cv := range condValue {
					if equal(av, cv, valueType) {
						return false
					}
				}
			}
			return true
		case map[string]interface{}: // 条件值是map
			// 属性切片中的所有值都不在map的值中
			for _, av := range attrValue {
				for _, cv := range condValue {
					if equal(av, cv, valueType) {
						return false
					}
				}
			}
			return true
		default:
			// 条件值是单个值，检查切片中是否都不等于该值
			for _, av := range attrValue {
				if equal(av, condValue, valueType) {
					return false
				}
			}
			return true
		}

	case string:
		switch condValue := condValue.(type) {
		case []interface{}: // 条件值是切片
			// 字符串不在任何切片元素中
			for _, cv := range condValue {
				if cvStr, ok := cv.(string); ok && strings.Contains(cvStr, attrValue) {
					return false
				}
			}
			return true
		case string: // 条件值是字符串
			return !strings.Contains(condValue, attrValue)
		default:
			return true
		}

	case map[string]interface{}:
		switch condValue := condValue.(type) {
		case map[string]interface{}: // 条件值是map
			// map中的所有值都不在条件map中
			for k, av := range attrValue {
				if cv, exists := condValue[k]; exists && equal(av, cv, valueType) {
					return false
				}
			}
			return true
		case []interface{}: // 条件值是切片
			// map中的所有值都不在切片中
			for _, av := range attrValue {
				for _, cv := range condValue {
					if equal(av, cv, valueType) {
						return false
					}
				}
			}
			return true
		default:
			// map中的所有值都不等于条件值
			for _, av := range attrValue {
				if equal(av, condValue, valueType) {
					return false
				}
			}
			return true
		}

	default:
		// 其他类型直接进行不相等比较
		return !equal(attrValue, condValue, valueType)
	}
}

// 比较两个切片是否相等
func compareSlices(a, b []interface{}, valueType ValueType) bool {
	if len(a) != len(b) {
		return false
	}
	for i := range a {
		if !equal(a[i], b[i], valueType) {
			return false
		}
	}
	return true
}

// 比较两个 map 是否相等
func compareMaps(a, b map[string]interface{}, valueType ValueType) bool {
	if len(a) != len(b) {
		return false
	}
	for k, va := range a {
		vb, exists := b[k]
		if !exists {
			return false
		}
		if !equal(va, vb, valueType) {
			return false
		}
	}
	return true
}
