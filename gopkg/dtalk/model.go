package dtalk

import (
	"strings"
)

type Sendable interface {
	Marshaler() []byte
}

type OutGoingModel struct {
	AtUsers []struct {
		DingtalkID string `json:"dingtalkId"`
	} `json:"atUsers"`
	ChatbotUserID             string `json:"chatbotUserId"`
	ConversationID            string `json:"conversationId"`
	ConversationTitle         string `json:"conversationTitle"`
	ConversationType          string `json:"conversationType"`
	CreateAt                  int64  `json:"createAt"`
	IsAdmin                   bool   `json:"isAdmin"`
	IsInAtList                bool   `json:"isInAtList"`
	MsgID                     string `json:"msgId"`
	Msgtype                   string `json:"msgtype"`
	SceneGroupCode            string `json:"sceneGroupCode"`
	SenderID                  string `json:"senderId"`
	SenderNick                string `json:"senderNick"`
	SessionWebhook            string `json:"sessionWebhook"`
	SessionWebhookExpiredTime int64  `json:"sessionWebhookExpiredTime"`
	Text                      struct {
		Content string `json:"content"`
	} `json:"text"`
}

func (o *OutGoingModel) CMD() *OutGoingCmd {
	keyAndArgs := strings.Split(strings.TrimSpace(o.Text.Content), " ")
	cmdName := strings.ToLower(keyAndArgs[0])
	return &OutGoingCmd{
		RAW:    o.Text.Content,
		Prefix: cmdName,
		Args:   keyAndArgs[1:],
	}
}

type OutGoingCmd struct {
	RAW    string
	Prefix string
	Args   []string
}
