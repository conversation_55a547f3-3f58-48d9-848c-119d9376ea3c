package sso

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"time"

	"git.7k7k.com/data/abAdmin/gopkg/jwts"
	"git.7k7k.com/data/abAdmin/httpd/base"
	"git.7k7k.com/data/abAdmin/infra/auth"
	"git.7k7k.com/data/abAdmin/infra/config"
	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/repository"
	"github.com/Kretech/xgo/dump"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

const (
	// SSOVerifyTicketURL SSO 验证 ticket 的 URL
	SSOVerifyTicketURL = "https://sso.youxi123.com/api/users/sso/verify_ticket"
)

// SSOUserInfo SSO 用户信息
type SSOUserInfo struct {
	UserID      string     `json:"user_id"`
	UserID2     string     `json:"userid"` // 兼容字段
	Email       string     `json:"email"`
	IsActive    bool       `json:"is_active"`
	IsSuperuser bool       `json:"is_superuser"`
	FullName    string     `json:"full_name"`
	Workcode    string     `json:"workcode"`
	Avatar      string     `json:"avatar"`
	DeptInfo    []DeptInfo `json:"dept_info"`
	Username    string     `json:"username"`
	Attribute   Attribute  `json:"attribute"`
	RequestID   string     `json:"request_id"`
}

// DeptInfo 部门信息
type DeptInfo struct {
	ID   int64  `json:"id"`
	Name string `json:"name"`
}

// Attribute 用户额外属性
type Attribute struct {
	GrafanaRole string `json:"grafana_role"`
}

// SSOErrorResponse SSO 错误响应
type SSOErrorResponse struct {
	Detail    string `json:"detail"`
	RequestID string `json:"request_id"`
}

// VerifyTicketRequest 验证 ticket 请求
type VerifyTicketRequest struct {
	Ticket string `json:"ticket"`
}

// SSOService SSO 服务
type SSOService struct {
	DB        *gorm.DB // 数据库连接
	SecretKey string   // SSO 密钥
}

// @autowire(set=infra)
// NewSSOService 创建 SSO 服务
func NewSSOService(c *config.Config, db *repository.QueryForBIv1) *SSOService {
	dump.Dump(c.JWT)
	return &SSOService{
		DB:        db.User.UnderlyingDB(),
		SecretKey: c.JWT.SSOSecret,
	}
}

// GetUserInfoByTicket 验证 ticket 并获取用户信息
func (s *SSOService) GetUserInfoByTicket(ticket string) (*SSOUserInfo, error) {
	// 构造请求体和密钥
	type ticketRequest struct {
		Ticket    string `json:"ticket"`
		SecretKey string `json:"secret_key"`
	}

	reqBody := ticketRequest{
		Ticket:    ticket,
		SecretKey: s.SecretKey,
	}

	// 将请求体转换为 JSON
	reqJSON, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("marshal request body failed: %w", err)
	}

	// 创建 HTTP 请求
	req, err := http.NewRequest("POST", SSOVerifyTicketURL, bytes.NewBuffer(reqJSON))
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{
		Timeout: 10 * time.Second,
	}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("send request failed: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response body failed: %w", err)
	}

	// 处理非 200 状态码
	if resp.StatusCode != http.StatusOK {
		var errResp SSOErrorResponse
		if err := json.Unmarshal(respBody, &errResp); err != nil {
			return nil, fmt.Errorf("unmarshal error response failed: %w, status code: %d, body: %s", err, resp.StatusCode, string(respBody))
		}
		return nil, fmt.Errorf("verify ticket failed: %s, request_id: %s", errResp.Detail, errResp.RequestID)
	}

	// 解析用户信息
	var userInfo SSOUserInfo
	if err := json.Unmarshal(respBody, &userInfo); err != nil {
		return nil, fmt.Errorf("unmarshal user info failed: %w", err)
	}

	return &userInfo, nil
}

// MatchUserByEmail 匹配用户，优先使用 workcode，其次使用 email 前缀匹配 account
func (s *SSOService) MatchUserByEmail(ctx context.Context, userInfo *SSOUserInfo) (*model.User, error) {
	if userInfo == nil {
		return nil, errors.New("user info is nil")
	}

	var user model.User

	// 1. 优先使用 workcode 匹配
	if userInfo.Workcode != "" {
		result := s.DB.Where("workcode = ?", userInfo.Workcode).First(&user)
		if result.Error == nil {
			return &user, nil
		}
		// workcode 匹配失败，继续尝试其他方式
		slog.InfoContext(ctx, "根据 workcode 匹配用户失败，尝试其他方式", "workcode", userInfo.Workcode)
	}

	// 2. 尝试使用 email 匹配
	result := s.DB.Where("email = ?", userInfo.Email).First(&user)
	if result.Error == nil {
		return &user, nil
	}

	// 3. 尝试使用 email 前缀匹配 account
	// 获取 email 的前缀部分（@ 符号之前的部分）
	emailPrefix := userInfo.Email
	if idx := bytes.IndexByte([]byte(userInfo.Email), '@'); idx > 0 {
		emailPrefix = userInfo.Email[:idx]
	}

	result = s.DB.Where("account = ?", emailPrefix).First(&user)
	if result.Error != nil {
		return nil, fmt.Errorf("无法匹配用户，尝试了 workcode、email 和 email 前缀匹配 account 均失败: %w", result.Error)
	}

	return &user, nil
}

// VerifyTicketPostHandler 验证 ticket 并获取用户信息的 POST 处理器
func (s *SSOService) VerifyTicketPostHandler(c *gin.Context) {
	// 解析请求体
	var req VerifyTicketRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		slog.ErrorContext(c, "bind request body failed", "error", err)
		base.WriteErrMsg(c, http.StatusBadRequest, "请求体无效")
		return
	}

	// 验证请求参数
	if req.Ticket == "" {
		slog.ErrorContext(c, "empty ticket")
		base.WriteErrMsg(c, http.StatusBadRequest, "ticket 为空")
		return
	}

	// 记录请求信息
	slog.InfoContext(c, "verify ticket POST request", "ticket", req.Ticket, "user_id", auth.GetUserId(c))

	// 验证 ticket
	userInfo, err := s.GetUserInfoByTicket(req.Ticket)
	if err != nil {
		slog.ErrorContext(c, "verify ticket failed", "error", err)
		base.WriteErrMsg(c, http.StatusUnauthorized, "验证 ticket 失败："+err.Error())
		return
	}

	// 根据邮箱匹配用户
	user, err := s.MatchUserByEmail(c, userInfo)
	if err != nil {
		slog.ErrorContext(c, "match user by email failed", "error", err)
		base.WriteErrMsg(c, http.StatusInternalServerError, "匹配用户失败（尝试规则：工号、邮箱前缀）")
		return
	}

	// 生成 JWT token
	token, err := jwts.GenerateToken(int(user.ID), userInfo.IsSuperuser)
	if err != nil {
		slog.ErrorContext(c, "generate token failed", "error", err)
		base.WriteErrMsg(c, http.StatusInternalServerError, "生成 token 失败")
		return
	}

	// 设置用户上下文
	c.Set(auth.ContextKeyUserID, int(user.ID))

	// 返回用户信息和 token
	base.WriteOk(c, gin.H{
		"user": gin.H{
			"id":       user.ID,
			"account":  user.Account,
			"realname": user.Realname,
			"email":    user.Email,
			"status":   user.Status,
		},
		"token": token,
	})
}

// RegisterRoutes 注册路由
func (s *SSOService) RegisterRoutes(router gin.IRouter) {
	router.POST("/sso/verify_ticket", s.VerifyTicketPostHandler)
}
