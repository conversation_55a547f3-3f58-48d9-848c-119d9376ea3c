package etrack

import (
	"context"
	"fmt"
	"log/slog"
)

func Catch(ctx context.Context, name string, err error, kvs ...any) {
	if err == nil {
		return
	}

	kvs = append(kvs, "error", fmt.Sprintf("%+v", err))
	slog.ErrorContext(ctx, name, kvs...)
}

func Fatal(ctx context.Context, name string, err error, kvs ...any) {
	if err == nil {
		return
	}

	Catch(ctx, name, err, kvs...)
	panic(fmt.<PERSON><PERSON><PERSON>("%s error: %w", name, err))
}
