package test

import (
	"testing"

	"git.7k7k.com/data/abAdmin/gopkg/condition"
	"git.7k7k.com/data/abAdmin/infra"
	"git.7k7k.com/data/abAdmin/infra/config"
)

func TestExpLogService_NewExpLog(t *testing.T) {
}

func TestGenerateExpression(t *testing.T) {
	filter10 := &condition.Filter{
		Logic: condition.AND,
		Conditions: []*condition.Filter{
			{
				Key:   "country",
				Op:    condition.IN,
				Value: []string{"us", "cn"},
				Type:  condition.STRING,
			},
			{
				Key:   "client_version",
				Op:    condition.GreaterThanOrEqual,
				Value: "3.2.2",
				Type:  condition.STRING,
			},
			{
				Key:   "sdkVersion",
				Op:    condition.GreaterThanOrEqual,
				Value: "5.8.1",
				Type:  condition.STRING,
			},
		},
	}

	filter20 := &condition.Filter{
		Logic: condition.OR,
		Conditions: []*condition.Filter{
			filter10,
			{
				Key:   "trueKey",
				Op:    condition.GreaterThanOrEqual,
				Type:  condition.INT,
				Value: 1,
			},
			{
				Key:   "k4",
				Op:    condition.Equal,
				Type:  condition.INT,
				Value: 1,
			},
		},
	}

	filter30 := &condition.Filter{
		Logic: condition.AND,
		Conditions: []*condition.Filter{
			filter20,
			{
				Key:   "k3",
				Op:    condition.GreaterThanOrEqual,
				Type:  condition.INT,
				Value: 1,
			},
			{
				Key:   "k4",
				Op:    condition.Equal,
				Type:  condition.INT,
				Value: 1,
			},
		},
	}

	type args struct {
		f *condition.Filter
	}
	tests := []struct {
		name   string
		filter *condition.Filter
		want   string
	}{
		{
			name:   "test filter10 ",
			filter: filter10,
			want:   "(国家 in [us cn] and client_version ge 3.2.2 and sdkVersion ge 5.8.1)",
		},
		{
			// （country in ("us", "cn") and client_version >= '3.2.2' and sdkVersion >= '5.8.1'） or (trueKey >= 1)
			name:   "test filter20 ",
			filter: filter20,
			want:   "((国家 in [us cn] and client_version ge 3.2.2 and sdkVersion ge 5.8.1) or trueKey ge 1 or k4 eq 1)",
		},
		{
			// （country in ("us", "cn") and client_version >= '3.2.2' and sdkVersion >= '5.8.1'） or (trueKey >= 1)
			name:   "test filter30 ",
			filter: filter30,
			want:   "(((国家 in [us cn] and client_version >= 3.2.2 and sdkVersion >= 5.8.1) or trueKey >= 1 or k4 == 1) and k3 >= 1 and k4 == 1)",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// if got := service.GenerateExpression(tt.filter); got != tt.want {
			// 	t.Errorf("GenerateExpression() = %v, want %v", got, tt.want)
			// }
		})
	}
}

func init() {
	config.GlobalConfig = &infra.Config{}
	config.GlobalConfig.AssetPath = "/Users/<USER>/GolandProjects/abAdmin/resource/assets/"
	_ = config.LoadStaticData()
}
