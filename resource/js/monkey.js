// ==UserScript==
// @name         AB实验平台
// @namespace    公司
// @version      2025-03-19
// @description  try to take over the world!
// <AUTHOR>
// @match        https://abtest.youxi123.com/
// @match        http://*************:5001/
// @match        http://*************:5001/
// @grant        none
// ==/UserScript==

(function () {
	'use strict';

	function $$(selector) {
		return document.querySelector(selector);
	}


	let envs = [
		{
			'name': '生产环境！！！',
			'host': 'abtest.youxi123.com'
		},
		{
			'name': '测试环境',
			'host': '*************:5001'
		},
		{
			'name': '联调',
			'host': '*************:5001'
		}
	]

	let env = envs.find(e => location.host.includes(e.host));

	if (!env) {
		return;
	}

	let title = $$('head > title');
	title.innerText += ' - ' + env.name;

	setTimeout(() => {
		let navTitle = $$('#app div.navbar div.left-side div > div:nth-child(2) > span');
		navTitle.innerText += ' - ' + env.name;
	}, 500);
})();