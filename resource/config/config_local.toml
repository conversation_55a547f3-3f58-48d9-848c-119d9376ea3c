addr = ":8001"
mode = "local"
strategy_file = "/resource/assets/strategy.json"

ab_url = "http://106.75.29.167:5001"
bi_url = "http://t.admarket.youxi123.com"

[sentry]
env = "local"
dsn = "http://ec87764e1c8fee6f0822489bf09815eb@106.75.65.198:9000/2"

[jwt]
key = "hello-hungry-abtest"
sso_secret = "oFcsjlRh1xmPr75bgsbrdndjD6s4TzzmSBWdOMDSJdauguzC"
admin = [335,329,387,348,296,550]

[task]
account_id = 515
close_exp_enabled = false
exp_strategy_trigger_enabled = false
close_exp_interval_second = 10
close_exp_days_ago = 7
exp_strategy_trigger_interval_second = 1

[database]
mysql_default = "abtest"

[database.mysql.abtest]
dsn = "xxgame_abtest_slt:CJy9p+lh7s4Wc6PG@tcp(106.75.29.167:23306)/abtest_dev?charset=utf8mb4&parseTime=True&loc=Local&timeout=3s&readTimeout=5s"
debug = true

[database.mysql.ad]
dsn = "root:123456@tcp(106.75.63.174:3306)/ad?charset=utf8mb4&parseTime=True&loc=Local&timeout=3s&readTimeout=5s"
debug = true

[database.redis.admin]
addr = "106.75.65.198:6379"
db = 4

[exp_sync.BI]
api_url = "http://t.admarket.youxi123.com/api/newBi/syn/expConf"
