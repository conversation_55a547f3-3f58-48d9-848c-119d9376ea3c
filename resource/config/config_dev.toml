addr = ":8001"
mode = "dev"
strategy_file = "/resource/assets/strategy.json"

ab_url = "http://106.75.65.198:5001"
bi_url = "http://t.admarket.youxi123.com"

[sentry]
env = "dev"
dsn = "http://ec87764e1c8fee6f0822489bf09815eb@10.9.87.141:9000/2"

[jwt]
key = "hello-hungry-abtest"
sso_secret = "wZ0egdGdssPXstjF86I1ugG8uED0MRspv5AN5Ul3vL9OnW2G"
admin = [335,329,387,348,296,550,137,463,467,725,726,727,728]

[task]
account_id = 515
close_exp_enabled = true
exp_strategy_trigger_enabled = true
close_exp_interval_second = 10
close_exp_days_ago = 7
exp_strategy_trigger_interval_second = 1

[database]
mysql_default = "abtest"

[database.mysql.abtest]
dsn = "xxgame_abtest_slt:CJy9p+lh7s4Wc6PG@tcp(10.9.46.118:3306)/abtest_dev?charset=utf8mb4&parseTime=True&loc=Local&timeout=3s&readTimeout=5s"
debug = true

[database.mysql.ad]
dsn = "h5app:CggIkhuZBNo6MAP27j6K@tcp(10.9.66.246:3306)/ad?charset=utf8mb4&parseTime=True&loc=Local&timeout=3s&readTimeout=5s"
debug = true

[database.redis.admin]
addr = "10.9.87.141:6379"
db = 4

[exp_sync.BI]
api_url = "http://t.admarket.youxi123.com/api/newBi/syn/expConf"
