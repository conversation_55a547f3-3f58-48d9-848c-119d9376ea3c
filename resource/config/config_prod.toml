addr = ":8001"
mode = "prod"
strategy_file = "/resource/assets/strategy.json"

ab_url = "http://abtest.youxi123.com"
bi_url = "https://admarket.youxi123.com"

[sentry]
env = "prod"
dsn = "http://ec87764e1c8fee6f0822489bf09815eb@***********:9000/2"

[jwt]
key = "hello-hungry-abtest"
sso_secret = "jXSIpOfCZDVgNTyFbk0YWH6imUjPiph98lj5xnzIYFJbCOXp"
admin = [387,137,463,467,725,726,727,728]

[task]
account_id = 515
close_exp_enabled = true
exp_strategy_trigger_enabled = true
close_exp_interval_second = 10
close_exp_days_ago = 7
exp_strategy_trigger_interval_second = 1

[database]
mysql_default = "abtest"

[database.mysql.abtest]
dsn = "abtest_backend_slt:CJy9p+lh7s4Wc6PG@tcp(***********:3306)/abtest_backend?charset=utf8mb4&parseTime=True&loc=Local&timeout=3s&readTimeout=5s"
debug = true

[database.mysql.ad]
dsn = "h5app:CggIkhuZBNo6MAP27j6K@tcp(***********:3306)/ad?charset=utf8mb4&parseTime=True&loc=Local&timeout=3s&readTimeout=5s"
debug = true

[database.redis.admin]
addr = "************:6379"
db = 4
password = "z3juFwyduLkP3Tsr"

[exp_sync.BI]
api_url = "https://admarket.youxi123.com/api/newBi/syn/expConf"
