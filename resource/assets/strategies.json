[{"strategy_type": "trigger_user_key", "strategy_name": "触发用户规则", "value": "country", "label": "国家", "type": "list.string", "allow_opt": [{"value": "in"}, {"value": "ni"}]}, {"strategy_type": "trigger_user_key", "strategy_name": "触发用户规则", "value": "af_media_source", "label": "AF渠道", "type": "list.string", "allow_opt": [{"value": "in"}, {"value": "ni"}], "source": {"type": "array", "list": [{"label": "dyg257967b4_int", "value": "dyg257967b4_int"}, {"label": "siftco_int", "value": "siftco_int"}, {"label": "appinflux_int", "value": "appinflux_int"}, {"label": "gnacompany_int", "value": "gnacompany_int"}, {"label": "yandexdirect_int", "value": "yandexdirect_int"}, {"label": "funnetads_int", "value": "funnetads_int"}, {"label": "movable_int", "value": "movable_int"}, {"label": "dygc68fc615_int", "value": "dygc68fc615_int"}, {"label": "pubmint_int", "value": "pubmint_int"}, {"label": "baseaids_int", "value": "baseaids_int"}, {"label": "rokuoneview_int", "value": "rokuoneview_int"}, {"label": "weperformyg_int", "value": "weperformyg_int"}, {"label": "appshot_int", "value": "appshot_int"}, {"label": "lenovotabpai4p_int", "value": "lenovotabpai4p_int"}, {"label": "engagerewards_int", "value": "engagerewards_int"}, {"label": "Google search", "value": "Google search"}, {"label": "Share-ClassicBestScore1-230225", "value": "Share-ClassicBestScore1-230225"}, {"label": "restricted", "value": "restricted"}, {"label": "Share-AdventureWin3-230225", "value": "Share-AdventureWin3-230225"}, {"label": "dygmovtion", "value": "dygmovtion"}, {"label": "dyg70a9f739_int", "value": "dyg70a9f739_int"}, {"label": "baliaffect_int", "value": "baliaffect_int"}, {"label": "metaweb", "value": "metaweb"}, {"label": "ayetstudios_int", "value": "ayetstudios_int"}, {"label": "Share-ClassicBestScore4-230225", "value": "Share-ClassicBestScore4-230225"}, {"label": "Share-ClassicBestScore2-230225", "value": "Share-ClassicBestScore2-230225"}, {"label": "skyflag_int", "value": "skyflag_int"}, {"label": "dyg25805322_int", "value": "dyg25805322_int"}, {"label": "freecash_int", "value": "freecash_int"}, {"label": "Meta_web", "value": "Meta_web"}, {"label": "promdyg", "value": "promdyg"}, {"label": "smadex_int", "value": "smadex_int"}, {"label": "onedigitalturbine_int", "value": "onedigitalturbine_int"}, {"label": "sponsorpay_int", "value": "sponsorpay_int"}, {"label": "tiktok_web", "value": "tiktok_web"}, {"label": "thetradedesk_int", "value": "thetradedesk_int"}, {"label": "dygmtion_int", "value": "dygmtion_int"}, {"label": "Social_instagram", "value": "Social_instagram"}, {"label": "boomob_int", "value": "boomob_int"}, {"label": "Share-Classic3-230225", "value": "Share-Classic3-230225"}, {"label": "hihonor_int", "value": "hihonor_int"}, {"label": "madhive_int", "value": "madhive_int"}, {"label": "TikTok", "value": "TikTok"}, {"label": "oppoglobal_int", "value": "oppoglobal_int"}, {"label": "tsone", "value": "tsone"}, {"label": "adtibe_int", "value": "adtibe_int"}, {"label": "inboxdollars_int", "value": "inboxdollars_int"}, {"label": "XIAOMI-Onelink", "value": "XIAOMI-Onelink"}, {"label": "dyge8999ad1_int", "value": "dyge8999ad1_int"}, {"label": "haina<PERSON><PERSON><PERSON>_int", "value": "haina<PERSON><PERSON><PERSON>_int"}, {"label": "adbridgeld_int", "value": "adbridgeld_int"}, {"label": "tasyee_int", "value": "tasyee_int"}, {"label": "Home Link", "value": "Home Link"}, {"label": "Snapchat-Onelink-Deeplink", "value": "Snapchat-Onelink-Deeplink"}, {"label": "Share-Set1-230225", "value": "Share-Set1-230225"}, {"label": "ad4game_int", "value": "ad4game_int"}, {"label": "kashkick_int", "value": "kashkick_int"}, {"label": "fluentco_int", "value": "fluentco_int"}, {"label": "okspin_int", "value": "okspin_int"}, {"label": "thecchange_int", "value": "thecchange_int"}, {"label": "bigoads_int", "value": "bigoads_int"}, {"label": "Share-Classic4-230225", "value": "Share-Classic4-230225"}, {"label": "dygc675f9fa_int", "value": "dygc675f9fa_int"}, {"label": "taurus_int", "value": "taurus_int"}, {"label": "Share-AdventureFailed3-230225", "value": "Share-AdventureFailed3-230225"}, {"label": "ironsource_int", "value": "ironsource_int"}, {"label": "mail.ru_int", "value": "mail.ru_int"}, {"label": "dyg653919a9_int", "value": "dyg653919a9_int"}, {"label": "thespotlight_int", "value": "thespotlight_int"}, {"label": "Mildom", "value": "Mildom"}, {"label": "edge_int", "value": "edge_int"}, {"label": "dayuwuxian_int", "value": "dayuwuxian_int"}, {"label": "Share-ClassicBestScore3-230225", "value": "Share-ClassicBestScore3-230225"}, {"label": "personalyrtb_int", "value": "personalyrtb_int"}, {"label": "scrambly_int", "value": "scrambly_int"}, {"label": "bidmotion_int", "value": "bidmotion_int"}, {"label": "jamppctv_int", "value": "jamppctv_int"}, {"label": "Share-AdventureWin4-230225", "value": "Share-AdventureWin4-230225"}, {"label": "Share-AdventureFailed2-230225", "value": "Share-AdventureFailed2-230225"}, {"label": "tapjoy_int", "value": "tapjoy_int"}, {"label": "dyg35dc78f8_int", "value": "dyg35dc78f8_int"}, {"label": "dyg3518a27c_int", "value": "dyg3518a27c_int"}, {"label": "flmobi_int", "value": "flmobi_int"}, {"label": "adisonofferwall_int", "value": "adisonofferwall_int"}, {"label": "skypieatech_int", "value": "skypieatech_int"}, {"label": "dyg0173ea33_int", "value": "dyg0173ea33_int"}, {"label": "kwaiforbusiness_int", "value": "kwaiforbusiness_int"}, {"label": "coolpadzhf_int", "value": "coolpadzhf_int"}, {"label": "mobvista_int", "value": "mobvista_int"}, {"label": "gasagheby_int", "value": "gasagheby_int"}, {"label": "armedaccept_int", "value": "armedaccept_int"}, {"label": "Share-Classic2-230225", "value": "Share-Classic2-230225"}, {"label": "Share-Set4-230225", "value": "Share-Set4-230225"}, {"label": "autokwai2f_int", "value": "autokwai2f_int"}, {"label": "voltadszf_int", "value": "voltadszf_int"}, {"label": "kvitsymedia_int", "value": "kvitsymedia_int"}, {"label": "longqingdianzi_int", "value": "longqingdianzi_int"}, {"label": "vibe_int", "value": "vibe_int"}, {"label": "dygcac1eea3_int", "value": "dygcac1eea3_int"}, {"label": "Share-AdventureWin1-230225", "value": "Share-AdventureWin1-230225"}, {"label": "mobisummer_int", "value": "mobisummer_int"}, {"label": "versemedia_int", "value": "versemedia_int"}, {"label": "vungle_int", "value": "vungle_int"}, {"label": "rewardsltjb_int", "value": "rewardsltjb_int"}, {"label": "Share-AdventureWin2-230225", "value": "Share-AdventureWin2-230225"}, {"label": "xiaomiglobal_int", "value": "xiaomiglobal_int"}, {"label": "Share-ClassicShare-Classic1-230225", "value": "Share-ClassicShare-Classic1-230225"}, {"label": "Social_twitch", "value": "Social_twitch"}, {"label": "ztesw_int", "value": "ztesw_int"}, {"label": "snapchat_int", "value": "snapchat_int"}, {"label": "User_invite", "value": "User_invite"}, {"label": "googlew2a", "value": "googlew2a"}, {"label": "hangmyads_int", "value": "hangmyads_int"}, {"label": "dyg22c06816_int", "value": "dyg22c06816_int"}, {"label": "primedyg_int", "value": "primedyg_int"}, {"label": "allmyfit_int", "value": "allmyfit_int"}, {"label": "myappfre_int", "value": "myappfre_int"}, {"label": "youngurts_int", "value": "youngurts_int"}, {"label": "ztepai_int", "value": "ztepai_int"}, {"label": "adaction5_int", "value": "adaction5_int"}, {"label": "bigabid_int", "value": "bigabid_int"}, {"label": "bytedanceglobal_int", "value": "bytedanceglobal_int"}, {"label": "chartboosts2s_int", "value": "chartboosts2s_int"}, {"label": "Discord", "value": "Discord"}, {"label": "vivoglobal_int", "value": "vivoglobal_int"}, {"label": "appnext_int", "value": "appnext_int"}, {"label": "reddit_int", "value": "reddit_int"}, {"label": "tvscientific_int", "value": "tvscientific_int"}, {"label": "Facebook Ads", "value": "Facebook Ads"}, {"label": "Social_twitter", "value": "Social_twitter"}, {"label": "adjoe_int", "value": "adjoe_int"}, {"label": "dygf7d445db_int", "value": "dygf7d445db_int"}, {"label": "wakemobi", "value": "wakemobi"}, {"label": "dv360_int", "value": "dv360_int"}, {"label": "moloco_int", "value": "moloco_int"}, {"label": "dygaa128692_int", "value": "dygaa128692_int"}, {"label": "appgrowth_int", "value": "appgrowth_int"}, {"label": "voodooib_int", "value": "voodooib_int"}, {"label": "None", "value": "None"}, {"label": "dyg8968c4e1_int", "value": "dyg8968c4e1_int"}, {"label": "tyrads_int", "value": "tyrads_int"}, {"label": "Share-Set3-230225", "value": "Share-Set3-230225"}, {"label": "google_gclid", "value": "google_gclid"}, {"label": "Share-Set2-230225", "value": "Share-Set2-230225"}, {"label": "Twitter", "value": "Twitter"}, {"label": "tiktoklive_int", "value": "tiktoklive_int"}, {"label": "organic", "value": "organic"}, {"label": "mistplay_int", "value": "mistplay_int"}, {"label": "exmox_int", "value": "exmox_int"}, {"label": "googleadwords_int", "value": "googleadwords_int"}, {"label": "digitalturbine_int", "value": "digitalturbine_int"}, {"label": "applovin_int", "value": "applovin_int"}, {"label": "vivopreload_int", "value": "vivopreload_int"}, {"label": "particlemedia_int", "value": "particlemedia_int"}, {"label": "xiaomipreload_int", "value": "xiaomipreload_int"}, {"label": "liftoff_int", "value": "liftoff_int"}, {"label": "oppopaipreinstall_int", "value": "oppopaipreinstall_int"}, {"label": "tiktokglobal_int", "value": "tiktokglobal_int"}, {"label": "mintegral_int", "value": "mintegral_int"}, {"label": "vivootapreload_int", "value": "vivootapreload_int"}, {"label": "Social_youtube", "value": "Social_youtube"}, {"label": "Social_facebook", "value": "Social_facebook"}, {"label": "unityads_int", "value": "unityads_int"}, {"label": "pubmin_int", "value": "pubmin_int"}, {"label": "aura_int", "value": "aura_int"}, {"label": "windyadsd_int", "value": "windyadsd_int"}, {"label": "xiaomipai_int", "value": "xiaomipai_int"}, {"label": "adisonadspw_int", "value": "adisonadspw_int"}, {"label": "Cross_sale", "value": "Cross_sale"}, {"label": "dygf1b7105f_int", "value": "dygf1b7105f_int"}, {"label": "oppo_int", "value": "oppo_int"}, {"label": "shalltrypai_int", "value": "shalltrypai_int"}]}}, {"strategy_type": "trigger_user_key", "strategy_name": "触发用户规则", "value": "app_version", "label": "APP版本号", "type": "string.version", "allow_opt": [{"value": "eq"}, {"value": "ne"}, {"value": "gt"}, {"value": "ge"}, {"value": "lt"}, {"value": "le"}, {"value": "in"}, {"value": "ni"}]}, {"strategy_type": "trigger_user_key", "strategy_name": "触发用户规则", "value": "game_version", "label": "资源版本号（game_version）", "type": "string.version", "allow_opt": [{"value": "eq"}, {"value": "ne"}, {"value": "gt"}, {"value": "ge"}, {"value": "lt"}, {"value": "le"}, {"value": "in"}, {"value": "ni"}]}, {"strategy_type": "trigger_user_key", "strategy_name": "触发用户规则", "value": "sdk_version", "label": "APP版本号（sdk_version）", "type": "string.version", "allow_opt": [{"value": "eq"}, {"value": "ne"}, {"value": "gt"}, {"value": "ge"}, {"value": "lt"}, {"value": "le"}, {"value": "in"}, {"value": "ni"}]}, {"strategy_type": "trigger_user_key", "strategy_name": "触发用户规则", "value": "exp_days", "label": "进入实验天数", "type": "int", "allow_opt": [{"value": "eq"}, {"value": "ne"}, {"value": "gt"}, {"value": "ge"}, {"value": "lt"}, {"value": "le"}]}, {"strategy_type": "trigger_user_key", "strategy_name": "触发用户规则", "value": "install_days", "label": "安装天数", "type": "int", "allow_opt": [{"value": "eq"}, {"value": "ne"}, {"value": "gt"}, {"value": "ge"}, {"value": "lt"}, {"value": "le"}]}, {"strategy_type": "trigger_user_key", "strategy_name": "触发用户规则", "value": "active_days", "label": "活跃天数", "type": "int", "allow_opt": [{"value": "eq"}, {"value": "ne"}, {"value": "gt"}, {"value": "ge"}, {"value": "lt"}, {"value": "le"}]}, {"strategy_type": "trigger_user_key", "strategy_name": "触发用户规则", "value": "user_tier", "label": "价值档位", "type": "int", "allow_opt": [{"value": "eq"}, {"value": "ne"}, {"value": "gt"}, {"value": "ge"}, {"value": "lt"}, {"value": "le"}]}, {"strategy_type": "trigger_user_key", "strategy_name": "触发用户规则", "value": "history_exp_ids", "label": "参与过的实验", "type": "list.int", "allow_opt": [{"value": "contain_any"}, {"value": "not_contain"}]}, {"strategy_type": "trigger_user_key", "strategy_name": "触发用户规则", "value": "history_grp_ids", "label": "参与过的方案", "type": "list.int", "allow_opt": [{"value": "contain_any"}, {"value": "not_contain"}]}, {"strategy_type": "trigger_user_key", "strategy_name": "触发用户规则", "value": "old_exp", "label": "老用户桶编号", "type": "int", "allow_opt": [{"value": "eq"}, {"value": "ne"}, {"value": "gt"}, {"value": "ge"}, {"value": "lt"}, {"value": "le"}]}, {"strategy_type": "trigger_user_key", "strategy_name": "触发用户规则", "value": "is_client_panel", "label": "是否客户端兜底", "type": "boolean", "allow_opt": [{"value": "eq"}]}, {"strategy_type": "trigger_user_key", "strategy_name": "触发用户规则", "value": "is_upgrade", "label": "是否热更", "type": "boolean", "allow_opt": [{"value": "eq"}]}, {"strategy_type": "trigger_user_key", "strategy_name": "触发用户规则", "value": "running_exp_ids", "label": "当前实验", "type": "list.int", "allow_opt": [{"value": "contain_any"}, {"value": "not_contain"}]}, {"strategy_type": "trigger_user_key", "strategy_name": "触发用户规则", "value": "running_grp_ids", "label": "当前方案", "type": "list.int", "allow_opt": [{"value": "contain_any"}, {"value": "not_contain"}]}, {"strategy_type": "trigger_user_key", "strategy_name": "触发用户规则", "value": "total_mem", "label": "设备总内存", "type": "float", "allow_opt": [{"value": "eq"}, {"value": "ne"}, {"value": "gt"}, {"value": "ge"}, {"value": "lt"}, {"value": "le"}]}, {"strategy_type": "trigger_user_key", "strategy_name": "触发用户规则", "value": "os_version", "label": "操作系统版本", "type": "string.version", "allow_opt": [{"value": "eq"}, {"value": "ne"}, {"value": "gt"}, {"value": "ge"}, {"value": "lt"}, {"value": "le"}]}, {"strategy_type": "trigger_user_key", "strategy_name": "触发用户规则", "value": "opt_tier", "label": "用户设备分层", "type": "string", "allow_opt": [{"value": "eq"}, {"value": "ne"}]}, {"strategy_type": "trigger_user_key", "strategy_name": "触发用户规则", "value": "core_num", "label": "CPU核心数", "type": "int", "allow_opt": [{"value": "eq"}, {"value": "ne"}, {"value": "gt"}, {"value": "ge"}, {"value": "lt"}, {"value": "le"}]}, {"strategy_type": "trigger_user_key", "strategy_name": "触发用户规则", "value": "manufacturers", "label": "生产厂商", "type": "list.string", "source": {"type": "array", "allow_create": true, "list": []}, "allow_opt": [{"value": "in"}, {"value": "ni"}]}, {"strategy_type": "trigger_user_key", "strategy_name": "触发用户规则", "value": "device_model", "label": "设备型号", "type": "list.string", "source": {"type": "array", "allow_create": true, "list": []}, "allow_opt": [{"value": "in"}, {"value": "ni"}]}, {"strategy_type": "trigger_stop_key", "strategy_name": "停止进量触发规则", "value": "test_flow", "label": "实验流量", "type": "int", "allow_opt": [{"value": "gt"}, {"value": "ge"}]}, {"strategy_type": "trigger_stop_key", "strategy_name": "停止进量触发规则", "value": "now_ts", "label": "当前时间", "type": "timestamp", "allow_opt": [{"value": "ge"}]}, {"strategy_type": "trigger_stop_key", "strategy_name": "停止进量触发规则", "value": "group_test_flow", "label": "单方案流量", "type": "int", "allow_opt": [{"value": "gt"}, {"value": "ge"}]}, {"strategy_type": "trigger_end_key", "strategy_name": "关闭触发规则", "value": "test_flow", "label": "实验流量", "type": "int", "allow_opt": [{"value": "eq"}, {"value": "ne"}, {"value": "gt"}, {"value": "ge"}, {"value": "lt"}, {"value": "le"}]}, {"strategy_type": "trigger_diversion_key", "strategy_name": "分流用户触发规则", "value": "ecpm", "label": "ecpm", "type": "range.float"}, {"strategy_type": "trigger_diversion_key", "strategy_name": "分流用户触发规则", "value": "adnum", "label": "日均广告次数", "type": "range.float"}, {"strategy_type": "trigger_diversion_key", "strategy_name": "分流用户触发规则", "value": "gamenum", "label": "日均局", "type": "range.float"}, {"strategy_type": "trigger_diversion_key", "strategy_name": "分流用户触发规则", "value": "aday", "label": "活跃天数", "type": "list.int.default", "default_value": [0, 1, 2, 3, 4, 5, 6, 7]}, {"strategy_type": "trigger_diversion_key", "strategy_name": "分流用户触发规则", "value": "country", "label": "国家", "type": "list.string.default", "default_value": ["", "us", "gb", "ph", "de", "ca", "pl", "au", "nl", "ru", "jp", "se", "fr", "kr", "it", "id", "be", "cz", "kz", "th", "eg", "ae", "pt", "ua", "fi", "gr", "ar", "hu", "sk", "al", "my", "hr", "ba", "cl", "mk", "lt", "pk", "iq", "co"]}, {"strategy_type": "trigger_diversion_key", "strategy_name": "分流用户触发规则", "value": "start_time", "label": "启动耗时（毫秒）", "type": "range.float"}, {"strategy_type": "trigger_diversion_key", "strategy_name": "分流用户触发规则", "value": "network", "label": "网络", "type": "list.string.default", "default_value": ["", "wifi", "5g", "4g", "3g", "2g"]}]