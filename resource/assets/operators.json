{"eq": {"value": "eq", "symbol": "==", "label": "等于"}, "ne": {"value": "ne", "symbol": "!=", "label": "不等于"}, "gt": {"value": "gt", "symbol": ">", "label": "大于"}, "lt": {"value": "lt", "symbol": "<", "label": "小于"}, "ge": {"value": "ge", "symbol": ">=", "label": "大于等于"}, "le": {"value": "le", "symbol": "<=", "label": "小于等于"}, "in": {"value": "in", "symbol": "∈", "label": "属于"}, "ni": {"value": "ni", "symbol": "∉", "label": "不属于"}, "contain": {"value": "contain", "symbol": "⊆", "label": "包含"}, "not_contain": {"value": "not_contain", "symbol": "⊈", "label": "不包含"}, "contain_any": {"value": "contain_any", "symbol": "contain_any", "label": "包含任一"}, "exclude_all": {"value": "exclude_all", "symbol": "exclude_all", "label": "排除全部"}}