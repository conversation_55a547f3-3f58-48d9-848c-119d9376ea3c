# This file is a template, and might need editing before it works on your project.
image: golang:1.24.0

variables:
  # Please edit to your GitLab project
  REPO_NAME: git.7k7k.com/data/abAdmin

# The problem is that to be able to use go get, one needs to put
# the repository in the $GOPATH. So for example if your gitlab domain
# is gitlab.com, and that your repository is namespace/project, and
# the default GOPATH being /go, then you'd need to have your
# repository in /go/src/gitlab.com/namespace/project
# Thus, making a symbolic link corrects this.
before_script:
  - mkdir -p $GOPATH/src/$(dirname $REPO_NAME)
  - ln -svf $CI_PROJECT_DIR $GOPATH/src/$REPO_NAME
  - cd $GOPATH/src/$REPO_NAME
  - git config url."****************:".insteadOf "https://git.7k7k.com/"
  - go version
  - go env -w GOPROXY=https://goproxy.cn,direct
  - time go mod tidy
  - time go build --ldflags="-checklinkname=0"
  - export LOG_DIR=/var/log

stages:
  - test
  - build
  - deploy

testing:
  stage: test
  coverage: '/total:\s+\(statements\)\s+(\d+.\d+\%)/'
  script:
    - go fmt $(go list ./... | grep -v /vendor/)
    - go vet $(go list ./... | grep -v /vendor/)
    - time go test -race -ldflags="-checklinkname=0" -coverprofile=coverage.out $(go list ./... | grep -v /vendor/)
    - go tool cover -func=coverage.out  # 输出覆盖率到stdout
  artifacts:
    paths:
      - coverage.out

# compile:
#   stage: build
#   script:
#     - go build -race -ldflags "-extldflags '-static'" -o $CI_PROJECT_DIR/mybinary
#   artifacts:
#     paths:
#       - mybinary
