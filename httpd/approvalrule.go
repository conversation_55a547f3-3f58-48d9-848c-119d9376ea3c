package httpd

import (
	"errors"
	"git.7k7k.com/data/abAdmin/httpd/base"
	"git.7k7k.com/data/abAdmin/httpd/request"
	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/service"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// BatchController 批次管理
// @autowire(set=http)
type ApprovalRuleController struct {
	service.ApprovalRuleManageService
}

// List 审批规则列表
func (c *ApprovalRuleController) List(ctx *gin.Context) {
	var req request.ApprovalListReq
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		base.WriteError(ctx, base.NewValidateError(err))
		return
	}
	list, err := c.ApprovalRuleManageService.GetApprovalRuleList(ctx, req)
	if err != nil {
		base.WriteError(ctx, base.NewValidateError(err))
	}
	base.WriteOk(ctx, list)
}

// Create 创建审批规则
func (c *ApprovalRuleController) Create(ctx *gin.Context) {
	var req model.ApprovalRule
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		base.WriteError(ctx, base.NewValidateError(err))
		return
	}
	rule, err := c.ApprovalRuleManageService.AddApprovalRule(ctx, &req)
	if err != nil {
		base.WriteError(ctx, base.NewValidateError(err))
		return
	}
	base.WriteOk(ctx, rule)
}

// Update 更新审批规则
func (c *ApprovalRuleController) Update(ctx *gin.Context) {
	var req model.ApprovalRule
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		base.WriteError(ctx, base.NewValidateError(err))
		return
	}
	rule, err := c.ApprovalRuleManageService.UpdateApprovalRule(ctx, &req)
	if err != nil {
		base.WriteError(ctx, base.NewValidateError(err))
		return
	}
	base.WriteOk(ctx, rule)
}

// Delete 删除审批规则
func (c *ApprovalRuleController) Delete(ctx *gin.Context) {
	ruleIdStr := ctx.Query("id")
	if len(ruleIdStr) == 0 {
		base.WriteError(ctx, base.NewValidateError(errors.New("参数不合法")))
		return
	}
	ruleId := cast.ToInt64(ruleIdStr)
	rule, err := c.ApprovalRuleManageService.DeleteApprovalRule(ctx, ruleId)
	if err != nil {
		base.WriteError(ctx, base.NewValidateError(err))
		return
	}
	base.WriteOk(ctx, rule)
}

// 获取审批节点列表
func (c *ApprovalRuleController) GetApprovalNodeList(ctx *gin.Context) {
	list, err := c.ApprovalRuleManageService.GetApprovalNodeList(ctx)
	if err != nil {
		base.WriteError(ctx, base.NewValidateError(err))
	}
	base.WriteOk(ctx, list)
}

// 审批开关
func (c *ApprovalRuleController) SwitchApprovalRule(ctx *gin.Context) {
	ruleIdStr, flag := ctx.GetPostForm("id")
	if !flag {
		base.WriteError(ctx, base.NewValidateError(errors.New("参数不合法")))
		return
	}
	ruleId := cast.ToInt64(ruleIdStr)
	stateStr, flag := ctx.GetPostForm("state")
	if !flag {
		base.WriteError(ctx, base.NewValidateError(errors.New("参数不合法")))
		return
	}
	state := cast.ToInt32(stateStr)
	rule, err := c.ApprovalRuleManageService.SwitchApprovalRule(ctx, ruleId, state)
	if err != nil {
		base.WriteError(ctx, base.NewValidateError(err))
		return
	}
	base.WriteOk(ctx, rule)
}
