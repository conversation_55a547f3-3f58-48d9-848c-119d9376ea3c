package request

import "time"

// ExpLogQuery 实验日志列表查询条件
type ExpLogQuery struct {
	Keyword   string     `form:"keyword"`
	BeginDate *time.Time `form:"begin_date" time_format:"2006-01-02"`
	EndDate   *time.Time `form:"end_date" time_format:"2006-01-02"`
	ExpAllLogQuery
	PageReq
}

type ExpAllLogQuery struct {
	ExpId int64 `form:"exp_id,string" binding:"required"` // 实验ID
}

// ExpSnapshotQuery 实验快照查询
type ExpSnapshotQuery struct {
	ExpLogId int64 `form:"exp_log_id,string" binding:"required"` // 实验日志ID
}
