package request

// StrategyQuery 实验策略模板查询结构体
type StrategyQuery struct {
	TplType int32 `form:"tpl_type,string" binding:"required"`
}

// StrategyDetailQuery 详情查询结构体
type StrategyDetailQuery struct {
	Id int64 `form:"id, string" binding:"required"`
}

// StrategyFilterQuery 实验策略过滤器
type StrategyFilterQuery struct {
	// 触发用户规则:trigger_user_key
	// 停止进量触发规则: trigger_stop_key
	// 关闭触发规则: trigger_end_key
	// 分流用户触发规则: trigger_diversion_key
	Type  string `form:"type" binding:"required"`
	ExpId int64  `form:"exp_id, string"`
}
