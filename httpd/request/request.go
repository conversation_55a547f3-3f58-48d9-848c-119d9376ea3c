package request

import (
	"git.7k7k.com/data/abAdmin/model"
)

type SplitExpReq struct {
	ExpId     int64 `json:"exp_id" binding:"-"`
	State     int32 `json:"state" binding:"required"`
	Operation int32 `json:"operation" binding:"required"`
}
type SplitGroupReq struct {
	ExpId     int64 `json:"exp_id" binding:"-"`
	State     int32 `json:"state" binding:"required"`
	Operation int32 `json:"operation" binding:"required"`
	Type      int32 `json:"type" binding:"-"`
}

type AddExpBaseReq struct {
	ExpId   int64  `json:"exp_id" binding:""`
	Name    string `json:"name" binding:"required"`
	ExpType int32  `json:"exp_type" binding:"required"`
	BatchId int64  `json:"batch_id,omitempty" binding:"-"`
	TagId   string `json:"tag_id,omitempty" binding:"-"`
	Days    int32  `json:"days" binding:"required,gte=1,lte=9999"`
	Owner   string `json:"owner" binding:"min=0,max=255"`
	Desc    string `json:"desc" binding:"min=0,max=100"`
	Step    int32  `json:"step" binding:"required"`
	Version int64  `json:"version" binding:"-"`
}

type AddExpStrategyReq struct {
	ExpId          int64            `json:"exp_id" binding:"required"`
	LayerId        int64            `json:"layer_id" binding:"required"`
	LayerRate      int32            `json:"layer_rate" binding:"-"`
	Step           int32            `json:"step" binding:"-"`
	Strategy       *model.Strategy  `json:"strategy" binding:"-"`
	Csr            []*model.CSRData `json:"csr" binding:"-"`
	Version        int64            `json:"version" binding:"-"`
	GroupLimitHour int32            `json:"group_limit_hour" binding:"-"`
}

type ChangeStateReq struct {
	ExpId     int64 `json:"exp_id" binding:"required"`
	Operation int32 `json:"operation" binding:"required"`
}
type DeleteStateReq struct {
	ExpId int64 `json:"exp_id" binding:"required"`
	IsDel int32 `json:"is_del" binding:"required"`
}
type ExpListReq struct {
	Search     string   `json:"search" binding:"min=0,max=255"`
	StartTime  []string `json:"start_time" binding:""`
	EndTime    []string `json:"end_time" binding:""`
	Page       int      `json:"page" binding:"required"`
	PageSize   int      `json:"page_size" binding:""`
	ExpType    string   `json:"exp_type" binding:""`
	BatchId    string   `json:"batch_id" binding:""`
	TagId      string   `json:"tag_id" binding:""`
	LayerId    string   `json:"layer_id" binding:""`
	State      string   `json:"state" binding:""`
	OrderField string   `json:"order_field" binding:""`
	OrderType  string   `json:"order_type" binding:""`
}
type AddExpReq struct {
	ExpId     int64            `json:"exp_id" binding:"required"`
	Name      string           `json:"name" binding:"required"`
	ExpType   int32            `json:"exp_type" binding:"required"`
	BatchId   int64            `json:"batch_id" binding:"-"`
	TagId     string           `json:"tag_id" binding:"-"`
	Days      int32            `json:"days" binding:"required,gte=1,lte=9999"`
	Owner     string           `json:"owner" binding:"min=0,max=255"`
	Desc      string           `json:"desc" binding:"min=0,max=100"`
	LayerId   int64            `json:"layer_id" binding:"required"`
	LayerRate int32            `json:"layer_rate" binding:"-"`
	Step      int32            `json:"step" binding:"required"`
	State     int32            `json:"state"`
	Strategy  *model.Strategy  `json:"strategy" binding:"-"`
	Csr       []*model.CSRData `json:"csr" binding:"min=0,max=255"`
	Groups    []Group          `json:"groups" binding:"required"`
	Version   int64            `json:"version" binding:"-"`
	Ignore    bool             `json:"ignore" binding:"-"`
}

type EditSaveExpReq struct {
	ExpInfo              *EditSaveBase    `json:"exp_info" binding:"required"`
	Groups               []*EditSaveGroup `json:"groups" binding:"required"`
	CloseGroup           []*GroupsReq     `json:"close_group" binding:"-"`
	NewGroup             []*GroupsReq     `json:"new_group" binding:"-"`
	RunningRedirectGroup []*GroupsReq     `json:"running_redirect_group" binding:"-"`
	RedirectGroup        []*GroupsReq     `json:"redirect_group" binding:"-"`
	Ignore               bool             `json:"ignore"`
	Desc                 string           `json:"desc" binding:"min=0,max=1000"`
}

type EditSaveGroup struct {
	GroupId       int64  `json:"group_id" binding:"-"`
	BatchId       int64  `json:"batch_id" binding:"-"`
	BatchName     string `json:"batch_name" binding:"-"`
	Name          string `json:"name" binding:"required"`
	GroupType     int32  `json:"group_type" binding:"required"`
	IsProgress    int32  `json:"is_progress" binding:"required"`
	ParamsType    int32  `json:"params_type" binding:"-"`
	ParamsContent string `json:"params_content" binding:"required"`
	Desc          string `json:"desc" binding:"min=0,max=5000"`
	State         int32  `json:"state" binding:"required"`
	IsDeleted     int32  `json:"is_deleted" binding:"-"`
}
type GroupsReq struct {
	Groups   *GroupBase `json:"groups" binding:"required"`
	ToGroups *GroupBase `json:"to_groups" binding:"-"`
}

type EditSaveBase struct {
	Id             int64            `json:"id" binding:"required"`
	Name           string           `json:"name" binding:"required"`
	Csr            []*model.CSRData `json:"csr" binding:"min=0,max=255"`
	Strategy       *model.Strategy  `json:"strategy" binding:"required"`
	ExpType        int32            `json:"exp_type" binding:"required"`
	BatchId        int64            `json:"batch_id" binding:"-"`
	BatchName      string           `json:"batch_name" binding:"-"`
	ExpRunTime     string           `json:"exp_run_time" binding:"-"`
	TagId          string           `json:"tag_id" binding:"-"`
	TagName        string           `json:"tag_name" binding:"-"`
	Days           int32            `json:"days" binding:"required,gte=1,lte=9999"`
	Owner          string           `json:"owner" binding:""`
	OwnerName      string           `json:"owner_name" binding:"-"`
	LayerId        int64            `json:"layer_id" binding:"required"`
	LayerName      string           `json:"layer_name" binding:"-"`
	LayerRate      int32            `json:"layer_rate" binding:"-"`
	Version        int64            `json:"version" binding:"required"`
	Desc           string           `json:"desc"`
	GroupLimitHour int32            `json:"group_limit_hour" binding:"-"`
	State          int32            `json:"state" binding:"-"`
}

type GroupBase struct {
	ExpId     int64  `json:"exp_id" binding:"required"`
	ExpName   string `json:"exp_name" binding:"required"`
	GroupId   int64  `json:"group_id" binding:"-"`
	GroupName string `json:"group_name" binding:"-"`
	Rate      string `json:"rate" binding:"-"`
}

type Group struct {
	ExpId         int64  `json:"exp_id" binding:"-"`
	GroupId       int64  `json:"group_id" binding:""`
	Name          string `json:"name" binding:"required"`
	GroupType     int32  `json:"group_type" binding:"required"`
	IsProgress    int32  `json:"is_progress" binding:"required"`
	ParamsType    int32  `json:"params_type" binding:"required"`
	ParamsContent string `json:"params_content" binding:"required"`
	//WhiteList     string `json:"white_list" binding:""`
	Desc  string `json:"desc" binding:"min=0,max=5000"`
	State int32  `json:"state" binding:"required"`
	IsDel int32  `json:"is_del" binding:"required"`
}

type ListExpReq struct {
	Page      int32  `json:"page" binding:"required"`
	PageSize  int32  `json:"page_size" binding:"required"`
	Search    string `json:"search" binding:""`
	StartTime string `json:"start_time" binding:""`
	EndTime   string `json:"end_time" binding:""`
	ExpType   int32  `json:"exp_type" binding:""`
	Owner     string `json:"owner" binding:"min=0,max=255"`
	LayerId   int32  `json:"layer_id" binding:""`
	State     int32  `json:"state" binding:""`
	TagId     int32  `json:"tag_id" binding:""`
	BatchId   int32  `json:"batch_id" binding:""`
}

type AddLayerReq struct {
	Name        string `json:"name" binding:"required,min=0,max=20"`
	IsExclusion int32  `json:"is_exclusion" binding:"required"`
	Level       int32  `json:"level" binding:"gte=0,lte=3"`
	Rate        int32  `json:"rate" binding:"-"`
}
type UpdateLayerReq struct {
	Id          int64  `json:"layer_id"`
	Name        string `json:"name" binding:"required,min=0,max=20"`
	IsExclusion int32  `json:"is_exclusion" binding:"required"`
	Level       int32  `json:"level" binding:"gte=0,lte=3"`
	Rate        int32  `json:"rate" binding:"-"`
}
type CheckLayerReq struct {
	IsExclusion int32 `json:"is_exclusion" binding:"required"`
	ProjectId   int32 `json:"project_id" binding:"required"`
}
type DownLayerReq struct {
	Id    int64 `json:"layer_id"`
	State int32 `json:"state"`
}
type ListLayerReq struct {
	Page     int    `json:"page" binding:"-"`
	PageSize int    `json:"page_size" binding:"-"`
	Search   string `json:"search" binding:""`
	State    int32  `json:"state" binding:"required"`
}

type AddTagReq struct {
	Name string `json:"name" binding:"required"`
}
type ListTagReq struct {
	Page   int    `json:"page" binding:"required"`
	Size   int    `json:"size" binding:"required"`
	Search string `json:"search" binding:""`
}
type UpdateTagReq struct {
	Id   int64  `json:"tag_id"`
	Name string `json:"name" binding:"min=0,max=255"`
}
type ListProjectReq struct {
	Search string `json:"search" binding:""`
	State  int32  `json:"state" binding:"required"`
}
type ProjectReq struct {
	UniqueId        string                     `json:"unique_id" binding:"required,alpha"`
	Name            string                     `json:"name" binding:"required"`
	Desc            string                     `json:"desc" binding:"min=0,max=100"`
	Data            model.ProjectData          `json:"data"`
	FeatureConflict model.FeatureConflictRules `json:"feature_conflict"`
}

type DownProjectReq struct {
	Id    int64 `json:"id"`
	State int32 `json:"state"`
}

type StrategyProjectReq struct {
	Id       int64    `json:"id"`
	Strategy []string `json:"strategy" binding:"required,min=1,max=255"`
}

type CSRData struct {
	Key   string      `json:"key"`
	Value interface{} `json:"value"`
	Type  string      `json:"type,omitempty"`
}

type ListStrategyReq struct {
	Id      int32 `json:"id" binding:""`
	TplType int32 `json:"tpl_type" binding:"required"`
}

// add group
type AddGroupReq struct {
	Ignore  bool     `json:"ignore" binding:"-"`
	Groups  []*Group `json:"groups" binding:"min=0,max=255"`
	ExpId   int64    `json:"exp_id" binding:"required"`
	Step    int32    `json:"step" binding:"required"`
	Version int64    `json:"version" binding:"-"`
}

type WhiteReq struct {
	GroupId int64  `json:"group_id" binding:"required"`
	White   string `json:"white_list" binding:"-"`
}

type UpdateGroupReq struct {
	Id            int64  `json:"group_id" binding:"required"`
	Name          string `json:"name" binding:""`
	ExpId         int64  `json:"exp_id" binding:""`
	GroupType     int32  `json:"group_type" binding:""`
	IsProgress    int32  `json:"is_progress" binding:""`
	Params        string `json:"params" binding:"min=0,max=255"`
	ParamsType    int32  `json:"params_type" binding:""`
	ParamsContent string `json:"params_content" binding:""`
	WhiteList     string `json:"white_list" binding:""`
	Desc          string `json:"desc" binding:"min=0,max=255"`
	State         int32  `json:"state" binding:""`
}

type DownloadReq struct {
	DownloadType string  `json:"download_type" binding:"required"`
	ExpIds       []int64 `json:"exp_ids" binding:"required"`
}

type ApprovalListReq struct {
	Page     int     `json:"page" binding:"required"`
	PageSize int     `json:"page_size" binding:"required"`
	Search   string  `json:"search" binding:""`
	State    []int32 `json:"state" binding:""`
}

type ApprovalTaskListReq struct {
	Page       int     `json:"page" binding:"required"`
	PageSize   int     `json:"page_size" binding:"required"`
	Search     string  `json:"search" binding:""`
	State      []int32 `json:"state" binding:""`
	ListType   int32   `json:"list_type" binding:""`
	IsEffected []int32 `json:"is_effected" binding:""`
}

type ApprovalReq struct {
	Id   int64 `json:"id" binding:"required"`
	Step int32 `json:"step" binding:""`
	model.ApprovalLog
}
