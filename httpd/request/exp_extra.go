package request

import "git.7k7k.com/data/abAdmin/model"

type ExtraExp struct {
	TxId      int64            `json:"tx_id" binding:"required"`
	Id        int64            `json:"id" binding:"-"`
	Name      string           `json:"name" binding:"required"`
	ExpType   int32            `json:"exp_type" binding:"required"`
	Desc      string           `json:"desc" binding:"-"`
	TagId     string           `json:"tag_id" binding:"-"`
	BatchId   int64            `json:"batch_id" binding:"-"`
	Days      int32            `json:"days" binding:"required"`
	Owner     string           `json:"owner" binding:"min=0,max=255"`
	LayerId   int64            `json:"layer_id" binding:"-"`
	LayerRate int64            `json:"layer_rate" binding:"-"`
	Csr       []*model.CSRData `json:"csr" binding:"min=0,max=255"`
	Strategy  *model.Strategy  `json:"strategy" binding:"required"`
	Groups    []*Group         `json:"groups" binding:"required"`
}
