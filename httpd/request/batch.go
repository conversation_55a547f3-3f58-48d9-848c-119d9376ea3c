package request

// BatchQuery 查询条件
type BatchQuery struct {
	Keyword string `json:"keyword" form:"keyword"`
	PageReq
}

// BatchReq 创建批次
type BatchReq struct {
	Name string `json:"name" binding:"required,max=20"`
	Desc string `json:"desc" binding:"max=40"`
}

// BatchUpdate 更新批次
type BatchUpdate struct {
	Id string `json:"id" binding:"required"`
	BatchReq
}

type BatchDelete struct {
	Id int64 `form:"id,string" binding:"required"`
}
