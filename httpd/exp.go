package httpd

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"strings"

	"git.7k7k.com/data/abAdmin/httpd/base"
	"git.7k7k.com/data/abAdmin/httpd/request"
	"git.7k7k.com/data/abAdmin/infra/auth"
	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/service"
	"github.com/cockroachdb/errors"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
)

// @autowire(set=http)
type ExpController struct {
	*service.ExpService
	*service.GroupService
	*service.ExpBatchService
}

func (e *ExpController) ExpForStrategy(ctx *gin.Context) {
	search := ctx.Query("search")
	expList, err := e.ExpService.ExpForStrategy(search, ctx)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.<PERSON>r(), "code": 1})
		return
	}
	ctx.JSON(200, gin.H{"msg": "success", "data": expList, "code": 0})
	return
}

func (e *ExpController) CanSplitExpList(ctx *gin.Context) {
	var req request.SplitExpReq
	var err error
	if err = ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}
	validate := validator.New()
	err = validate.Struct(req)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}
	list, err := e.ExpService.CanSplitExpList(req, ctx)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}
	ctx.JSON(200, gin.H{"msg": "success", "code": 0, "data": list})
	return

}
func (e *ExpController) CanSplitGroupList(ctx *gin.Context) {
	var req request.SplitGroupReq
	var err error
	if err = ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}
	validate := validator.New()
	err = validate.Struct(req)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}
	list, err := e.ExpService.CanSplitGroupsList(req, ctx)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}
	ctx.JSON(200, gin.H{"msg": "success", "code": 0, "data": list})
	return
}

func (e *ExpController) SaveBase(ctx *gin.Context) {
	var add request.AddExpBaseReq
	var err error
	if err = ctx.ShouldBindJSON(&add); err != nil {
		base.WriteError(ctx, err)
		return
	}
	validate := validator.New()
	err = validate.Struct(add)
	if err != nil {
		base.WriteError(ctx, err)
		return
	}
	if strings.Trim(add.Name, " ") == "" {
		base.WriteError(ctx, base.NewAlertTip("实验名称不能是空字符串"))
		return

	}
	exp, err := e.ExpService.SaveBase(add, ctx)
	if err != nil {
		base.WriteError(ctx, base.NewBizError(err))
		return
	}
	base.WriteOk(ctx, map[string]any{"exp_id": exp.ID, "version": exp.Version})
	return
}
func (e *ExpController) ExpWithLayer(ctx *gin.Context) {
	var id int64
	layerId := ctx.Query("layer_id")
	if layerId != "" {
		id = cast.ToInt64(layerId)
	}
	list, err := e.ExpService.ExpWithLayer(id)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}
	ctx.JSON(200, gin.H{"msg": "success", "data": list, "code": 0})
	return
}
func (e *ExpController) Detail(ctx *gin.Context) {
	var expId int64
	expIdStr := ctx.Query("exp_id")
	if expIdStr == "" {
		ctx.JSON(200, gin.H{"msg": "exp_id is empty", "code": 1, "data": ""})
		return
	}
	expId = cast.ToInt64(expIdStr)
	expInfo, err := e.ExpService.GetDetail(expId, ctx)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1})
		return
	}
	ctx.JSON(200, gin.H{"msg": "success", "data": expInfo, "code": 0})
	return

}
func (e *ExpController) SaveStrategy(ctx *gin.Context) {
	var add request.AddExpStrategyReq
	if err := ctx.ShouldBindJSON(&add); err != nil {
		base.WriteError(ctx, err)
		return
	}
	validate := validator.New()
	err := validate.Struct(add)
	if err != nil {
		base.WriteError(ctx, err)
		return
	}

	exp, err := e.ExpService.SaveStrategy(ctx, add)
	if err != nil {
		base.WriteError(ctx, base.NewBizError(err))
		return
	}
	ret := make(map[string]any)
	ret["version"] = exp.Version
	ret["exp_id"] = exp.ID
	base.WriteOk(ctx, ret)
	return

}
func (e *ExpController) verifySaveGroups(req request.AddGroupReq) error {
	groups := req.Groups
	compareCount := 0
	expCount := 0
	for _, group := range groups {
		if group.GroupType == model.GroupTypeCompare {
			compareCount = compareCount + 1
		}
		if group.GroupType == model.GroupTypeExp {
			expCount = expCount + 1
		}
	}
	if compareCount < 1 || expCount < 1 {
		return base.NewBizError(errors.New("至少有一个实验组和对照组"))
	}
	return nil
}
func (e *ExpController) SaveGroups(ctx *gin.Context) {
	var add request.AddGroupReq
	var err error
	if err := ctx.ShouldBindJSON(&add); err != nil {
		base.WriteError(ctx, err)
		return
	}
	validate := validator.New()
	err = validate.Struct(add)
	if err != nil {
		base.WriteError(ctx, err)
		return
	}
	err = e.verifySaveGroups(add)
	if err != nil {
		base.WriteError(ctx, err)
		return
	}
	ret, err := e.ExpService.SaveGroups(add, ctx)
	if err != nil {
		base.WriteError(ctx, base.NewBizError(err))
		return
	}
	base.WriteOk(ctx, ret)
	return
}
func (e *ExpController) Save(ctx *gin.Context) {
	var req *request.AddExpReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1, "data": ""})
		return
	}
	validate := validator.New()
	err := validate.Struct(req)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1, "data": ""})
		return
	}

	ret, err := e.ExpService.Save(req, ctx)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1, "data": ret})
		return
	}
	ctx.JSON(200, gin.H{"msg": "AB实验相关得数据请在BI平台查看，陆续开放相关得功能没敬请关注", "code": 0, "data": ret})
	return
}
func (e *ExpController) Download(ctx *gin.Context) {
	var req request.DownloadReq
	var err error
	if err = ctx.ShouldBindJSON(&req); err != nil {
		base.WriteError(ctx, err)
		return
	}
	err = e.ExpBatchService.Download(ctx, req.DownloadType, req.ExpIds)
	if err != nil {
		base.WriteError(ctx, err)
		return
	}
	return
}
func (e *ExpController) Upload(ctx *gin.Context) {
	var err error
	file, err := ctx.FormFile("file")
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1, "data": ""})
		return
	}
	f, err := file.Open()
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1, "data": ""})
		return
	}
	defer f.Close()
	data, err := ioutil.ReadAll(f)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1, "data": ""})
		return
	}
	// 3. 解析 JSON 内容
	expList := &model.ExpInfoList{}
	if err = json.Unmarshal(data, &expList); err != nil {
		ctx.JSON(200, gin.H{"msg": "JSON 解析失败", "code": 1, "data": nil})
		return
	}
	ret, err := e.ExpBatchService.Upload(ctx, expList)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1, "data": nil})
		return
	}
	ctx.JSON(200, gin.H{"msg": "success", "code": 0, "data": ret})

}
func (e *ExpController) UploadGroups(ctx *gin.Context) {
	var err error
	file, err := ctx.FormFile("file")
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1, "data": ""})
		return
	}
	f, err := file.Open()
	defer f.Close()
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1, "data": ""})
		return
	}
	dataRead, err := excelize.OpenReader(f)
	defer dataRead.Close()
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1, "data": ""})
		return
	}
	sheetList := dataRead.GetSheetList()
	if len(sheetList) == 0 {
		ctx.JSON(200, gin.H{"msg": "data is empty", "code": 1, "data": ""})
		return
	}
	data, err := dataRead.GetRows(sheetList[0])
	if len(data) == 0 {
		ctx.JSON(200, gin.H{"msg": "excel sheet is empty", "code": 1, "data": ""})
		return
	}
	expTypeMap := map[string]int32{
		"对照组": 1,
		"实验组": 2,
	}
	isProcessMap := map[string]int32{
		"是": 1,
		"否": 2,
	}
	list := make([]map[string]any, 0, len(data))
	isRepeat := make(map[string]bool, 100)
	dataErrArr := make([]string, 0, len(data))
	for i, datum := range data {
		if i == 0 || len(datum) == 0 {
			continue
		}

		// 判断5列全为空的情况，如果全为空，则跳过
		flag := false
		for _, v := range datum {
			val := strings.TrimSpace(v)
			if len(val) > 0 {
				flag = true
				break
			}

		}
		if !flag {
			continue
		}
		if len(datum) < 5 {
			dataErr := fmt.Sprintf("第%d行，实验名称：%s 缺少相应的列，请修改", i+1, datum[0])
			dataErrArr = append(dataErrArr, dataErr)
			continue
		}
		mapData := make(map[string]any)
		expType := strings.Trim(datum[1], "")
		if _, ok := expTypeMap[expType]; !ok {
			dataErr := fmt.Sprintf("第%d行，实验名称：%s 实验组类型不合法，请修改", i+1, datum[0])
			dataErrArr = append(dataErrArr, dataErr)
			continue
		}

		isProcess := strings.Trim(datum[3], "")
		if _, ok := isProcessMap[isProcess]; !ok {
			dataErr := fmt.Sprintf("第%d行，实验名称：%s 实验组是否进量类型不合法，请修改", i+1, datum[0])
			dataErrArr = append(dataErrArr, dataErr)
			continue
		}
		name := strings.Trim(datum[0], "")
		if isRepeat[name] {
			dataErr := fmt.Sprintf("第%d行，实验名称：%s 出现重复，请修改", i+1, datum[0])
			dataErrArr = append(dataErrArr, dataErr)
			continue
		}
		isRepeat[name] = true
		if datum[4] != "" {
			err = e.ProjectService.IsParamsValid(ctx, auth.GetProjectId64(ctx), datum[4])
			if err != nil {
				dataErr := fmt.Sprintf("第%d行，实验名称：%s 参数格式不对，应该为json格式，请修改", i+1, datum[0])
				dataErrArr = append(dataErrArr, dataErr)
				continue
			}
		}

		mapData["name"] = datum[0]
		mapData["group_type"] = expTypeMap[expType]
		mapData["desc"] = datum[2]
		mapData["is_progress"] = isProcessMap[isProcess]
		mapData["params_content"] = datum[4]
		list = append(list, mapData)
	}
	if len(dataErrArr) > 0 {
		ctx.JSON(200, gin.H{"msg": strings.Join(dataErrArr, "\n"), "code": 1, "data": ""})
		return
	}
	ctx.JSON(200, gin.H{"msg": "success", "code": 0, "data": list})
	return
}

func (e *ExpController) EditSave(ctx *gin.Context) {
	var req request.EditSaveExpReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1, "data": ""})
		return
	}
	validate := validator.New()
	err := validate.Struct(req)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1, "data": ""})
		return
	}

	ret, err := e.ExpService.EditSave(&req, ctx)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1, "data": ret})
		return
	}
	ctx.JSON(200, gin.H{"msg": "success", "code": 0, "data": ret})
	return

}
func (e *ExpController) List(ctx *gin.Context) {
	var req request.ExpListReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1, "data": ""})
		return
	}
	validate := validator.New()
	err := validate.Struct(req)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1, "data": ""})
		return
	}

	list, err := e.ExpService.List(req, ctx, auth.GetProjectId64(ctx))
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}
	ctx.JSON(200, gin.H{"msg": "success", "code": 0, "data": list})
	return
}
func (e *ExpController) SetWhite(ctx *gin.Context) {

	var req request.WhiteReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1, "data": ""})
		return
	}
	validate := validator.New()
	err := validate.Struct(req)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1, "data": ""})
		return
	}

	data, err := e.ExpService.SetWhite(ctx, &req)

	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1, "data": ""})
		return
	}
	ctx.JSON(200, gin.H{"msg": "success", "code": 0, "data": data})
	return
}
func (e *ExpController) GetBatchList(ctx *gin.Context) {
	list, err := e.ExpService.GetBatchList(ctx)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}
	ctx.JSON(200, gin.H{"msg": "success", "code": 0, "data": list})
	return
}
func (e *ExpController) GetTagList(ctx *gin.Context) {
	list, err := e.ExpService.GetTagList(ctx)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}
	ctx.JSON(200, gin.H{"msg": "success", "code": 0, "data": list})
	return
}
func (e *ExpController) DraftList(ctx *gin.Context) {
	var req request.ExpListReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1, "data": ""})
		return
	}
	validate := validator.New()
	err := validate.Struct(req)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1, "data": ""})
		return
	}

	list, err := e.ExpService.DraftList(req, ctx)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}
	ctx.JSON(200, gin.H{"msg": "success", "code": 0, "data": list})
	return
}
func (e *ExpController) ChangeState(ctx *gin.Context) {
	var req request.ChangeStateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		base.WriteError(ctx, base.NewValidateError(err))
		return
	}
	validate := validator.New()
	err := validate.Struct(req)
	if err != nil {
		base.WriteError(ctx, base.NewValidateError(err))
		return
	}

	ret, err := e.ExpService.ChangeState(ctx, &req)
	if err != nil {
		base.WriteError(ctx, err)
		return
	}
	base.WriteOk(ctx, ret)
}
func (e *ExpController) ListHeader(ctx *gin.Context) {
	list, err := e.ExpService.ListHeader(ctx)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}
	ctx.JSON(200, gin.H{"msg": "success", "code": 0, "data": list})
	return
}
func (e *ExpController) Delete(ctx *gin.Context) {
	var req request.DeleteStateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1, "data": ""})
		return
	}
	validate := validator.New()
	err := validate.Struct(req)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1, "data": ""})
		return
	}

	err = e.ExpService.Delete(req, ctx)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}
	ctx.JSON(200, gin.H{"msg": "success", "code": 0, "data": 0})
	return
}
