package base

import (
	"git.7k7k.com/pkg/common/ctxs"
	"github.com/gin-gonic/gin"
)

type HTTPOut[T any] struct {
	Code    int    `json:"code"`
	TraceId string `json:"trace_id"`
	Message string `json:"msg,omitempty"`
	Data    T      `json:"data,omitempty"`
}

type HTTPOutAny struct {
	Code    int    `json:"code"`
	TraceId string `json:"trace_id"`
	Message string `json:"msg,omitempty"`
	Data    any    `json:"data,omitempty"`
}

func Write(gc *gin.Context, code int, data any) {
	gc.Writer.Header().Set("Content-Type", "application/json")
	gc.JSON(200, HTTPOutAny{Code: code, Data: data, TraceId: ctxs.TraceId(gc)})
}

func WriteOk(gc *gin.Context, data any) {
	gc.Writer.Header().Set("Content-Type", "application/json")
	gc.JSON(200, HTTPOutAny{Code: 0, Data: data, TraceId: ctxs.TraceId(gc)})
}

func WriteErrMsg(gc *gin.Context, code int, msg string) {
	gc.Writer.Header().Set("Content-Type", "application/json")
	gc.JSON(200, HTTPOutAny{Code: code, Message: msg, TraceId: ctxs.TraceId(gc)})
}

func Abort(gc *gin.Context, code int, msg string) {
	WriteErrMsg(gc, code, msg)
	gc.Abort()
}

// WriteError 通过err的类型转换http码和业务错误码
func WriteError(gc *gin.Context, err error) {
	gc.Writer.Header().Set("Content-Type", "application/json")
	appErr := TransError(err)
	gc.JSON(appErr.HttpCode, &HTTPOutAny{
		Code:    appErr.ErrCode,
		Message: appErr.ErrMsg,
	})
}
