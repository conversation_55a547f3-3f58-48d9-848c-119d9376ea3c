package base

import (
	"github.com/cockroachdb/errors"
	"github.com/go-playground/validator/v10"
	"strings"
	"unicode"
)

type validateError struct {
	err error
}

// NewValidateError 参数验证
func NewValidateError(err error) *validateError {
	return &validateError{err: err}
}

func (e validateError) Error() string {
	var validationErrors validator.ValidationErrors
	if !errors.As(e.err, &validationErrors) {
		return e.err.Error()
	}

	var msg strings.Builder
	for i, fieldErr := range e.err.(validator.ValidationErrors) {
		if i > 0 {
			msg.WriteString(", ")
		}
		msg.WriteString(convertToSnakeCase(fieldErr.Field()))
		switch fieldErr.Tag() {
		case "required":
			msg.WriteString("是必填的")
		case "min":
			msg.WriteString("的值不能小于")
			msg.WriteString(fieldErr.Param())
		case "max":
			msg.WriteString("的值不能超过")
			msg.WriteString(fieldErr.Param())
		case "gte":
			msg.WriteString("的值必须大于等于")
			msg.WriteString(fieldErr.Param())
		case "lte":
			msg.WriteString("的值必须小于等于")
			msg.WriteString(fieldErr.Param())
		case "email":
			msg.WriteString("邮件格式验证失败")
		case "url":
			msg.WriteString("url格式验证失败")
		default:
			msg.WriteString("验证失败")
		}
	}
	return msg.String()
}

// convertToSnakeCase 转换为蛇形
func convertToSnakeCase(field string) string {
	var snakeCase strings.Builder
	for _, char := range field {
		if unicode.IsUpper(char) {
			if snakeCase.Len() > 0 {
				snakeCase.WriteString("_")
			}
			snakeCase.WriteRune(unicode.ToLower(char))
		} else {
			snakeCase.WriteRune(char)
		}
	}
	return snakeCase.String()
}
