package base

// 移到middleware中 TODO，全局处理

import (
	"net/http"
)

// AppError 应用程序错误结构体
type AppError struct {
	HttpCode int    // http响应码
	ErrCode  int    // 业务错误码
	ErrMsg   string // 错误消息
	Err      error  // 原始错误（可选）
}

// createAppError 创建AppError实例的辅助函数
func createAppError(httpCode int, errCode int, errMsg string, err error) *AppError {
	if errMsg == "" && err != nil {
		errMsg = err.Error()
	}
	return &AppError{
		HttpCode: httpCode,
		ErrCode:  errCode,
		ErrMsg:   errMsg,
		Err:      err,
	}
}

// TransError 方法根据传入的HTTP状态码返回对应的错误码和消息
func TransError(err error) (appErr *AppError) {
	if err == nil {
		return createAppError(http.StatusOK, 0, "success", nil)
	}

	switch err.(type) {
	//	参数校验异常
	case validateError, *validateError:
		appErr = createAppError(http.StatusBadRequest, 40001, err.Error(), err)

	//	业务异常
	case bizError, *bizError:
		appErr = createAppError(http.StatusOK, 50001, err.Error(), err)

	case alertTip, *alertTip:
		appErr = createAppError(http.StatusOK, 50002, err.Error(), err)

	default:
		appErr = createAppError(http.StatusInternalServerError, 99999, "未知错误", err)
	}
	return appErr
}
