package httpd

import (
	"git.7k7k.com/data/abAdmin/httpd/request"
	"git.7k7k.com/data/abAdmin/service"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"strings"
)

// @autowire(set=http)
type ProjectController struct {
	*service.ProjectService
}

func (c *ProjectController) Add(ctx *gin.Context) {
	var add request.ProjectReq
	var err error
	if err = ctx.ShouldBindJSON(&add); err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1})
		return
	}
	validate := validator.New()
	err = validate.Struct(add)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.<PERSON>rror(), "code": 1})
		return
	}
	if strings.TrimSpace(add.Name) == "" {
		ctx.JSON(200, gin.H{"msg": "项目名称不能为空，请重新输入"})
		return
	}
	err = c.ProjectService.Add(add)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1})
		return
	}
	ctx.JSON(200, gin.H{"msg": "success", "code": 0, "data": ""})
	return
}

func (c *ProjectController) List(ctx *gin.Context) {
	var req request.ListProjectReq
	var err error
	if err = ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1})
		return
	}
	validate := validator.New()
	err = validate.Struct(req)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1})
		return
	}
	list, err := c.ProjectService.List(req, ctx)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1})
		return
	}

	ctx.JSON(200, gin.H{"msg": "success", "code": 0, "data": list})
	return
}
func (c *ProjectController) DownList(ctx *gin.Context) {
	var req request.ListProjectReq
	var err error
	if err = ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1})
		return
	}
	validate := validator.New()
	err = validate.Struct(req)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1})
		return
	}
	list, err := c.ProjectService.DownList(req, ctx)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1})
		return
	}

	ctx.JSON(200, gin.H{"msg": "success", "code": 0, "data": list})
	return
}
func (c *ProjectController) Edit(ctx *gin.Context) {
	var req request.ProjectReq
	var err error
	if err = ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1})
		return
	}

	validate := validator.New()
	err = validate.Struct(req)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1})
		return
	}
	if strings.TrimSpace(req.Name) == "" {
		ctx.JSON(200, gin.H{"msg": "项目不能为空，请重新输入"})
		return
	}
	count, err := c.Update(req, ctx)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1})
		return
	}
	ctx.JSON(200, gin.H{"msg": "success", "code": 0, "count": count})
	return
}

func (c *ProjectController) Down(ctx *gin.Context) {
	var req request.DownProjectReq
	var err error
	if err = ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1})
		return
	}

	validate := validator.New()
	err = validate.Struct(req)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1})
		return
	}
	count, err := c.ProjectService.Down(req, ctx)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1})
		return
	}
	if count == 0 {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1, "count": count})
		return
	}
	ctx.JSON(200, gin.H{"msg": "success", "code": 0, "count": count})
	return
}
