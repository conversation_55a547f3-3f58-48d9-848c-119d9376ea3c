package httpd

import (
	"git.7k7k.com/data/abAdmin/gopkg/dtalk"
	"git.7k7k.com/data/abAdmin/service"
	"github.com/gin-gonic/gin"
)

func DingtalkOutgoing(s *service.DingtalkCommandService) func(ctx *gin.Context) {
	return func(gc *gin.Context) {
		obj := dtalk.OutGoingModel{}
		err := gc.BindJSON(&obj)
		if err != nil {
			return
		}

		cmd := obj.CMD()
		out := s.<PERSON><PERSON>(gc, cmd)
		gc.JSON(200, out)
	}
}
