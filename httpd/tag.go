package httpd

import (
	"git.7k7k.com/data/abAdmin/httpd/request"
	"git.7k7k.com/data/abAdmin/service"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/spf13/cast"
	"strings"
)

// @autowire(set=http)
type TagController struct {
	*service.TagService
}

func (c *TagController) Add(ctx *gin.Context) {
	var add request.AddTagReq
	var err error
	if err = ctx.ShouldBindJSON(&add); err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1})
		return
	}
	validate := validator.New()
	err = validate.Struct(add)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1})
		return
	}
	if strings.TrimSpace(add.Name) == "" {
		ctx.JSON(200, gin.H{"msg": "标签名不能为空，请重新输入", "code": 1})
		return
	}
	err = c.TagService.Add(add, ctx)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1})
		return
	}
	ctx.JSON(200, gin.H{"msg": "success", "code": 0, "data": ""})
	return
}

func (c *TagController) List(ctx *gin.Context) {
	var req request.ListTagReq
	var err error
	if err = ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1})
		return
	}
	validate := validator.New()
	err = validate.Struct(req)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1})
		return
	}
	list, count, err := c.TagService.List(req, ctx)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}

	ctx.JSON(200, gin.H{"msg": "success", "code": 0, "data": list, "total": count, "page": req.Page, "size": req.Size})
	return
}
func (c *TagController) Edit(ctx *gin.Context) {
	var req request.UpdateTagReq
	var err error
	if err = ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}

	validate := validator.New()
	err = validate.Struct(req)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1})
		return
	}
	if strings.TrimSpace(req.Name) == "" {
		ctx.JSON(200, gin.H{"msg": "标签名不能为空，请重新输入", "code": 1})
		return
	}
	count, err := c.Update(req, ctx)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}
	if count == 0 {
		ctx.JSON(200, gin.H{"msg": "标签名称重复", "code": 1})
		return
	}
	ctx.JSON(200, gin.H{"msg": "success", "code": 0, "data": count})
	return
}

func (c *TagController) Delete(ctx *gin.Context) {
	id := ctx.Query("tag_id")
	if id == "" {
		ctx.JSON(200, gin.H{"msg": "tag_id is empty"})
		return
	}
	tagId := cast.ToInt64(id)
	count, err := c.TagService.Delete(tagId, ctx)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1})
		return
	}
	ctx.JSON(200, gin.H{"msg": "success", "code": 0, "data": count})
	return
}
