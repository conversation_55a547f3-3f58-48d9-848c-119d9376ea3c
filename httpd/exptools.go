package httpd

import (
	"encoding/json"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"io"
	"sort"
	"strings"
)

// @autowire(set=http)
type ExpToolsController struct {
}

// JSON writes a JSON response with code 200 and an empty data field.
func (t *ExpToolsController) JsonFileToExpList(ctx *gin.Context) {
	// Read the JSON file into a byte slice
	var err error
	file, err := ctx.FormFile("file")
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1, "data": ""})
		return
	}
	f, err := file.Open()
	defer f.Close()
	data, err := io.ReadAll(f)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1, "data": ""})
		return
	}
	if !json.Valid(data) {
		ctx.JSON(200, gin.H{"msg": "json file is invalid", "code": 1, "data": ""})
		return
	}

	// Unmarshal the byte slice into a map
	expList := make([]map[string]interface{}, 0, 100000)
	err = json.Unmarshal(data, &expList)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1, "data": ""})
		return
	}
	//
	ret := make([]map[string]any, 0, 10000)
	for _, expItem := range expList {
		if expItem == nil {
			continue
		}
		newExpItem := make(map[string]any, 10)
		if bucketTitle, ok := expItem["bucketTitle"].(string); ok {
			newExpItem["bucketTitle"] = bucketTitle
		}
		if bucketId, ok := expItem["bucketId"].(float64); ok {
			newExpItem["bucketId"] = bucketId
		}
		if bucketState, ok := expItem["bucketState"].(string); ok {
			newExpItem["bucketState"] = bucketState
		}
		if _, ok := expItem["experimentList"]; ok {
			groupList := make([][]string, 0, 200)
			if expItem["experimentList"] != nil {
				if experimentList, ok := expItem["experimentList"].(map[string]interface{}); ok {
					for groupName, jsonValue := range experimentList {
						jsonByte, _ := json.Marshal(jsonValue)
						jsonStr := string(jsonByte)
						groupList = append(groupList, []string{groupName, "实验组", "描述", "是", jsonStr})
					}
					sort.Slice(groupList, func(i, j int) bool {
						groupArr1 := strings.Split(groupList[i][0], "_")
						groupArr2 := strings.Split(groupList[j][0], "_")
						if len(groupArr2) == 2 && len(groupArr1) == 2 {
							num1 := cast.ToInt64(groupArr1[1])
							num2 := cast.ToInt64(groupArr2[1])
							return num1 < num2
						} else {
							return groupList[i][0] < groupList[j][0]
						}
					})
					newExpItem["groupList"] = groupList
				}
			}
		}

		ret = append(ret, newExpItem)
	}
	ctx.JSON(200, gin.H{"msg": "success", "code": 0, "data": ret})
	return

}
