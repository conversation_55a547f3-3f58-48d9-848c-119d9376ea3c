package httpd

import (
	"git.7k7k.com/data/abAdmin/httpd/base"
	"git.7k7k.com/data/abAdmin/httpd/request"
	"git.7k7k.com/data/abAdmin/httpd/response"
	"git.7k7k.com/data/abAdmin/service"
	"github.com/gin-gonic/gin"
)

// ExpLogController 实验操作日志
// @autowire(set=http)
type ExpLogController struct {
	*service.ExpLogService
}

// List 实验操作日志（含关键字查询）
func (h *ExpLogController) List(c *gin.Context) {
	var query request.ExpLogQuery
	err := c.BindQuery(&query)
	if err != nil {
		base.WriteError(c, base.NewValidateError(err))
		return
	}

	data, count, err := h.ExpLogService.List(&query)
	if err != nil {
		base.WriteError(c, err)
	} else {
		base.WriteOk(c, &response.ListData{
			Count: count,
			Data:  data,
		})
	}
}

// Detail 实验操作日志（含关键字查询）
func (h *ExpLogController) Detail(c *gin.Context) {
	var query request.ExpSnapshotQuery
	err := c.BindQuery(&query)
	if err != nil {
		base.WriteError(c, base.NewValidateError(err))
		return
	}

	data, err := h.ExpLogService.Detail(&query)
	if err != nil {
		base.WriteError(c, err)
	} else {
		base.WriteOk(c, data)
	}
}

// All 获取该实验下所有的快照列表，给前端当select选择列表
func (h *ExpLogController) All(c *gin.Context) {
	var query request.ExpAllLogQuery
	err := c.BindQuery(&query)
	if err != nil {
		base.WriteError(c, base.NewValidateError(err))
		return
	}

	data, err := h.ExpLogService.All(&query)
	if err != nil {
		base.WriteError(c, err)
	} else {
		base.WriteOk(c, data)
	}
}
