package httpd

import (
	"git.7k7k.com/data/abAdmin/httpd/base"
	"git.7k7k.com/data/abAdmin/httpd/request"
	"git.7k7k.com/data/abAdmin/service"
	"github.com/cockroachdb/errors"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// BatchController 批次管理
// @autowire(set=http)
type ApprovalTaskController struct {
	service.ApprovalTaskManageService
}

// List 审批规则列表
func (c *ApprovalTaskController) List(ctx *gin.Context) {
	var req request.ApprovalTaskListReq
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		base.WriteError(ctx, base.NewValidateError(err))
		return
	}
	list, err := c.ApprovalTaskManageService.List(ctx, req)
	if err != nil {
		base.WriteError(ctx, base.NewValidateError(err))
		return
	}
	base.WriteOk(ctx, list)
}

// List 审批规则列表
func (c *ApprovalTaskController) Approval(ctx *gin.Context) {
	var req request.ApprovalReq
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		base.WriteError(ctx, base.NewValidateError(err))
		return
	}
	list, err := c.ApprovalTaskManageService.Approval(ctx, req)
	if err != nil {
		base.WriteError(ctx, base.NewValidateError(err))
		return
	}
	base.WriteOk(ctx, list)
}
func (c *ApprovalTaskController) Revoke(ctx *gin.Context) {
	approvalIdStr := ctx.Query("id")
	if len(approvalIdStr) == 0 {
		base.WriteError(ctx, base.NewValidateError(errors.New("参数不合法")))
		return
	}
	approvalId := cast.ToInt64(approvalIdStr)
	list, err := c.ApprovalTaskManageService.Revoke(ctx, approvalId)
	if err != nil {
		base.WriteError(ctx, base.NewValidateError(err))
		return
	}
	base.WriteOk(ctx, list)
}

// List 审批规则列表
func (c *ApprovalTaskController) Log(ctx *gin.Context) {
	approvalIdStr := ctx.Query("id")
	if len(approvalIdStr) == 0 {
		base.WriteError(ctx, base.NewValidateError(errors.New("参数不合法")))
		return
	}
	approvalId := cast.ToInt64(approvalIdStr)
	log, err := c.ApprovalTaskManageService.ApprovalLog(ctx, approvalId)
	if err != nil {
		base.WriteError(ctx, base.NewValidateError(err))
		return
	}
	base.WriteOk(ctx, log)
}

// List 审批规则列表
func (c *ApprovalTaskController) Changes(ctx *gin.Context) {
	approvalIdStr := ctx.Query("id")
	if len(approvalIdStr) == 0 {
		base.WriteError(ctx, base.NewValidateError(errors.New("参数不合法")))
		return
	}
	approvalId := cast.ToInt64(approvalIdStr)
	log, err := c.ApprovalTaskManageService.Changes(ctx, approvalId)
	if err != nil {
		base.WriteError(ctx, base.NewValidateError(err))
		return
	}
	base.WriteOk(ctx, log)
}
