package httpd

import (
	"git.7k7k.com/data/abAdmin/httpd/base"
	"git.7k7k.com/data/abAdmin/httpd/request"
	"git.7k7k.com/data/abAdmin/model/dto"
	"git.7k7k.com/data/abAdmin/service"
	"github.com/gin-gonic/gin"
	"strings"
)

// @autowire(set=http)
type StrategyController struct {
	*service.StrategyService
}

// Create 创建策略模板
func (c *StrategyController) Create(ctx *gin.Context) {
	var req dto.AddStrategyReq
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		base.WriteError(ctx, base.NewValidateError(err))
		return
	}
	req.Name = strings.TrimSpace(req.Name)
	if req.Name == "" {
		base.WriteError(ctx, base.NewAlertTip("名称不可以为空"))
		return
	}
	err = c.StrategyService.Create(ctx, &req)
	base.WriteError(ctx, err)
}

// List 实验模板的列表
func (c *StrategyController) List(ctx *gin.Context) {
	var query request.StrategyQuery
	err := ctx.ShouldBindQuery(&query)
	if err != nil {
		base.WriteError(ctx, base.NewValidateError(err))
		return
	}
	list, err := c.StrategyService.List(ctx, &query)
	if err != nil {
		base.WriteError(ctx, err)
		return
	}
	base.WriteOk(ctx, list)
}

// Detail 根据ID获取实验模板的详情
func (c *StrategyController) Detail(ctx *gin.Context) {
	var query request.StrategyDetailQuery
	err := ctx.ShouldBindQuery(&query)
	if err != nil {
		base.WriteError(ctx, base.NewValidateError(err))
		return
	}
	data, err := c.StrategyService.Detail(&query)
	if err != nil {
		base.WriteError(ctx, err)
		return
	}
	base.WriteOk(ctx, data)
}

// FilterList 实验策略的过滤器
func (c *StrategyController) FilterList(ctx *gin.Context) {
	var query request.StrategyFilterQuery
	err := ctx.ShouldBindQuery(&query)
	if err != nil {
		base.WriteError(ctx, base.NewValidateError(err))
		return
	}
	data, err := c.StrategyService.FilterList(ctx, &query)
	if err != nil {
		base.WriteError(ctx, err)
		return
	}
	base.WriteOk(ctx, data)
}

// FilterExpression 实验策略的表达式
func (c *StrategyController) FilterExpression(ctx *gin.Context) {
	var query dto.StrategyQueryReq
	err := ctx.ShouldBindJSON(&query)
	if err != nil {
		base.WriteError(ctx, base.NewValidateError(err))
		return
	}
	data, err := c.StrategyService.QueryExpression(ctx, &query)
	if err != nil {
		base.WriteError(ctx, err)
		return
	}
	base.WriteOk(ctx, data)
}
