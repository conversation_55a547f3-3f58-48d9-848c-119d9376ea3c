package response

import "git.7k7k.com/data/abAdmin/model"

type ExpDetail struct {
	Id                int64            `json:"id"`
	Name              string           `json:"name"`
	Desc              string           `json:"desc"`
	ExpType           int32            `json:"exp_type"`
	ExpTypeName       string           `json:"exp_type_name"`
	Days              int32            `json:"days"`
	ExpRunTime        string           `json:"exp_run_time"`
	ExpRunTimeDays    int64            `json:"exp_run_time_days"`
	ExpRunTimeHours   int64            `json:"exp_run_time_hours"`
	ExpRunTimeMinutes int64            `json:"exp_run_time_minutes"`
	GroupLimitHour    int32            `json:"group_limit_hour"`
	BatchId           int64            `json:"batch_id"`
	BatchName         string           `json:"batch_name"`
	Owner             string           `json:"owner"`
	OwnerName         string           `json:"owner_name"`
	TagId             string           `json:"tag_id"`
	TagName           string           `json:"tag_name"`
	LayerId           int64            `json:"layer_id"`
	ProjectID         int64            `json:"project_id"`
	LayerName         string           `json:"layer_name"`
	LayerRate         int32            `json:"layer_rate"`
	Uid               int32            `json:"uid"`
	TotalUser         int64            `json:"total_user"`
	TotalCurrentUser  int64            `json:"total_current_user"`
	StartTime         string           `json:"start_time"`
	EndTime           string           `json:"end_time"`
	State             int32            `json:"state"`
	StateName         string           `json:"state_name"`
	StrategyStr       string           `json:"strategy_str"`
	Strategy          *model.Strategy  `json:"strategy"`
	CsrStr            string           `json:"csr_str"`
	Csr               []*model.CSRData `json:"csr"`
	Step              int32            `json:"step"`
	NextStep          int32            `json:"next_step"`
	Version           int64            `json:"version"`
	CreateTime        string           `json:"create_time"`
	UpdateTime        string           `json:"update_time"`
	Groups            []*model.Group   `gorm:"foreignKey:exp_id" json:"groups"`
	IsApprovalRunning bool             `json:"is_approval_running"`
	ApprovalUid       int32            `json:"approval_uid"`
	ApprovalId        int64            `json:"approval_id"`
}

func (e *ExpDetail) ToModel() *model.Exp {
	return &model.Exp{
		ID:        e.Id,
		ProjectID: e.ProjectID,
		Name:      e.Name,
	}
}

type DraftExpList struct {
	Id         int64  `json:"id"`
	Name       string `json:"name"`
	Step       string `json:"step"`
	NextStep   int32  `json:"next_step"`
	CreateTime string `json:"create_time"`
	UpdateTime string `json:"update_time"`
	Owner      string `json:"owner"`
	OwnerName  string `json:"owner_name"`
}
type Group struct {
	GroupId          int64  `json:"group_id"`
	Name             string `json:"name"`
	GroupType        int    `json:"group_type"`
	GroupTypeName    string `json:"group_type_name"`
	Desc             string `json:"desc"`
	ParamsName       string `json:"params_name"`
	ParamsContent    string `json:"params_content"`
	IsProgress       int    `json:"is_progress"`
	BatchId          int    `json:"batch_id"`
	BatchName        string `json:"batch_name"`
	WhiteList        string `json:"white_list"`
	Rate             int    `json:"rate"`
	UserCount        int64  `json:"user_count"`
	UserCurrentCount int64  `json:"user_current_count"`
	State            int    `json:"state"`
	IsDeleted        int    `json:"is_deleted"`
}
type ExpList struct {
	ExpId     int64  `json:"exp_id"`
	ExpName   string `json:"exp_name"`
	LayerRate int32  `json:"layer_rate"`
	State     int32  `json:"state"`
}

type LayerList struct {
	Id       int64  `json:"id"`
	Name     string `json:"name"`
	Level    int32  `json:"level"`
	Desc     string `json:"desc"`
	DownTime string `json:"down_time"`
	Rate     int32  `json:"rate"`
}

type Item struct {
	Label any `json:"label"`
	Value any `json:"value"`
}
type ConflictGroup struct {
	GroupName string      `json:"group_name"`
	Conflicts []*Conflict `json:"conflicts"`
}
type Conflict struct {
	ParamContent   string   `json:"param"`
	ConflictParams []string `json:"conflict_params"`
	ExpName        string   `json:"exp_name"`
	ExpId          int64    `json:"exp_id"`
	GroupsName     []string `json:"group_name"`
	GroupIds       []int64  `json:"group_ids"`
}
type ExpGroup struct {
	ExpId    int64            `json:"exp_id"`
	Version  int64            `json:"version"`
	Conflict []*ConflictGroup `json:"conflict"`
}

// ListData 列表数据
type List struct {
	Total    int64 `json:"total" binding:"required"`
	List     any   `json:"list" binding:"required"`
	Page     int   `json:"page" binding:"required"`
	PageSize int   `json:"page_size" binding:"required"`
}
