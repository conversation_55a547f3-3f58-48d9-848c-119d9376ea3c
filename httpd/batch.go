package httpd

import (
	"git.7k7k.com/data/abAdmin/httpd/base"
	"git.7k7k.com/data/abAdmin/httpd/request"
	"git.7k7k.com/data/abAdmin/httpd/response"
	"git.7k7k.com/data/abAdmin/service"
	"github.com/gin-gonic/gin"
	"strings"
)

// BatchController 批次管理
// @autowire(set=http)
type BatchController struct {
	*service.BatchService
}

// Create 新建批次
func (h *BatchController) Create(c *gin.Context) {
	var batch request.BatchReq
	err := c.ShouldBindJSON(&batch)
	if err != nil {
		base.WriteError(c, base.NewValidateError(err))
		return
	}
	batch.Name = strings.TrimSpace(batch.Name)
	if batch.Name == "" {
		base.WriteError(c, base.NewAlertTip("名称不可以为空"))
		return
	}

	err = h.BatchService.Create(c, batch)
	base.WriteError(c, err)
}

// Update 更新批次
func (h *BatchController) Update(c *gin.Context) {
	var batch request.BatchUpdate
	err := c.ShouldBindJSON(&batch)
	if err != nil {
		base.WriteError(c, base.NewValidateError(err))
		return
	}
	batch.Name = strings.TrimSpace(batch.Name)
	if batch.Name == "" {
		base.WriteError(c, base.NewAlertTip("名称不可以为空"))
		return
	}

	err = h.BatchService.Update(batch)
	base.WriteError(c, err)
}

// Delete 删除批次
func (h *BatchController) Delete(c *gin.Context) {
	var batch request.BatchDelete
	err := c.ShouldBindQuery(&batch)
	if err != nil {
		base.WriteError(c, base.NewValidateError(err))
		return
	}

	err = h.BatchService.Delete(batch)
	base.WriteError(c, err)
}

// List 批次列表（含关键字查询）
func (h *BatchController) List(c *gin.Context) {
	var query request.BatchQuery
	err := c.BindQuery(&query)
	if err != nil {
		base.WriteError(c, base.NewValidateError(err))
		return
	}

	batchData, count, err := h.BatchService.List(c, &query)
	if err != nil {
		base.WriteError(c, err)
	} else {
		base.WriteOk(c, &response.ListData{
			Count: count,
			Data:  batchData,
		})
	}
}
