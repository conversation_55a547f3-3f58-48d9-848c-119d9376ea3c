package httpd

import (
	"git.7k7k.com/data/abAdmin/httpd/request"
	"git.7k7k.com/data/abAdmin/service"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"strings"
)

// @autowire(set=http)
type LayerController struct {
	*service.LayerService
}

// add layer
func (c LayerController) Add(ctx *gin.Context) {
	var add request.AddLayerReq
	var err error
	if err = ctx.ShouldBindJSON(&add); err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}
	validate := validator.New()
	err = validate.Struct(add)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1})
		return
	}
	if strings.TrimSpace(add.Name) == "" {
		ctx.JSON(200, gin.H{"msg": "分层名不能为空，请重新输入", "code": 1})
		return
	}
	_, err = c.LayerService.Add(ctx, add)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error(), "code": 1})
		return
	}
	ctx.JSON(200, gin.H{"msg": "success", "code": 0, "data": ""})
	return
}

func (c LayerController) List(ctx *gin.Context) {
	var req request.ListLayerReq
	var err error
	if err = ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}
	validate := validator.New()
	err = validate.Struct(req)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}
	list, err := c.LayerService.List(ctx, req)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}

	ctx.JSON(200, gin.H{"msg": "success", "code": 0, "data": list})
	return
}
func (c LayerController) DownList(ctx *gin.Context) {
	var req request.ListLayerReq
	var err error
	if err = ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}
	validate := validator.New()
	err = validate.Struct(req)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}
	list, err := c.LayerService.DownList(req, ctx)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}

	ctx.JSON(200, gin.H{"msg": "success", "code": 0, "data": list})
	return
}
func (c LayerController) Edit(ctx *gin.Context) {
	var req request.UpdateLayerReq
	var err error
	if err = ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}

	validate := validator.New()
	err = validate.Struct(req)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}
	if strings.TrimSpace(req.Name) == "" {
		ctx.JSON(200, gin.H{"msg": "分层名不能为空，请重新输入"})
		return
	}
	count, err := c.LayerService.Update(req, ctx)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}
	ctx.JSON(200, gin.H{"msg": "success", "code": 0, "data": count})
	return
}

func (c LayerController) Down(ctx *gin.Context) {
	var req request.DownLayerReq
	var err error
	if err = ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}

	validate := validator.New()
	err = validate.Struct(req)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}
	count, err := c.LayerService.Down(req, ctx)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}
	ctx.JSON(200, gin.H{"msg": "success", "code": 0, "data": count})
	return
}

func (c *LayerController) All(ctx *gin.Context) {
	list, err := c.LayerService.All(ctx)
	if err != nil {
		ctx.JSON(200, gin.H{"msg": err.Error()})
		return
	}
	ctx.JSON(200, gin.H{"msg": "success", "code": 0, "data": list})
	return
}
