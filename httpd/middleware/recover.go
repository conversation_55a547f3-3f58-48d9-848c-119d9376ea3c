package middleware

import (
	"fmt"
	"log/slog"
	"runtime/debug"

	"git.7k7k.com/data/abAdmin/httpd/base"
	"github.com/gin-gonic/gin"
)

func Recover() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if r := recover(); r != nil {
				base.WriteErrMsg(c, 500, fmt.Sprintf("%+v", r))
				slog.ErrorContext(c, "recoverHTTP", "stack", string(debug.Stack()))
				slog.ErrorContext(c, string(debug.Stack()))
			}
		}()

		c.Next()
	}
}
