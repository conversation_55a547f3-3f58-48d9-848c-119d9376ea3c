package middleware

import (
	"log/slog"
	"net/http"

	"git.7k7k.com/data/abAdmin/httpd/base"
	"git.7k7k.com/data/abAdmin/infra/auth"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/golang-jwt/jwt/v5/request"
	"github.com/samber/lo"
	"github.com/spf13/cast"
)

func ContextKeyValue() func(*gin.Context) {
	return func(c *gin.Context) {
		heads := c.Request.Header.Get("X-Project-Id")
		if heads != "" {
			c.Set(auth.ContextKeyProjectID, cast.ToInt(heads))
		}
	}
}

// AuthInternal
func AuthInternal() func(*gin.Context) {
	return func(c *gin.Context) {
		userId := cast.ToInt(c.GetHeader("X-User-ID"))
		if userId == 0 {
			base.Abort(c, http.StatusUnauthorized, "unauthorized: empty uid")
			return
		}

		c.Set(auth.ContextKeyUserID, userId)
	}
}

// AuthCheck 只负责取出 auth，不判断权限
func AuthCheck(key []byte, admin []int) func(*gin.Context) {
	adminSet := lo.SliceToMap(admin, func(item int) (int, bool) {
		return item, true
	})
	_ = adminSet

	return func(c *gin.Context) {
		token, err := request.ParseFromRequest(c.Request, request.OAuth2Extractor, func(token *jwt.Token) (interface{}, error) {
			return key, nil
		})
		if err != nil {
			slog.ErrorContext(c, "unauthorized", "header", c.Request.Header)
			base.Abort(c, http.StatusUnauthorized, "unauthorized: "+err.Error())
			return
		}

		payload := token.Claims.(jwt.MapClaims)
		if len(payload) == 0 {
			base.Abort(c, http.StatusUnauthorized, "unauthorized: empty payload")
			return
		}

		userId := cast.ToInt(payload["uid"])
		if userId == 0 {
			base.Abort(c, http.StatusUnauthorized, "unauthorized: empty uid")
			return
		}

		c.Set(auth.ContextKeyUserID, userId)
		disableAdminOnce := c.GetHeader("Noroot") == "1" // 临时禁用超管
		if !disableAdminOnce && (adminSet[userId] || cast.ToBool(payload["admin"])) {
			c.Set(auth.ContextKeyIsAdmin, true)
		}
	}
}

var Permissions = map[string]string{
	"/api/project/create":             "abtest.project.all",
	"/api/project/list":               "",
	"/api/project/downlist":           "abtest.project.all",
	"/api/project/edit":               "abtest.project.all",
	"/api/project/down":               "abtest.project.all",
	"/api/tag/create":                 "abtest.tag.all",
	"/api/tag/list":                   "",
	"/api/tag/delete":                 "abtest.tag.all",
	"/api/tag/edit":                   "abtest.tag.all",
	"/api/layer/create":               "abtest.layer.all",
	"/api/layer/list":                 "abtest.layer.all",
	"/api/layer/downlist":             "abtest.layer.all",
	"/api/layer/down":                 "abtest.layer.all",
	"/api/layer/edit":                 "abtest.layer.all",
	"/api/exp/verify-exp-name":        "",
	"/api/exp/save-base":              "",
	"/api/exp/save-strategy":          "",
	"/api/exp/save-groups":            "",
	"/api/exp/save":                   "",
	"/api/exp/split-exp-list":         "",
	"/api/exp/split-group-list":       "",
	"/api/exp/batch-all":              "",
	"/api/exp-tools/jsonfile-to-exp":  "",
	"/api/exp/tag-all":                "",
	"/api/exp/upload-groups":          "",
	"/api/exp/change-state":           "",
	"/api/exp/detail":                 "",
	"/api/exp/list-header":            "",
	"/api/exp/delete":                 "",
	"/api/exp/list":                   "abtest.exp.list",
	"/api/exp/list-draft":             "",
	"/api/exp/edit-save":              "",
	"/api/exp/layerlist":              "",
	"/api/exp/exp-with-layer":         "",
	"/api/strategy/create":            "",
	"/api/strategy/list":              "",
	"/api/strategy/detail":            "abtest.exp.view",
	"/api/strategy/filter/list":       "",
	"/api/batch/create":               "",
	"/api/batch/update":               "",
	"/api/batch/delete":               "",
	"/api/batch/list":                 "",
	"/api/exp/log/list":               "",
	"/api/exp/snapshot/detail":        "",
	"/api/exp/log/all":                "",
	"/api/auth/state":                 "",
	"/admin-api/auth/state":           "",
	"/api/tools/auto-complete":        "",
	"/api/exp/exp-for-strategy":       "",
	"/api/exp/group-for-strategy":     "",
	"/api/strategy/filter/expression": "",
	"/api/exp/set-white":              "",
}

func IsLoginAndPermissions() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 是否是超管
		isAdmin := auth.IsAdmin(c)
		if isAdmin {
			return
		}

		// 权限验证
		path := c.Request.URL.Path
		perm, ok := Permissions[path]
		if !ok { // 有可能是漏配
			//slog.WarnContext(c, "nopermap", "path", c.Request.URL.Path)
			//base.Abort(c, http.StatusForbidden, "该权限暂未开放")
			// 在新添加操作，但还不确定权限的情况下，也可以通过，但有可能漏配。
			// 目前配置权限较少，所以暂时全部放开。
			return
		}

		if perm == "" { // 豁免。例如 projectList 接口走这里
			return
		}

		projectId := auth.GetProjectId(c)
		if projectId < 1 {
			base.Abort(c, http.StatusBadRequest, "缺少 project id")
			return
		}

		noNeedPrjId := c.Request.URL.Path == "/api/auth/state" && projectId == 9999
		needPrjid := !noNeedPrjId
		if needPrjid {
			if !auth.InProject(c, projectId) {
				base.Abort(c, http.StatusForbidden, "没有相应的项目权限，请联系管理员")
				return
			}

			if !auth.CanName(c, perm) {
				base.Abort(c, http.StatusForbidden, "没有相应的权限，请联系管理员")
				return
			}
		}

		//请求处理
		c.Next()
	}
}
