package middleware

import (
	"fmt"
	"time"

	"math/rand/v2"

	"github.com/gin-gonic/gin"
)

func TraceID() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从 header 中获取 trace_id
		traceID := c.GetHeader("X-Trace-ID")

		// 如果 header 中没有 trace_id，生成一个新的
		if traceID == "" {
			traceID = generateTraceID()
		}

		// 将 trace_id 存储到 context 中
		c.Set("trace_id", traceID)

		// 继续处理请求
		c.Next()
	}
}

// generateTraceID 生成一个唯一的 trace_id
func generateTraceID() string {
	now := time.Now()
	// 格式化日期时间，例如：20230405150405
	dateTime := now.Format("20060102150405")
	// 生成6位随机数
	randomNum := rand.IntN(1000000)
	// 组合日期时间和随机数
	return fmt.Sprintf("%s%06d", dateTime, randomNum)
}
