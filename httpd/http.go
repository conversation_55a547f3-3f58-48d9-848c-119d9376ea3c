package httpd

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"github.com/getsentry/sentry-go"
	"github.com/gin-contrib/cors"
	"github.com/gin-contrib/pprof"
	"github.com/spf13/cast"

	"git.7k7k.com/data/abAdmin/gopkg/sso"
	"git.7k7k.com/data/abAdmin/httpd/base"
	"git.7k7k.com/data/abAdmin/httpd/middleware"
	"git.7k7k.com/data/abAdmin/infra"
	"git.7k7k.com/data/abAdmin/service"
	"git.7k7k.com/data/abAdmin/service/oapi"
	"git.7k7k.com/pkg/common/ctxs"
	"git.7k7k.com/pkg/common/gosentry"
	middleware2 "git.7k7k.com/pkg/common/http/middleware"
	sentrygin "github.com/getsentry/sentry-go/gin"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"gorm.io/gorm"
)

// @autowire(set=http)
type Handlers struct {
	ProjectController
	LayerController
	ExpController
	TagController
	StrategyController
	BatchController
	GroupController
	ExpLogController
	ExpToolsController
	ApprovalRuleController
	ApprovalTaskController

	LayerService        *service.LayerService
	ExpExtraService     *service.ExpExtraService
	AuthService         *service.AuthService
	AutoCompleteService *service.AutoCompleteService
	DingtalkCmd         *service.DingtalkCommandService

	SSOService *sso.SSOService

	Query *service.Query

	OpenAPI *OpenAPI
}

// @autowire(set=http)
type OpenAPI struct {
	ExpService *oapi.ExpService
}

// @autowire(set=http)
func NewHTTPServer(config *infra.Config, handlers *Handlers) (*http.Server, func()) {
	// g := gin.Default()
	g := gin.New()
	g.Use(
		cors.Default(),
		middleware.ContextKeyValue(),
		middleware2.LogAndMetric(),
		middleware2.OpenTracing(),
		middleware.Recover(),
		sentrygin.New(sentrygin.Options{Repanic: true}),
		func(gc *gin.Context) {

			gc.Next() // UserID 在 Auth 里才获取到

			tx := gosentry.RootSpan(gc)
			if tx == nil {
				return
			}
			tx = tx.GetTransaction()

			ctx := tx.Context()
			hub := sentry.GetHubFromContext(ctx)
			hub.Scope().SetUser(sentry.User{
				// ID: strconv.Itoa(auth.GetUserId(gc)),
				ID: ctxs.UserId(gc),
			})
		},
	)

	// dump.Dump(config)
	g.GET("/ping", func(c *gin.Context) { c.JSON(http.StatusOK, gin.H{"message": "pong"}) })
	g.GET("/metrics", gin.WrapH(promhttp.Handler()))
	g.GET("/debug/sleep", SleepAPI)
	pprof.Register(g, "/debug/pprof")

	//
	// 业务接口
	// {"code": 200, "message": "", "data": {}}
	//

	handlers.SSOService.RegisterRoutes(g)

	api := g.Group("api", middleware.AuthCheck([]byte(config.JWT.Key), config.JWT.Admin), middleware.IsLoginAndPermissions())
	routeAdmin(api, handlers)

	adminApi := g.Group("admin-api", middleware.AuthCheck([]byte(config.JWT.Key), config.JWT.Admin), middleware.IsLoginAndPermissions())
	routeAdmin(adminApi, handlers)

	internalAPI := g.Group("internal-api", middleware.AuthInternal())
	routeAdmin(internalAPI, handlers)

	openapi := g.Group("openapi")
	routeOpenApi(openapi, handlers)

	pprof.RouteRegister(g, "pprof")

	g.POST("/abtesting/dingtalk/outgoing", DingtalkOutgoing(handlers.DingtalkCmd))

	srv := &http.Server{
		Addr:    config.Addr,
		Handler: g,
	}

	cleanup := func() {
		ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
		defer cancel()

		err := srv.Shutdown(ctx)
		if err != nil {
			log.Println("Server Shutdown", err)
		}
	}

	return srv, cleanup
}

func routeOpenApi(api gin.IRouter, handlers *Handlers) {

	Route(api, "GET", "/exp/list", handlers.OpenAPI.ExpService.List)

	Route(api, "POST", "/exp/create", handlers.ExpExtraService.Create)
	Route(api, "POST", "/exp/update", handlers.ExpExtraService.Update)
	Route(api, "POST", "/exp/change-status", handlers.ExpService.ChangeState)
	Route(api, "POST", "/layer/create", handlers.LayerService.Add)
	Route(api, "POST", "/layer/list", handlers.LayerService.List)
}

func routeAdmin(api gin.IRouter, handlers *Handlers) {
	// Auth
	Route(api, "GET", "/user/list", handlers.AuthService.ListUser)
	Route(api, "POST", "/user/save", handlers.AuthService.SaveUserInfo)
	Route(api, "GET", "/user/sync", handlers.AuthService.SyncUser)
	Route(api, "GET", "/auth/state", handlers.AuthService.State)

	Route(api, "GET", "/auth/show-cache", handlers.AuthService.ShowCache)

	Route(api, "GET", "/tools/auto-complete", handlers.AutoCompleteService.List)
	//Route(api, "POST", "/exp/set-white", handlers.ExpService.SetWhite)

	// 项目相关
	api.POST("/project/create", handlers.ProjectController.Add)
	api.POST("/project/list", handlers.ProjectController.List)
	api.POST("/project/downlist", handlers.ProjectController.DownList)
	api.POST("/project/edit", handlers.ProjectController.Edit)
	api.POST("/project/down", handlers.ProjectController.Down)
	// 标签相关
	api.POST("/tag/create", handlers.TagController.Add)
	api.POST("/tag/list", handlers.TagController.List)
	api.GET("/tag/delete", handlers.TagController.Delete)
	api.POST("/tag/edit", handlers.TagController.Edit)
	// 分层相关
	api.POST("/layer/create", handlers.LayerController.Add)
	api.POST("/layer/list", handlers.LayerController.List)
	api.POST("/layer/downlist", handlers.LayerController.DownList)
	api.POST("/layer/down", handlers.LayerController.Down)
	api.POST("/layer/edit", handlers.LayerController.Edit)
	// 实验主流程相关
	api.POST("/exp/save-base", handlers.ExpController.SaveBase)
	api.POST("/exp/save-strategy", handlers.ExpController.SaveStrategy)
	api.POST("/exp/save-groups", handlers.ExpController.SaveGroups)
	api.POST("/exp/save", handlers.ExpController.Save)
	// 导出excel
	api.POST("/exp/split-exp-list", handlers.ExpController.CanSplitExpList)
	api.POST("/exp/split-group-list", handlers.ExpController.CanSplitGroupList)
	api.GET("/exp/batch-all", handlers.ExpController.GetBatchList)
	api.GET("/exp/tag-all", handlers.ExpController.GetTagList)
	api.POST("/exp/upload-groups", handlers.ExpController.UploadGroups)
	api.POST("/exp/change-state", handlers.ExpController.ChangeState)
	api.GET("/exp/list-header", handlers.ExpController.ListHeader)
	api.POST("/exp/delete", handlers.ExpController.Delete)
	api.GET("/exp/detail", handlers.ExpController.Detail)
	api.GET("/exp/exp-for-strategy", handlers.ExpController.ExpForStrategy)
	api.POST("/exp/download", handlers.ExpController.Download)
	api.POST("/exp/upload", handlers.ExpController.Upload)
	// 实验日常工具
	api.POST("/exp-tools/jsonfile-to-exp", handlers.ExpToolsController.JsonFileToExpList)

	api.POST("/exp/list", handlers.ExpController.List)
	api.POST("/exp/set-white", handlers.ExpController.SetWhite)
	api.POST("/exp/list-draft", handlers.ExpController.DraftList)
	api.POST("/exp/edit-save", handlers.ExpController.EditSave)
	api.GET("/exp/layerlist", handlers.LayerController.All)
	api.GET("/exp/exp-with-layer", handlers.ExpController.ExpWithLayer)

	// 实验策略相关
	api.POST("/strategy/create", handlers.StrategyController.Create)
	api.GET("/strategy/list", handlers.StrategyController.List)
	api.GET("/strategy/detail", handlers.StrategyController.Detail)
	api.GET("/strategy/filter/list", handlers.StrategyController.FilterList)
	api.POST("/strategy/filter/expression", handlers.StrategyController.FilterExpression)
	// 批次
	api.POST("/batch/create", handlers.BatchController.Create)
	api.PUT("/batch/update", handlers.BatchController.Update)
	api.PATCH("/batch/delete", handlers.BatchController.Delete)
	api.GET("/batch/list", handlers.BatchController.List)

	//	实验操作记录（实验日志&实验快照）
	api.GET("/exp/log/list", handlers.ExpLogController.List)
	api.GET("/exp/snapshot/detail", handlers.ExpLogController.Detail)
	api.GET("/exp/log/all", handlers.ExpLogController.All)
	// 审批规则管理
	api.POST("/approval-rule/list", handlers.ApprovalRuleController.List)
	api.POST("/approval-rule/create", handlers.ApprovalRuleController.Create)
	api.POST("/approval-rule/update", handlers.ApprovalRuleController.Update)
	api.GET("/approval-rule/delete", handlers.ApprovalRuleController.Delete)
	api.POST("/approval-rule/switch", handlers.ApprovalRuleController.SwitchApprovalRule)
	api.GET("/approval-rule/node-list", handlers.ApprovalRuleController.GetApprovalNodeList)
	// 审批任务
	api.POST("/approval-task/list", handlers.ApprovalTaskController.List)
	api.GET("/approval-task/log", handlers.ApprovalTaskController.Log)
	api.GET("/approval-task/changes", handlers.ApprovalTaskController.Changes)
	api.POST("/approval-task/approval", handlers.ApprovalTaskController.Approval)
	api.GET("/approval-task/revoke", handlers.ApprovalTaskController.Revoke)

}
func RouteApi(g gin.IRouter, method string, relativePath string, f func(*gin.Context)) {
	g.Handle(method, relativePath, func(ctx *gin.Context) {
		var input any
		if err := ctx.ShouldBind(&input); err != nil {
			base.WriteErrMsg(ctx, http.StatusBadRequest, err.Error())
			return
		}
		var expId int64
		if method == "GET" {
			expId = cast.ToInt64(ctx.Query("exp_id"))
		}
		fmt.Print(expId)
		if input != nil {
			paramsMap, isOk := input.(map[string]interface{})
			if isOk {
				if _, ok := paramsMap["exp_id"]; ok {
					expId = cast.ToInt64(paramsMap["exp_id"])
				}
			}
		}
		if expId != 0 {
			// 校验权限

		}
	})
}

func Route[I, O any](g gin.IRouter, method string, relativePath string, f func(context.Context, I) (O, error)) {
	g.Handle(method, relativePath, func(gc *gin.Context) {
		var input I
		if err := gc.ShouldBind(&input); err != nil {
			base.WriteErrMsg(gc, http.StatusBadRequest, err.Error())
			return
		}

		inputPtr := &input
		if fromForm, ok := (any)(inputPtr).(FromFormer); ok {
			query, _ := Form2Query(gc.Request.Form)
			fromForm.FromForm(query)
		}

		validate := validator.New()
		if err := validate.StructCtx(gc, input); err != nil {
			base.WriteErrMsg(gc, http.StatusBadRequest, err.Error())
			return
		}

		output, err := f(gc, input)

		if err != nil {
			base.WriteErrMsg(gc, http.StatusInternalServerError, err.Error())
			return
		}

		base.WriteOk(gc, output)
	})
}

type FromFormer interface {
	FromForm(query *gorm.DB)
}

func Form2Query(form url.Values) (query *gorm.DB, err error) {
	return
}

func SleepAPI(gc *gin.Context) {
	value := gc.Query("sleep")
	ms, _ := strconv.Atoi(value)
	time.Sleep(time.Millisecond * time.Duration(ms))
}
