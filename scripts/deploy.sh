#!/bin/bash

# 停止现有服务
sudo systemctl stop abAdmin || true

# 复制服务文件
sudo cp scripts/abAdmin.service /etc/systemd/system/

# 安装 logrotate 配置
sudo cp scripts/abAdmin.logrotate /etc/logrotate.d/
sudo chown root:root /etc/logrotate.d/abAdmin.logrotate
sudo chmod 644 /etc/logrotate.d/abAdmin.logrotate

# 重新加载 systemd
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start abAdmin

# 设置开机自启
sudo systemctl enable abAdmin

# 检查服务状态
sudo systemctl status abAdmin
