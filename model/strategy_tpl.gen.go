// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameStrategyTpl = "strategy_tpl"

// StrategyTpl 策略模版表
type StrategyTpl struct {
	ID         int64     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	Name       string    `gorm:"column:name;not null;comment:策略名称" json:"name"`                         // 策略名称
	ProjectID  int64     `gorm:"column:project_id;not null;comment:ab_project关联id" json:"project_id"`   // ab_project关联id
	TplType    int32     `gorm:"column:tpl_type;not null;comment:类型：1 触发用户规则 4 分流用户规则" json:"tpl_type"` // 类型：1 触发用户规则 4 分流用户规则
	TplData    string    `gorm:"column:tpl_data;not null;comment:模版内容" json:"tpl_data"`                 // 模版内容
	State      int32     `gorm:"column:state;not null;default:2;comment:状态：1删除 2启用 3禁用" json:"state"`   // 状态：1删除 2启用 3禁用
	CreateTime time.Time `gorm:"column:create_time;not null;comment:创建时间" json:"create_time"`           // 创建时间
	UpdateTime time.Time `gorm:"column:update_time;not null;comment:更新时间" json:"update_time"`           // 更新时间
}

// TableName StrategyTpl's table name
func (*StrategyTpl) TableName() string {
	return TableNameStrategyTpl
}
