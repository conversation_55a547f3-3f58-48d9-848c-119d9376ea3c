// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameSplitGroup = "split_group"

// SplitGroup 实验组分流表
type SplitGroup struct {
	ID            int64     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	FromExpID     int64     `gorm:"column:from_exp_id;not null;comment:分流实验id" json:"from_exp_id"`                         // 分流实验id
	FromExpName   string    `gorm:"column:from_exp_name;not null;comment:实验名称" json:"from_exp_name"`                       // 实验名称
	FromGroupID   int64     `gorm:"column:from_group_id;not null;comment:分流实验组id" json:"from_group_id"`                    // 分流实验组id
	FromGroupName string    `gorm:"column:from_group_name;not null;comment:实验组名称" json:"from_group_name"`                  // 实验组名称
	ToExpID       int64     `gorm:"column:to_exp_id;not null;comment:到实验id" json:"to_exp_id"`                              // 到实验id
	ToExpName     string    `gorm:"column:to_exp_name;not null;comment:到实验名称" json:"to_exp_name"`                          // 到实验名称
	ToGroupID     int64     `gorm:"column:to_group_id;not null;comment:到实验组id" json:"to_group_id"`                         // 到实验组id
	ToGroupName   string    `gorm:"column:to_group_name;not null;comment:实验组名称" json:"to_group_name"`                      // 实验组名称
	Rate          int32     `gorm:"column:rate;not null;comment:分流占比" json:"rate"`                                         // 分流占比
	Cnt           int32     `gorm:"column:cnt;not null;comment:需要迁移人数" json:"cnt"`                                         // 需要迁移人数
	SplitType     int32     `gorm:"column:split_type;not null;comment:分流类型：1关闭 2重定向 3运行中重定向" json:"split_type"`            // 分流类型：1关闭 2重定向 3运行中重定向
	Desc          string    `gorm:"column:desc;not null;comment:描述" json:"desc"`                                           // 描述
	State         int32     `gorm:"column:state;not null;default:1;comment:状态：1正常 2删除 3导入完成" json:"state"`                 // 状态：1正常 2删除 3导入完成
	CreateTime    time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"` // 创建时间
	UpdateTime    time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"` // 更新时间
}

// TableName SplitGroup's table name
func (*SplitGroup) TableName() string {
	return TableNameSplitGroup
}
