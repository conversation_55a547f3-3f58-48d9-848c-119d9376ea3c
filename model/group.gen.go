// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameGroup = "group"

// Group 实验组/方案表
type Group struct {
	ID            int64     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	ExpID         int64     `gorm:"column:exp_id;not null;comment:ab_exp关联id" json:"exp_id"`                                    // ab_exp关联id
	GroupType     int32     `gorm:"column:group_type;not null;comment:实验组类型：1对照 2实验" json:"group_type"`                         // 实验组类型：1对照 2实验
	Name          string    `gorm:"column:name;not null;comment:实验组名称" json:"name"`                                             // 实验组名称
	ParamsType    int32     `gorm:"column:params_type;not null;comment:参数类型：1 json" json:"params_type"`                         // 参数类型：1 json
	Params        string    `gorm:"column:params;not null;comment:参数key" json:"params"`                                         // 参数key
	ParamsContent string    `gorm:"column:params_content;not null;comment:参数内容" json:"params_content"`                          // 参数内容
	WhiteList     string    `gorm:"column:white_list;not null;comment:白名单" json:"white_list"`                                   // 白名单
	IsProgress    int32     `gorm:"column:is_progress;not null;comment:是否进量 是1 否2" json:"is_progress"`                          // 是否进量 是1 否2
	BatchID       int64     `gorm:"column:batch_id;not null;comment:ab_batch关联id" json:"batch_id"`                              // ab_batch关联id
	Desc          string    `gorm:"column:desc;not null;comment:描述" json:"desc"`                                                // 描述
	State         int32     `gorm:"column:state;not null;default:1;comment:实验组状态：1:草稿 2:待开始，3:运行中 4:运行中不进量 5:已关闭" json:"state"` // 实验组状态：1:草稿 2:待开始，3:运行中 4:运行中不进量 5:已关闭
	Data          GroupData `gorm:"column:data;default:'{}';serializer:json" json:"data"`
	IsDeleted     int32     `gorm:"column:is_deleted;not null;default:2;comment:1禁用 2启用" json:"is_deleted"` // 1禁用 2启用
	CreateTime    time.Time `gorm:"column:create_time;not null;comment:创建时间" json:"create_time"`            // 创建时间
	UpdateTime    time.Time `gorm:"column:update_time;not null;comment:更新时间" json:"update_time"`            // 更新时间
}

// TableName Group's table name
func (*Group) TableName() string {
	return TableNameGroup
}
