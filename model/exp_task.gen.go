// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameExpTask = "exp_task"

// ExpTask 实验任务表
type ExpTask struct {
	ID                   int64      `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	Name                 string     `gorm:"column:name;not null;comment:任务名称" json:"name"`                                       // 任务名称
	ExpID                int64      `gorm:"column:exp_id;not null;comment:实验ID" json:"exp_id"`                                   // 实验ID
	ExpStatus            int32      `gorm:"column:exp_status;not null;comment:实验状态" json:"exp_status"`                           // 实验状态
	PlannedExecutionTime time.Time  `gorm:"column:planned_execution_time;not null;comment:计划执行时间" json:"planned_execution_time"` // 计划执行时间
	ActualExecutionTime  *time.Time `gorm:"column:actual_execution_time;comment:实际执行时间" json:"actual_execution_time"`            // 实际执行时间
	CreateTime           time.Time  `gorm:"column:create_time;not null;comment:创建时间" json:"create_time"`                         // 创建时间
	UpdateTime           time.Time  `gorm:"column:update_time;not null;comment:更新时间" json:"update_time"`                         // 更新时间
	Desc                 string     `gorm:"column:desc;not null;comment:执行备注" json:"desc"`                                       // 执行备注
	Status               int32      `gorm:"column:status;not null;comment:任务状态 1未开始 2执行中 3取消 4延迟 5已完成 6失败" json:"status"`        // 任务状态 1未开始 2执行中 3取消 4延迟 5已完成 6失败
}

// TableName ExpTask's table name
func (*ExpTask) TableName() string {
	return TableNameExpTask
}
