// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameBatch = "batch"

// Batch 批次表
type Batch struct {
	ID         int64     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	Name       string    `gorm:"column:name;not null;comment:批次名称" json:"name"`                               // 批次名称
	ProjectID  int64     `gorm:"column:project_id;not null;comment:ab_project关联id" json:"project_id"`         // ab_project关联id
	Desc       string    `gorm:"column:desc;not null;comment:批次描述" json:"desc"`                               // 批次描述
	CreateTime time.Time `gorm:"column:create_time;not null;comment:创建时间" json:"create_time"`                 // 创建时间
	UpdateTime time.Time `gorm:"column:update_time;not null;comment:更新时间" json:"update_time"`                 // 更新时间
	IsDeleted  int32     `gorm:"column:is_deleted;not null;default:2;comment:状态：1已删除 2未删除" json:"is_deleted"` // 状态：1已删除 2未删除
}

// TableName Batch's table name
func (*Batch) TableName() string {
	return TableNameBatch
}
