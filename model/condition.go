package model

import (
	"fmt"
	"git.7k7k.com/data/abAdmin/gopkg/condition"
	"github.com/spf13/cast"
	"strings"
)

type Condition = condition.Filter

type Strategy struct {
	TriggerUserKey *Condition `json:"trigger_user_key,omitempty"` // 触发用户规则
	TriggerOnce    bool       `json:"trigger_once"`               // 是否只触发一次：进入实验后不再判断触发规则
	TriggerEndKey  *Condition `json:"trigger_end_key,omitempty"`  // 关闭触发规则
	TriggerStopKey *Condition `json:"trigger_stop_key,omitempty"` // 停止进量触发规则
}

// CSRData crs的数据结构
type CSRData struct {
	Key   string              `json:"key"`
	Value interface{}         `json:"value"`
	Type  condition.ValueType `json:"type,omitempty"`
}

func (c *CSRData) GenerateExpression() string {
	if c.Value == nil {
		return ""
	}
	val := strings.Join(c.getValue(), ",")
	ret := fmt.Sprintf("%s:%s", c.Key, val)
	return ret
}

func (c *CSRData) getValue() []string {
	switch c.Type {
	case condition.INT, condition.FLOAT:
		return nil
	case condition.BOOL:
		return nil
	case condition.STRING, condition.StringVersion:
		return nil
	case condition.ListInt, condition.ListString, condition.ListStringDefault, condition.RangeInt, condition.RangeFloat, condition.RangeIntDefault:
		ret := make([]string, 0)
		for _, f := range cast.ToStringSlice(c.Value) {
			if len(f) == 0 {
				continue
			}
			ret = append(ret, f)
		}
		return ret
	case condition.Timestamp:
		return nil
	default:
		return nil
	}
}
