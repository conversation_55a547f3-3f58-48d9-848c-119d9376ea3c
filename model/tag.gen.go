// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTag = "tag"

// Tag 标签表
type Tag struct {
	ID         int64     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	ProjectID  int64     `gorm:"column:project_id;not null;default:2;comment:项目id" json:"project_id"`       // 项目id
	Name       string    `gorm:"column:name;not null;comment:标签名称" json:"name"`                             // 标签名称
	IsDeleted  int32     `gorm:"column:is_deleted;not null;default:2;comment:状态：1禁用 2启用" json:"is_deleted"` // 状态：1禁用 2启用
	CreateTime time.Time `gorm:"column:create_time;not null;comment:创建时间" json:"create_time"`               // 创建时间
	UpdateTime time.Time `gorm:"column:update_time;not null;comment:更新时间" json:"update_time"`               // 更新时间
}

// TableName Tag's table name
func (*Tag) TableName() string {
	return TableNameTag
}
