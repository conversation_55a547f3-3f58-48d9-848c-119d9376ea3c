// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameParamsType = "params_type"

// ParamsType 参数类型表
type ParamsType struct {
	ID         int64     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	Name       string    `gorm:"column:name;not null;comment:参数名称" json:"name"`                        // 参数名称
	State      int32     `gorm:"column:state;not null;default:1;comment:状态：-1删除 0禁用 1启用" json:"state"` // 状态：-1删除 0禁用 1启用
	CreateTime time.Time `gorm:"column:create_time;not null;comment:创建时间" json:"create_time"`          // 创建时间
	UpdateTime time.Time `gorm:"column:update_time;not null;comment:更新时间" json:"update_time"`          // 更新时间
}

// TableName ParamsType's table name
func (*ParamsType) TableName() string {
	return TableNameParamsType
}
