package logic_test

import (
	"encoding/json"
	"reflect"
	"slices"
	"testing"

	"git.7k7k.com/data/abAdmin/model/logic"
	"github.com/stretchr/testify/assert"
)

func TestBlockGroupParamStyleProcesser_RemoveFeature(t *testing.T) {
	tests := []struct {
		name string // description of this test case
		// Named input parameters for target function.
		data        string
		featureKeys []string
		featureIDs  []string
		want        string
	}{
		{
			name:        "test",
			data:        `{"conditions":[],"features": [{"id":1001, "score":3.4}, {"id":2001}, {"id":3001}]}`,
			featureKeys: []string{},
			featureIDs:  []string{"2001"},
			want:        `{"conditions":[],"features":[{"id":1001,"score":3.4},{"id":3001}]}`,
		},
		{
			name:        "test with exclude_ids",
			data:        `{"conditions":[],"features":[{"id":1001,"score":3.4}],"exclude_ids":[2001,3001]}`,
			featureKeys: []string{},
			featureIDs:  []string{"2001"},
			want:        `{"conditions":[],"features":[{"id":1001,"score":3.4}],"exclude_ids":[2001,3001]}`,
		},
		{
			name:        "empty exclude_ids",
			data:        `{"conditions":[],"features":[{"id":1001,"score":3.4},{"id":2001}],"exclude_ids":[]}`,
			featureKeys: []string{},
			featureIDs:  []string{"2001"},
			want:        `{"conditions":[],"features":[{"id":1001,"score":3.4}],"exclude_ids":[]}`,
		},
		{
			name:        "no exclude_ids field",
			data:        `{"conditions":[],"features":[{"id":1001,"score":3.4},{"id":2001}]}`,
			featureKeys: []string{},
			featureIDs:  []string{"2001"},
			want:        `{"conditions":[],"features":[{"id":1001,"score":3.4}]}`,
		},
		{
			name:        "id in both features and exclude_ids",
			data:        `{"conditions":[],"features":[{"id":1001},{"id":2001}],"exclude_ids":[2001,3001]}`,
			featureKeys: []string{},
			featureIDs:  []string{"2001"},
			want:        `{"conditions":[],"features":[{"id":1001}],"exclude_ids":[2001,3001]}`,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			data := make(map[string]any)
			err := json.Unmarshal([]byte(tt.data), &data)
			if err != nil {
				t.Fatalf("unmarshal data error: %v", err)
			}

			var b logic.BlockGroupParamStyleProcessor
			got := b.RemoveFeature(data, tt.featureKeys, tt.featureIDs)
			gotJSON, err := json.Marshal(got)
			if err != nil {
				t.Fatalf("marshal got error: %v", err)
			}

			assert.JSONEq(t, tt.want, string(gotJSON))
		})
	}
}

func TestGeneralGroupParamStyleProcesser_GetFeatureKeys(t *testing.T) {
	tests := []struct {
		name string
		data string
		want []string
	}{
		{
			name: "empty object",
			data: `{}`,
			want: []string{},
		},
		{
			name: "single key",
			data: `{"key1": "value1"}`,
			want: []string{"key1"},
		},
		{
			name: "multiple keys",
			data: `{"key1": "value1", "key2": 123, "key3": true}`,
			want: []string{"key1", "key2", "key3"},
		},
		{
			name: "nested objects",
			data: `{"key1": {"nested": "value"}, "key2": [1,2,3]}`,
			want: []string{"key1.nested", "key2"},
		},
		{
			name: "deep nested objects",
			data: `{"key1": {"nested": {"deep": "value"}}, "key2": {"a": 1, "b": 2}}`,
			want: []string{"key1.nested.deep", "key2.a", "key2.b"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			g := logic.GeneralGroupParamStyleProcessor{}
			got := g.GetFeatureKeys(tt.data)

			// 由于 KeysOfDict 返回的顺序可能不固定，我们需要先排序再比较
			if len(got) != len(tt.want) {
				t.Errorf("GetFeatureKeys() got length = %v, want length %v", len(got), len(tt.want))
				return
			}

			// 创建一个 map 来检查所有期望的 key 是否都存在
			wantMap := make(map[string]bool)
			for _, w := range tt.want {
				wantMap[w] = true
			}

			for _, g := range got {
				if !wantMap[g] {
					t.Errorf("GetFeatureKeys() got unexpected key %v", g)
				}
				delete(wantMap, g)
			}

			if len(wantMap) > 0 {
				t.Errorf("GetFeatureKeys() missing expected keys: %v", wantMap)
			}
		})
	}
}

func TestGeneralGroupParamStyleProcesser_RemoveFeature(t *testing.T) {
	tests := []struct {
		name        string
		data        map[string]any
		featureKeys []string
		featureIDs  []string
		want        map[string]any
	}{
		{
			name: "remove simple key",
			data: map[string]any{
				"key1": "value1",
				"key2": "value2",
			},
			featureKeys: []string{"key1"},
			want: map[string]any{
				"key2": "value2",
			},
		},
		{
			name: "remove nested key",
			data: map[string]any{
				"key1": map[string]any{
					"nested": "value",
				},
				"key2": "value2",
			},
			featureKeys: []string{"key1.nested"},
			want: map[string]any{
				"key1": map[string]any{},
				"key2": "value2",
			},
		},
		{
			name: "remove multiple keys",
			data: map[string]any{
				"key1": map[string]any{
					"nested": "value",
				},
				"key2": map[string]any{
					"a": 1,
					"b": 2,
				},
			},
			featureKeys: []string{"key1.nested", "key2.a"},
			want: map[string]any{
				"key1": map[string]any{},
				"key2": map[string]any{
					"b": 2,
				},
			},
		},
		{
			name: "remove non-existent key",
			data: map[string]any{
				"key1": "value1",
			},
			featureKeys: []string{"key2"},
			want: map[string]any{
				"key1": "value1",
			},
		},
		{
			name:        "empty input",
			data:        map[string]any{},
			featureKeys: []string{"key1"},
			want:        map[string]any{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			g := logic.GeneralGroupParamStyleProcessor{}
			got := g.RemoveFeature(tt.data, tt.featureKeys, tt.featureIDs)

			// 将结果转换为 JSON 字符串进行比较，避免 map 直接比较的问题
			gotJSON, err := json.Marshal(got)
			if err != nil {
				t.Fatalf("failed to marshal got: %v", err)
			}
			wantJSON, err := json.Marshal(tt.want)
			if err != nil {
				t.Fatalf("failed to marshal want: %v", err)
			}

			if string(gotJSON) != string(wantJSON) {
				t.Errorf("RemoveFeature() = %v, want %v", string(gotJSON), string(wantJSON))
			}
		})
	}
}

func TestBlockGroupParamStyleProcessor_ConflictLabel(t *testing.T) {
	tests := []struct {
		name    string
		data    string
		keyOrID string
		want    string
	}{
		{
			name:    "feature exists",
			data:    `{"features": [{"id": 1001, "name": "test1"}, {"id": 2001, "name": "test2"}]}`,
			keyOrID: "1",
			want:    "1001",
		},
		{
			name:    "feature not exists",
			data:    `{"features": [{"id": 2001, "name": "test2"}]}`,
			keyOrID: "1",
			want:    "1",
		},
		{
			name:    "empty features",
			data:    `{"features": []}`,
			keyOrID: "1",
			want:    "1",
		},
		{
			name:    "invalid feature data",
			data:    `{"features": [{"name": "test1"}]}`,
			keyOrID: "1",
			want:    "1",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			b := logic.BlockGroupParamStyleProcessor{}
			got := b.ConflictLabel(tt.data, tt.keyOrID)
			if got != tt.want {
				t.Errorf("ConflictLabel() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGeneralGroupParamStyleProcessor_ConflictLabel(t *testing.T) {
	tests := []struct {
		name    string
		data    string
		keyOrID string
		want    string
	}{
		{
			name:    "simple string value",
			data:    `{"key1": "value1", "key2": "value2"}`,
			keyOrID: "key1",
			want:    `{"key1":"value1"}`,
		},
		{
			name:    "number value",
			data:    `{"key1": 123, "key2": "value2"}`,
			keyOrID: "key1",
			want:    `{"key1":123}`,
		},
		{
			name:    "boolean value",
			data:    `{"key1": true, "key2": "value2"}`,
			keyOrID: "key1",
			want:    `{"key1":true}`,
		},
		{
			name:    "object value",
			data:    `{"key1": {"nested": "value"}, "key2": "value2"}`,
			keyOrID: "key1",
			want:    `{"key1":{"nested":"value"}}`,
		},
		{
			name:    "array value",
			data:    `{"key1": [1,2,3], "key2": "value2"}`,
			keyOrID: "key1",
			want:    `{"key1":[1,2,3]}`,
		},
		{
			name:    "key not exists",
			data:    `{"key2": "value2"}`,
			keyOrID: "key1",
			want:    `{"key1":null}`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			g := logic.GeneralGroupParamStyleProcessor{}
			got := g.ConflictLabel(tt.data, tt.keyOrID)
			if got != tt.want {
				t.Errorf("ConflictLabel() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestBlockGroupParamStyleProcessor_Valid(t *testing.T) {
	tests := []struct {
		name    string
		data    string
		wantErr bool
		errMsg  string
	}{
		{
			name:    "valid data",
			data:    `{"features":[{"id":1001}],"exclude_ids":[2001]}`,
			wantErr: false,
		},
		{
			name:    "invalid feature id",
			data:    `{"features":[{"id":999}],"exclude_ids":[2001]}`,
			wantErr: true,
			errMsg:  "feature id is too short",
		},
		{
			name:    "feature id in exclude_ids",
			data:    `{"features":[{"id":1001}],"exclude_ids":[1001]}`,
			wantErr: true,
			errMsg:  "feature_id <1001> 不能同时出现在 exclude_ids 中",
		},
		{
			name:    "invalid exclude_id",
			data:    `{"features":[{"id":1001}],"exclude_ids":[999]}`,
			wantErr: true,
			errMsg:  "feature id is too short",
		},
		{
			name:    "no exclude_ids field",
			data:    `{"features":[{"id":1001}]}`,
			wantErr: false,
		},
		{
			name:    "empty exclude_ids",
			data:    `{"features":[{"id":1001}],"exclude_ids":[]}`,
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			b := logic.BlockGroupParamStyleProcessor{}
			err := b.Valid(tt.data)

			if (err != nil) != tt.wantErr {
				t.Errorf("Valid() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr && err != nil && err.Error() != tt.errMsg {
				t.Errorf("Valid() error message = %v, want %v", err.Error(), tt.errMsg)
			}
		})
	}
}

func TestBlockGroupParamStyleProcessor_GetFeatureIDs(t *testing.T) {
	tests := []struct {
		name string
		data string
		want []string
	}{
		{
			name: "features and exclude_ids",
			data: `{"features":[{"id":1001},{"id":2001}],"exclude_ids":[3001,4001]}`,
			want: []string{"1001", "2001", "3001", "4001"},
		},
		{
			name: "only features",
			data: `{"features":[{"id":1001},{"id":2001}]}`,
			want: []string{"1001", "2001"},
		},
		{
			name: "only exclude_ids",
			data: `{"features":[],"exclude_ids":[3001,4001]}`,
			want: []string{"3001", "4001"},
		},
		{
			name: "empty data",
			data: `{"features":[],"exclude_ids":[]}`,
			want: []string{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			b := logic.BlockGroupParamStyleProcessor{}
			got := b.GetFeatureIDs(tt.data)

			// 由于返回顺序可能不固定，需要排序后比较
			slices.Sort(got)
			slices.Sort(tt.want)

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetFeatureIDs() = %v, want %v", got, tt.want)
			}
		})
	}
}
