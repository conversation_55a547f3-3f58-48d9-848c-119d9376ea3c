package logic

import (
	"encoding/json"
	"errors"
	"fmt"
	"maps"
	"slices"
	"strconv"

	"git.7k7k.com/pkg/common/jsonutil"
	"github.com/emirpasic/gods/v2/sets/hashset"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
)

type GroupParamStyle string

func (s GroupParamStyle) String() string {
	return string(s)
}

func (s GroupParamStyle) Processor() GroupParamStyleProcessor {
	switch s {
	case "block":
		return BlockGroupParamStyleProcessor{} // 方块的特殊版本
	case "nop":
		return NopParamStyleProcessor{} // 最旧的版本，星云在用
	default:
		return GeneralGroupParamStyleProcessor{} // 通用版本，遵循 RFC7396
	}
}

type GroupParamStyleProcessor interface {
	Valid(data string) error
	GetFeatureKeys(data string) []string
	GetFeatureIDs(data string) []string
	RemoveFeature(data map[string]any, featureKeys []string, featureIDs []string) map[string]any
	ConflictLabel(data string, keyOrID string) string // 冲突显示标签
	IndexOf(data string, key string) any
	Merger() func(json1, json2 []byte) ([]byte, error)
}

// GeneralGroupParamStyleProcessor 标准JSON格式
type GeneralGroupParamStyleProcessor struct{}

// Merger implements GroupParamStyleProcessor.
func (g GeneralGroupParamStyleProcessor) Merger() func(json1 []byte, json2 []byte) ([]byte, error) {
	return jsonutil.MergeWithRFC7396
}

// ConflictLabel implements GroupParamStyleProcessor.
func (g GeneralGroupParamStyleProcessor) ConflictLabel(data string, keyOrID string) string {
	v := gjson.Get(data, keyOrID).Value()
	s := "{}"
	s, _ = sjson.Set(s, keyOrID, v)
	return s
}

// IndexOf implements GroupParamStyleProcessor.
func (g GeneralGroupParamStyleProcessor) IndexOf(data string, key string) any {
	return gjson.Get(data, key).Value()
}

// GetFeatureIDs implements GroupParamStyleProcessor.
func (g GeneralGroupParamStyleProcessor) GetFeatureIDs(data string) []string {
	return g.GetFeatureKeys(data)
}

// GetFeatureKeys implements GroupParamStyleProcessor.
func (g GeneralGroupParamStyleProcessor) GetFeatureKeys(data string) []string {
	keys, _ := jsonutil.KeysOfDict([]byte(data))
	return keys
}

// Valid implements GroupParamStyleProcessor.
func (g GeneralGroupParamStyleProcessor) Valid(data string) error {
	v := struct{}{}
	err := json.Unmarshal([]byte(data), &v)
	if err != nil {
		return err
	}
	return nil
}

// RemoveFeature implements GroupParamStyleProcessor.
func (g GeneralGroupParamStyleProcessor) RemoveFeature(data map[string]any, featureKeys []string, featureIDs []string) map[string]any {
	s, _ := jsonutil.Default.MarshalToString(data)
	for _, key := range featureKeys {
		s, _ = sjson.Delete(s, key)
	}
	result := make(map[string]any)
	jsonutil.Default.UnmarshalFromString(s, &result)
	return result
}

type BlockGroupParamStyleProcessor struct{}

// Merger implements GroupParamStyleProcessor.
func (b BlockGroupParamStyleProcessor) Merger() func(json1 []byte, json2 []byte) ([]byte, error) {
	return jsonutil.MergeWithQJSON
}

// ConflictLabel implements GroupParamStyleProcessor.
func (b BlockGroupParamStyleProcessor) ConflictLabel(data string, keyOrID string) string {
	v := b.IndexOf(data, keyOrID)
	if v == nil {
		return keyOrID
	}

	return cast.ToString(v.(map[string]any)["id"])
}

func (b BlockGroupParamStyleProcessor) Int2Key(featureID int) string {
	return b.Str2Key(strconv.Itoa(featureID))
}

func (b BlockGroupParamStyleProcessor) Str2Key(featureID string) string {
	if len(featureID) < 3 {
		return featureID + "_invalid_feature_id___"
	}
	return featureID[:len(featureID)-3]
}

// blockGroupParamStyleValid 只能校验，不能存储数据
type blockGroupParamStyleValid struct {
	Features []struct {
		ID int `json:"id"`
	} `json:"features"`
	ExcludeIDs []int `json:"exclude_ids"`
}

// Valid implements GroupParamStyleProcessor.
func (b BlockGroupParamStyleProcessor) Valid(data string) error {
	v := blockGroupParamStyleValid{}
	err := json.Unmarshal([]byte(data), &v)
	if err != nil {
		return err
	}

	excludeIDs := hashset.New(v.ExcludeIDs...)

	// 检查 exclude_ids 中的 ID 是否有效
	for _, id := range v.ExcludeIDs {
		if id < 1000 {
			return errors.New("feature id is too short")
		}
	}

	for _, f := range v.Features {
		if f.ID < 1000 {
			return errors.New("feature id is too short")
		}

		if excludeIDs.Contains(f.ID) {
			return fmt.Errorf("feature_id <%v> 不能同时出现在 exclude_ids 中", f.ID)
		}
	}

	return nil
}

// GetFeatureIDs implements GroupParamStyleProcessor.
func (b BlockGroupParamStyleProcessor) GetFeatureIDs(data string) []string {
	v := blockGroupParamStyleValid{}
	_ = json.Unmarshal([]byte(data), &v)
	ids := make([]string, 0, len(v.Features))
	for _, f := range v.Features {
		ids = append(ids, strconv.Itoa(f.ID))
	}
	for _, id := range v.ExcludeIDs {
		ids = append(ids, strconv.Itoa(id))
	}
	return ids
}

// GetFeatureKeys implements GroupParamStyleProcessor.
func (b BlockGroupParamStyleProcessor) GetFeatureKeys(data string) []string {
	v := blockGroupParamStyleValid{}
	_ = json.Unmarshal([]byte(data), &v)
	keys := make([]string, 0, len(v.Features))
	for _, f := range v.Features {
		keys = append(keys, b.Int2Key(f.ID))
	}
	return keys
}

// IndexOf implements GroupParamStyleProcessor.
func (b BlockGroupParamStyleProcessor) IndexOf(data string, key string) any {
	m := map[string]any{}
	_ = json.Unmarshal([]byte(data), &m)
	if features, ok := m["features"].([]any); ok {
		for _, v := range features {
			if feat, ok := v.(map[string]any); ok {
				if id := cast.ToInt(feat["id"]); id > 0 {
					if b.Int2Key(id) == key {
						return feat
					}
				}
			}
		}
	}
	return nil
}

// RemoveFeature implements GroupParamStyleProcessor.
func (b BlockGroupParamStyleProcessor) RemoveFeature(data map[string]any, featureKeys []string, featureIDs []string) map[string]any {
	result := maps.Clone(data)

	// TODO 改为 set 优化性能

	// 处理 features 数组
	if features, ok := result["features"].([]any); ok {
		list := make([]any, 0, len(features))
		for _, v := range features {
			if feat, ok := v.(map[string]any); ok {
				// 检查 ID 是否冲突
				id := cast.ToString(feat["id"])
				if id == "" {
					continue
				}

				// 检查特征 ID 是否在冲突列表中
				if slices.Contains(featureIDs, id) {
					continue // discard
				}

				// 检查特征 key 是否冲突
				key := b.Str2Key(id)
				keyConflict := false
				for _, fk := range featureKeys {
					if key == fk {
						keyConflict = true
						break
					}
				}
				if keyConflict {
					continue // discard
				}

				list = append(list, feat)
			}
		}
		result["features"] = list
	}

	return result
}

type NopParamStyleProcessor struct{}

// Merger implements GroupParamStyleProcessor.
func (n NopParamStyleProcessor) Merger() func(json1 []byte, json2 []byte) ([]byte, error) {
	return jsonutil.MergeWithQJSON
}

// ConflictLabel implements GroupParamStyleProcessor.
func (n NopParamStyleProcessor) ConflictLabel(data string, keyOrID string) string {
	return keyOrID
}

// GetFeatureIDs implements GroupParamStyleProcessor.
func (n NopParamStyleProcessor) GetFeatureIDs(data string) []string {
	return []string{}
}

// GetFeatureKeys implements GroupParamStyleProcessor.
func (n NopParamStyleProcessor) GetFeatureKeys(data string) []string {
	return []string{}
}

// IndexOf implements GroupParamStyleProcessor.
func (n NopParamStyleProcessor) IndexOf(data string, key string) any {
	return nil
}

// RemoveFeature implements GroupParamStyleProcessor.
func (n NopParamStyleProcessor) RemoveFeature(data map[string]any, featureKeys []string, featureIDs []string) map[string]any {
	return data
}

// Valid implements GroupParamStyleProcessor.
func (n NopParamStyleProcessor) Valid(data string) error {
	v := struct{}{}
	return json.Unmarshal([]byte(data), &v)
}

func IsConflictKey(key1 string, others ...string) bool {
	return lo.Contains(others, key1)
}

func IsConflictFeatureID(id1 string, id2 string, getRules func(string) []string) bool {
	return id1 == id2 || IsConflictKey(id2, getRules(id1)...) || IsConflictKey(id1, getRules(id2)...)
}
