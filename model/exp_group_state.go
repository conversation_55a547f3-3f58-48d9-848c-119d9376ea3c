package model

type ExpGroupStatus int32

// 实验组(方案)状态 1:草稿（默认值） 2:待开始，3:运行中 4:运行中不进量 5:已关闭
const (
	ExpGroupStatusDraft ExpGroupStatus = iota + 1
	ExpGroupStatusPending
	ExpGroupStatusRunning
	ExpGroupStatusRunningWithoutFlow
	ExpGroupStatusClosed
)

const (
	AllowFlow    = 1
	NotAllowFlow = 2
)

// Name 返回枚举值的字符串名称
func (s ExpGroupStatus) Name() string {
	switch s {
	case ExpGroupStatusDraft:
		return "草稿"
	case ExpGroupStatusPending:
		return "待启动"
	case ExpGroupStatusRunning:
		return "运行中"
	case ExpGroupStatusRunningWithoutFlow:
		return "运行中不进量"
	case ExpGroupStatusClosed:
		return "已关闭"
	default:
		return "Unknown"
	}
}
