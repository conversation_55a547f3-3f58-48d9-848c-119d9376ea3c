// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameProject = "project"

// Project 项目表
type Project struct {
	ID              int64                `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	UniqueID        string               `gorm:"column:unique_id;not null;comment:项目唯一标识" json:"unique_id"`                                    // 项目唯一标识
	Name            string               `gorm:"column:name;not null;comment:项目名称" json:"name"`                                                // 项目名称
	Desc            string               `gorm:"column:desc;not null;comment:项目描述" json:"desc"`                                                // 项目描述
	FeatureConflict FeatureConflictRules `gorm:"column:feature_conflict;default:'{}';comment:冲突列表;serializer:json" json:"feature_conflict"`    // 冲突列表
	FeatureStats    *FeatureStats        `gorm:"column:feature_stats;default:'{}';comment:记录feature占用状态;serializer:json" json:"feature_stats"` // 记录feature占用状态
	State           int32                `gorm:"column:state;not null;default:1;comment:状态：1在线 2下线" json:"state"`                              // 状态：1在线 2下线
	Data            ProjectData          `gorm:"column:data;default:'{}';comment:附加信息;serializer:json" json:"data"`                            // 附加信息
	DownTime        time.Time            `gorm:"column:down_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"down_time"`            // 创建时间
	CreateTime      time.Time            `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`        // 创建时间
	UpdateTime      time.Time            `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`        // 更新时间
}

// TableName Project's table name
func (*Project) TableName() string {
	return TableNameProject
}
