// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNamePermissionRole = "permission_role"

// PermissionRole mapped from table <permission_role>
type PermissionRole struct {
	PermissionID int32 `gorm:"column:permission_id;primaryKey" json:"permission_id"`
	RoleID       int32 `gorm:"column:role_id;primaryKey" json:"role_id"`
}

// TableName PermissionRole's table name
func (*PermissionRole) TableName() string {
	return TableNamePermissionRole
}
