package model

import (
	"time"

	"git.7k7k.com/data/abAdmin/model/logic"
)

type ExpData struct {
	FreezedAt time.Time `json:"freezed_at,omitzero"`
	ClosedAt  time.Time `json:"closed_at,omitzero"`
}

type GroupData struct {
	BatchName string    `json:"batch_name"`
	FreezedAt time.Time `json:"freezed_at,omitzero"`
	ClosedAt  time.Time `json:"closed_at,omitzero"`
}

type ProjectData struct {
	AllowUserKeys      []string              `json:"allow_user_keys"`      // 可以配哪些用户条件
	BundleID           string                `json:"bundle_id"`            // 包名
	Bucketv2ID         int                   `json:"bucketv2_id"`          // 2.0的bucket_id
	GroupParamStyle    logic.GroupParamStyle `json:"group_param_style"`    //
	DefaultTriggerOnce bool                  `json:"default_trigger_once"` //项目默认 是否只触发一次：进入实验后不再判断触发规则

	CsrRedisName   string `json:"csr_redis_name"`   // CSR 分流使用的 redis
	CsrRedisPrefix string `json:"csr_redis_prefix"` // CSR 分流使用的 prefix

	EnableDingding bool `json:"enable_dingding"` // 是否开启钉钉通知
	// 钉钉token列表，每个元素都可以是多个token
	// 例如：["token1,token2", "token3,token4,token5"]
	// - 1 2 会任选其一进行发送
	// - 3 4 5 会任选其一发送
	DingdingTokens []string `json:"dingding_tokens"`
	DingdingToken  string   `json:"dingding_token"` // 钉钉token
}

func (dt *ProjectData) CanSendDingding() bool {
	return dt.EnableDingding &&
		(dt.DingdingToken != "" || len(dt.DingdingTokens) > 0)
}

func (dt *ProjectData) GetDingdingTokens() []string {
	if len(dt.DingdingTokens) > 0 {
		return dt.DingdingTokens
	}
	return []string{dt.DingdingToken}
}

type FeatureConflictRules map[FeatureID][]FeatureID

func (f FeatureConflictRules) GetConflictIDs(featureId string) []string {
	if f == nil {
		return nil
	}
	return f[featureId]
}

type LevelID = int32

type LayerID = int64

type FeatureID = string
type FeatureKey = string

type FeatureStats struct {
	DataFeatureKey map[LevelID]map[LayerID]map[FeatureKey][]int64 `json:"data_feature_key"`
	DataFeatureId  map[LevelID]map[LayerID]map[FeatureID][]int64  `json:"data_feature_id"`
}

// 获取等级
func (f *FeatureStats) GetLevelFeatureKeyMap(levelId LevelID) map[LayerID]map[FeatureKey][]int64 {
	return f.DataFeatureKey[levelId]
}
func (f *FeatureStats) GetLevelFeatureIdMap(levelId LevelID) map[LayerID]map[FeatureID][]int64 {
	return f.DataFeatureId[levelId]
}
func (f *FeatureStats) SetLayerFeatureIdMap(levelId LevelID, layerId LayerID, featureGroupIds map[string][]int64) map[LevelID]map[LayerID]map[FeatureID][]int64 {
	if f.DataFeatureId[levelId][layerId] == nil {
		f.DataFeatureId[levelId][layerId] = make(map[FeatureID][]int64, len(featureGroupIds))
	}
	f.DataFeatureId[levelId][layerId] = featureGroupIds
	return f.DataFeatureId
}

func (f *FeatureStats) SetLayerFeatureKeyMap(levelId LevelID, layerId LayerID, featureGroupIds map[string][]int64) map[LevelID]map[LayerID]map[FeatureID][]int64 {
	if f.DataFeatureKey[levelId][layerId] == nil {
		f.DataFeatureKey[levelId][layerId] = make(map[FeatureKey][]int64, len(featureGroupIds))
	}
	f.DataFeatureKey[levelId][layerId] = featureGroupIds
	return f.DataFeatureKey
}

// Get 获取分层下的特性
func (f *FeatureStats) GetLayerFeatureIdMap(levelId LevelID, layerId LayerID) map[FeatureID][]int64 {
	levelM, ok := f.DataFeatureId[levelId]
	if !ok {
		return nil
	}
	layerM, ok := levelM[layerId]
	if !ok {
		return nil
	}
	return layerM
}

// Get 获取分层下的特性
func (f *FeatureStats) GetLayerFeatureKeyMap(levelId LevelID, layerId LayerID) map[FeatureKey][]int64 {
	levelM, ok := f.DataFeatureKey[levelId]
	if !ok {
		return nil
	}
	layerM, ok := levelM[layerId]
	if !ok {
		return nil
	}
	return layerM
}

func (f *FeatureStats) GetDiffLayerFeatureIdMap(levelId LevelID, layerId LayerID) map[FeatureID][]int64 {
	levelM, ok := f.DataFeatureId[levelId]
	if !ok {
		return nil
	}
	diffFeatureIdMap := make(map[FeatureID][]int64, len(levelM))
	for id, m := range levelM {
		if id == layerId {
			continue
		}
		for featureId, v := range m {
			if _, ok := diffFeatureIdMap[FeatureID(featureId)]; !ok {
				diffFeatureIdMap[featureId] = v
			} else {
				diffFeatureIdMap[FeatureID(featureId)] = append(diffFeatureIdMap[FeatureID(featureId)], v...)
			}
		}
	}
	return diffFeatureIdMap
}

func (f *FeatureStats) GetDiffLayerFeatureKeyMap(levelId LevelID, layerId LayerID) map[FeatureKey][]int64 {
	levelM, ok := f.DataFeatureKey[levelId]
	if !ok {
		return nil
	}
	diffFeatureKeyMap := make(map[FeatureID][]int64, len(levelM))
	for id, m := range levelM {
		if id == layerId {
			continue
		}
		for featureId, v := range m {
			if _, ok := diffFeatureKeyMap[FeatureID(featureId)]; !ok {
				diffFeatureKeyMap[featureId] = v
			} else {
				diffFeatureKeyMap[FeatureID(featureId)] = append(diffFeatureKeyMap[FeatureID(featureId)], v...)
			}
		}
	}
	return diffFeatureKeyMap
}

type ExpInfoList struct {
	Key  string     `json:"key"`
	List []*ExpInfo `json:"list"`
}

type ExpInfo struct {
	Name           string     `json:"name"`
	ExpType        int32      `json:"exp_type"`
	BatchName      string     `json:"batch_name"`
	TagName        string     `json:"tag_name"`
	Days           int32      `json:"days"`
	Owner          string     `json:"owner"`
	LayerName      string     `json:"layer_name"`
	LayerRate      int32      `json:"layer_rate"`
	Desc           string     `json:"desc"`
	Strategy       *Strategy  `json:"strategy"`
	GroupLimitHour int32      `json:"group_limit_hour"`
	Csr            []*CSRData `json:"csr"`
	Groups         []*Group   `json:"groups"`
}
