package model

import (
	"strings"
	"time"
)

const TableNameRole = "roles"

// Role mapped from table <roles>
type Role struct {
	ID          int32     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	Name        string    `gorm:"column:name;not null" json:"name"`
	DisplayName string    `gorm:"column:display_name" json:"display_name"`
	Description string    `gorm:"column:description" json:"description"`
	CreatedAt   time.Time `gorm:"column:created_at" json:"created_at"`
	UpdatedAt   time.Time `gorm:"column:updated_at" json:"updated_at"`
}

// TableName Role's table name
func (*Role) TableName() string {
	return TableNameRole
}

func (r *Role) ABName() string {
	return strings.TrimPrefix(r.<PERSON><PERSON>, `abtest-`)
}
