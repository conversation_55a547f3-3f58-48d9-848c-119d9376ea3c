package model

import "time"

type Stage string

type ApprovalLog struct {
	UserId    int32      `json:"user_id"`
	ApproveAt *time.Time `json:"approve_at"`
	State     int32      `json:"state"`
	Comment   string     `json:"comment"`
}
type ApprovalData struct {
	ExpId    int64    `json:"exp_id"`
	RuleId   int64    `json:"rule_id"`
	BeforeId int64    `json:"before_id"`
	AfterId  int64    `json:"after_id"`
	Desc     string   `json:"desc"`
	Changes  *Changes `json:"changes"`
}
type Changes struct {
	BaseChanges  []*Iterm `json:"base_changes"`
	BatchChanges []*Iterm `json:"batch_changes"`
}
type Iterm struct {
	Value string `json:"value"`
	Label string `json:"label"`
}
type ChangeNode struct {
	NodeType  string `json:"node"`
	GroupType string `json:"type"`
	Change    string `json:"change"`
}
