package model

// 任务类型 实验
const TaskTypePlanned = 1

type TaskStatus int

// 任务状态 1未开始 2执行中 3延迟 4取消 5已完成 6失败
const (
	TaskStatusPending TaskStatus = iota + 1
	TaskStatusRunning
	TaskStatusDelay
	TaskStatusCancel
	TaskStatusFinish
	TaskStatusFailed
)

// Name 返回枚举值的字符串名称
func (t TaskStatus) Name() string {
	switch t {
	case TaskStatusPending:
		return "未开始"
	case TaskStatusRunning:
		return "执行中"
	case TaskStatusDelay:
		return "延迟"
	case TaskStatusCancel:
		return "取消"
	case TaskStatusFinish:
		return "完成"
	case TaskStatusFailed:
		return "失败"
	}
	return "Unknown"
}
