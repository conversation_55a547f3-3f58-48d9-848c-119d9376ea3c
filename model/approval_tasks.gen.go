// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameApprovalTask = "approval_tasks"

// ApprovalTask 审批任务表
type ApprovalTask struct {
	ID               int64          `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                         // 主键ID
	ProjectID        int64          `gorm:"column:project_id;not null;comment:项目ID" json:"project_id"`                              // 项目ID
	RuleID           int64          `gorm:"column:rule_id;not null;comment:审批规则ID" json:"rule_id"`                                  // 审批规则ID
	ExpID            int64          `gorm:"column:exp_id;not null;comment:实验ID" json:"exp_id"`                                      // 实验ID
	BeforeSnapshotID int64          `gorm:"column:before_snapshot_id;not null;comment:变更前快照ID" json:"before_snapshot_id"`           // 变更前快照ID
	AfterSnapshotID  int64          `gorm:"column:after_snapshot_id;not null;comment:变更后快照ID" json:"after_snapshot_id"`             // 变更后快照ID
	Desc             string         `gorm:"column:desc;not null;comment:任务描述" json:"desc"`                                          // 任务描述
	CreatorID        int32          `gorm:"column:creator_id;not null;comment:创建人ID" json:"creator_id"`                             // 创建人ID
	State            int32          `gorm:"column:state;not null;default:1;comment:状态 1: 审批中 2: 审批完成 3: 关闭" json:"state"`           // 状态 1: 审批中 2: 审批完成 3: 关闭
	IsEffected       int32          `gorm:"column:is_effected;not null;comment:0：无效，1：有效" json:"is_effected"`                       // 0：无效，1：有效
	Step             int32          `gorm:"column:step;not null;comment:当前审批步骤" json:"step"`                                        // 当前审批步骤
	Approvers        []int32        `gorm:"column:approvers;default:'[]';comment:当前步骤审批人;serializer:json" json:"approvers"`         // 当前步骤审批人
	FlowApprovers    []int32        `gorm:"column:flow_approvers;default:'[]';comment:历史审批人;serializer:json" json:"flow_approvers"` // 历史审批人
	SearchText       []string       `gorm:"column:search_text;default:'[]';comment:关键词查询索引;serializer:json" json:"search_text"`     // 关键词查询索引
	Log              []*ApprovalLog `gorm:"column:log;default:'[]';comment:审批日志;serializer:json" json:"log"`                        // 审批日志
	CreatedAt        *time.Time     `gorm:"column:created_at;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`             // 创建时间
	UpdatedAt        *time.Time     `gorm:"column:updated_at;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`             // 更新时间
	Changes          *Changes       `gorm:"column:changes;default:'[]';comment:相关改变;serializer:json" json:"changes"`                // 相关改变
	ApprovalRule     ApprovalRule   `gorm:"foreignKey:rule_id" json:"approval_rule"`
	ExpLog           ExpLog         `gorm:"foreignKey:after_snapshot_id" json:"exp_log"`
	Exp              Exp            `gorm:"foreignKey:exp_id" json:"exp"`
}

// TableName ApprovalTask's table name
func (*ApprovalTask) TableName() string {
	return TableNameApprovalTask
}
