package model

import (
	"strings"
	"time"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

func init() {
	// schema.RegisterSerializer("json", schema.JSONSerializer{})
}

type UserInfo struct {
	ID               int64        `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id,omitempty" form:"id"`
	UserID           int64        `gorm:"column:user_id;default:0;NOT NULL" json:"user_id,omitempty" form:"user_id"`
	SearchText       string       `gorm:"column:search_text;NOT NULL" json:"search_text,omitempty" form:"search_text"`
	CreatedAt        time.Time    `gorm:"column:created_at;default:CURRENT_TIMESTAMP;NOT NULL" json:"created_at,omitempty" form:"created_at"`
	UpdatedAt        time.Time    `gorm:"column:updated_at;default:CURRENT_TIMESTAMP;NOT NULL" json:"updated_at,omitempty" form:"updated_at"`
	Data             UserInfoData `gorm:"serializer:json" json:"data"`
	User             *User        `gorm:"-" json:"user"`
	SearchTextList   []any        `gorm:"-" json:"-"`
	DisplayRoleTitle string       `gorm:"-" json:"display_role_title"`
}

type UserInfoData struct {
	PrjRoles []*PrjRole `json:"prj_roles" form:"prj_roles"`
}

type PrjRole struct {
	PrjId   int     `json:"prj_id"`
	RoleIds []int32 `json:"role_ids"`
	Display string  `json:"display"`

	Project *Project `gorm:"-" json:"-"`
	Roles   []*Role  `gorm:"-" json:"-"`
}

func (u *UserInfo) AfterFind(_ *gorm.DB) (err error) {
	displays := lo.Map(u.Data.PrjRoles, func(prjRole *PrjRole, _ int) string {
		return prjRole.Display
	})
	u.DisplayRoleTitle = strings.Join(displays, "，")
	return
}

func (u *UserInfo) BeforeSave(_ *gorm.DB) (err error) {
	if u.Data.PrjRoles == nil {
		u.Data.PrjRoles = []*PrjRole{}
	}
	u.SearchText = BuildSearchText(u.SearchTextList...)
	return
}
