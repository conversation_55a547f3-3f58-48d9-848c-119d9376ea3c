package dto

import (
	"fmt"
	"net/url"
)

type Markdown struct {
	// Title string
	Lines []string
}

func (m *Markdown) AddLine(line string) {
	m.Lines = append(m.Lines, line+"\n\n")
}

func (m *Markdown) AddLinef(format string, args ...any) {
	m.Lines = append(m.Lines, fmt.Sprintf(format, args...)+"\n\n")
}

func (m *Markdown) AddUL(line string) {
	m.AddLine("- " + line)
}

func (m *Markdown) LinkTo(src string, text string) string {
	return fmt.Sprintf("[%s](%s)", text, m.URL(src))
}

func (m *Markdown) URL(src string) string {
	return "dingtalk://dingtalkclient/page/link?pc_slide=false&url=" + url.QueryEscape(src)
}

func (m *Markdown) Green(text string) string {
	return fmt.Sprintf("<font color=#00CD66>%s</font>", text)
}

func (m *Markdown) Red(text string) string {
	return fmt.Sprintf("<font color=#ff0000>%s</font>", text)
}

func (m *Markdown) ToText() string {
	text := ""
	for _, line := range m.Lines {
		text += line
	}
	return text
}
