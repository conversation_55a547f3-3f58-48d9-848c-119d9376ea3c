package dto

import (
	"time"

	"git.7k7k.com/data/abAdmin/httpd/response"
	"git.7k7k.com/data/abAdmin/model"
)

// ExpLog 实验日志
type ExpLog struct {
	Exp               *model.Exp            `json:"-"`
	AddGroups         []*model.Group        `json:"-"`
	UpdateGroups      []*model.Group        `json:"-"`
	UnchangedGroups   []*model.Group        `json:"-"`
	UserMap           map[int32]*model.User `json:"-"`
	SplitGroup        []*model.SplitGroup   `json:"-"`
	ApprovalStateNode string                `json:"-"`
	ExpOperate        string                `json:"-"`
	OriginExp         *model.Exp            `json:"-"`
}

// Snapshot 快照
type Snapshot struct {
	ExpInfo           ExpInfo             `json:"exp_info"`
	ExpWithLayer      []*response.ExpList `json:"exp_with_layer"`
	Groups            []*response.Group   `json:"groups"`
	TotalUser         int64               `json:"total_user"`
	GroupCount        int                 `json:"group_count"`
	AddGroups         []*model.Group      `json:"add_groups"`
	UpdateGroups      []*model.Group      `json:"update_groups"`
	UnchangedGroups   []*model.Group      `json:"unchanged_groups"`
	SplitGroup        []*model.SplitGroup `json:"redirect_group" binding:"-"`
	ApprovalStateNode string              `json:"approval_state_node"`
	ExpOperate        string              `json:"exp_operate"`
	OriginExp         *model.Exp          `json:"origin_exp"`
}

// ExpInfo 实验的基础数据
type ExpInfo struct {
	Id             int64            `json:"id"`
	Name           string           `json:"name"`
	Desc           string           `json:"desc"`
	ExpType        int32            `json:"exp_type"`
	ExpTypeName    string           `json:"exp_type_name"`
	Days           int32            `json:"days"`
	ExpRunTime     string           `json:"exp_run_time"`
	BatchName      string           `json:"batch_name"`
	BatchId        int64            `json:"batch_id"`
	Owner          string           `json:"owner"`
	OwnerName      string           `json:"owner_name"`
	TagName        string           `json:"tag_name"`
	TagId          string           `json:"tag_id"`
	LayerId        int64            `json:"layer_id"`
	LayerName      string           `json:"layer_name"`
	LayerRate      int32            `json:"layer_rate"`
	StartTime      *time.Time       `json:"start_time"`
	EndTime        *time.Time       `json:"end_time"`
	State          int32            `json:"state"`
	StateName      string           `json:"state_name"`
	Strategy       *model.Strategy  `json:"strategy"`
	Csr            []*model.CSRData `json:"csr"`
	Step           int              `json:"step"`
	NextStep       int              `json:"next_step"`
	Version        int64            `json:"version"`
	UpdateTime     time.Time        `json:"update_time"`
	GroupLimitHour int32            `json:"group_limit_hour"`
}

type ApprovalData struct {
	ExpId    int64    `json:"exp_id"`
	RuleId   int64    `json:"rule_id"`
	BeforeId int64    `json:"before_id"`
	AfterId  int64    `json:"after_id"`
	Desc     string   `json:"desc"`
	Changes  *Changes `json:"changes"`
}
type Changes struct {
	BaseChanges  []*Iterm `json:"base_changes"`
	BatchChanges []*Iterm `json:"batch_changes"`
}
type Iterm struct {
	Value string `json:"value"`
	Label string `json:"label"`
}
