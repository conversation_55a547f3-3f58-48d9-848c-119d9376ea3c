package dto

import (
	"git.7k7k.com/data/abAdmin/gopkg/condition"
	"git.7k7k.com/data/abAdmin/model"
)

// StrategyTemplate 策略模板的响应结构体，需要根据类型对其解析为不同的condition
type StrategyTemplate struct {
	model.StrategyTpl
	Data any `json:"tpl_data"`
}

// StrategyQuery 实验策略模板查询结构体
type StrategyQuery struct {
	TplType int32 `form:"tpl_type,string" binding:"required"`
}

// StrategyDetailQuery 详情查询结构体
type StrategyDetailQuery struct {
	Id int64 `form:"id, string" binding:"required"`
}

// StrategyFilterQuery 实验策略过滤器
type StrategyFilterQuery struct {
	// 触发用户规则:trigger_user_key
	// 停止进量触发规则: trigger_stop_key
	// 关闭触发规则: trigger_end_key
	// 分流用户触发规则: trigger_diversion_key
	Type  string `form:"type" binding:"required"`
	ExpId int64  `form:"exp_id, string"`
}

// AddStrategyReq 添加策略模板
type AddStrategyReq struct {
	Name    string           `json:"name" binding:"required"`
	TplType int32            `json:"tpl_type" binding:"min=0,max=255"`
	Rules   *model.Condition `json:"rules"`
	CSRData []*model.CSRData `json:"csr"`
}

// StrategyQueryReq 策略表达式请求
type StrategyQueryReq struct {
	ExpId    int64           `json:"exp_id" binding:"required"`
	Strategy *model.Strategy `json:"strategies" binding:"required"`
}

// StrategyResp 策略表达式响应
type StrategyResp struct {
	PreExpression map[string]string `json:"pre_expression"`
	CurExpression map[string]string `json:"cur_expression"`
}

// Condition 条件的响应
type Condition struct {
	Label        string              `json:"label,omitempty"`
	Value        string              `json:"value,omitempty"` // 对应ExpressionKey的key
	StrategyType string              `json:"strategy_type,omitempty"`
	Type         condition.ValueType `json:"type,omitempty"`
	AllowOpt     []*Operator         `json:"allow_opt,omitempty"`
	DefaultValue interface{}         `json:"default_value,omitempty"`
	Options      interface{}         `json:"options,omitempty"`
	Source       struct {
		Type        string `json:"type,omitempty"`
		AllowCreate bool   `json:"allow_create,omitempty"`
		List        []any  `json:"list,omitempty"`
	} `json:"source,omitempty"`
}

// Operator 操作符
type Operator struct {
	Label  condition.OpType `json:"label"`
	Value  condition.OpType `json:"value"`
	Symbol condition.OpType `json:"symbol"`
}

// StrategyNode 国家、实验、方案等条目
type StrategyNode struct {
	Label    string          `json:"label"`
	Value    string          `json:"value"`
	Children []*StrategyNode `json:"children,omitempty"`
}

type ExpressionKey struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

type Country struct {
	Label      string `json:"label"`
	Value      string `json:"value"`
	Region     string `json:"region"`
	RegionName string `json:"region_name"`
}
