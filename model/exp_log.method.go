package model

// type Snapshot struct {
// 	ExpInfo      ExpInfo             `json:"exp_info"`
// 	ExpWithLayer []*response.ExpList `json:"exp_with_layer"`
// 	Groups       []*response.Group   `json:"groups"`
// 	TotalUser    int64               `json:"total_user"`
// 	GroupCount   int                 `json:"group_count"`
// }

// func (e *ExpLog) GetExp() *Exp {
// 	detail := &dto.Snapshot{}
// 	err = json.Unmarshal([]byte(*snapshot.ExpSnapshot), detail)
// 	if err != nil {
// 		return nil, base.NewBizError(errors.Errorf("failed to unmarshal experiment snapshot，%s", err.Error()))
// 	}
// }
