// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNamePermission = "permissions"

// Permission mapped from table <permissions>
type Permission struct {
	ID             int32     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	ParentID       int32     `gorm:"column:parent_id" json:"parent_id"`
	Group          int32     `gorm:"column:group" json:"group"`
	Name           string    `gorm:"column:name;not null" json:"name"`
	IsMenu         bool      `gorm:"column:is_menu;not null" json:"is_menu"`
	Left           int32     `gorm:"column:left" json:"left"`
	Right          int32     `gorm:"column:right" json:"right"`
	Depth          int32     `gorm:"column:depth" json:"depth"`
	Sort           int32     `gorm:"column:sort;not null" json:"sort"`
	DisplayName    string    `gorm:"column:display_name" json:"display_name"`
	DisplayIcon    string    `gorm:"column:display_icon" json:"display_icon"`
	Description    string    `gorm:"column:description" json:"description"`
	CreatedAt      time.Time `gorm:"column:created_at" json:"created_at"`
	UpdatedAt      time.Time `gorm:"column:updated_at" json:"updated_at"`
	Status         int32     `gorm:"column:status;default:1" json:"status"`
	FrontRoute     string    `gorm:"column:front_route;not null;comment:前端路由" json:"front_route"`       // 前端路由
	FrontPath      string    `gorm:"column:front_path;not null;comment:路径" json:"front_path"`           // 路径
	FrontName      string    `gorm:"column:front_name;not null;comment:名称" json:"front_name"`           // 名称
	FrontComponent string    `gorm:"column:front_component;not null;comment:名称" json:"front_component"` // 名称
	FrontIcon      string    `gorm:"column:front_icon;not null;comment:icon" json:"front_icon"`         // icon
	FrontOrder     int32     `gorm:"column:front_order;not null;comment:排序" json:"front_order"`         // 排序
	FrontRedirect  string    `gorm:"column:front_redirect;not null;comment:跳转路径" json:"front_redirect"` // 跳转路径
}

// TableName Permission's table name
func (*Permission) TableName() string {
	return TableNamePermission
}
