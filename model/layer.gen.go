// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameLayer = "layer"

// Layer 流量层表
type Layer struct {
	ID          int64      `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	ProjectID   int64      `gorm:"column:project_id;not null;comment:ab_project关联id" json:"project_id"`                   // ab_project关联id
	Name        string     `gorm:"column:name;not null;comment:流量层名称" json:"name"`                                        // 流量层名称
	IsExclusion int32      `gorm:"column:is_exclusion;not null;comment:是否互斥：1是 2否" json:"is_exclusion"`                   // 是否互斥：1是 2否
	Rate        int32      `gorm:"column:rate;not null;comment:独占占比" json:"rate"`                                         // 独占占比
	Level       int32      `gorm:"column:level;not null;comment:分层等级，1低级，2中级，3高级" json:"level"`                           // 分层等级，1低级，2中级，3高级
	Desc        string     `gorm:"column:desc;not null;comment:描述" json:"desc"`                                           // 描述
	State       int32      `gorm:"column:state;not null;default:1;comment:状态：1在线 2下线" json:"state"`                       // 状态：1在线 2下线
	DownTime    *time.Time `gorm:"column:down_time;comment:下线时间" json:"down_time"`                                        // 下线时间
	UpdateTime  time.Time  `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"` // 更新时间
	CreateTime  time.Time  `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"` // 创建时间
}

// TableName Layer's table name
func (*Layer) TableName() string {
	return TableNameLayer
}
