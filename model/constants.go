package model

import "errors"

// 删除状态码  1已删除 2未删除
const (
	Deleted    = iota + 1 // 已删除
	NotDeleted            // 未删除
)

// 实验策略类型
const (
	StrategyUserRule      = iota + 1 // 触发用户规则
	StrategyNoNewFlowRule            // 停止进量触发规则
	StrategyCloseRule                // 关闭触发规则
	StrategyDivertedRule             // 分流用户触发规则
)

// 实验方案状态
const (
	ExpPlanStatusRunningNoNewFlow = iota + 1
	ExpPlanStatusClose
)

// 策略模板的状态码 1删除 2启用 3禁用
const (
	StrategyStateDeleted = iota + 1
	StrategyStateEnabled
	StrategyStateDisabled
)

// 重定向状态
const (
	RedirectStateClose     = -1
	RedirectStateTodo      = 1
	RedirectStateTodoLocal = 11 // 本地测试
	RedirectStateRunning   = 2
	RedirectStateDone      = 3
)

// 实验类型
const (
	ExpTypeNew = iota + 1
	ExpTypeActive
	ExpTypeOther
)

var ExpTypeMap = map[int]string{
	ExpTypeNew:    "新增实验",
	ExpTypeActive: "活跃实验",
	ExpTypeOther:  "其他实验",
}

const (
	LayerTypeNoEx = 2
	LayerTypeEx   = 1
)
const (
	GroupTypeCompare = 1
	GroupTypeExp     = 2
)

var GroupTypeMap = map[int]string{
	GroupTypeCompare: "对照组",
	GroupTypeExp:     "实验组",
}

// 实验操作方式
const (
	ExpOperationTypeStart = iota + 1
	ExpOperationTypeGrayStart
	ExpOperationTypeNotProgress
	ExpOperationTypeClose
	ExpOperationTypeRestart
)

// 实验状态
// 1草稿 2待执行 3执行中 4执行中未进量 5已关闭 6灰度中 7灰度中未进量
const (
	ExpStateDraft                  = 1
	ExpStateTodo                   = 2
	ExpStateRunning                = 3
	ExpStateRunningNotProgress     = 4
	ExpStateClose                  = 5
	ExpStateGrayRunning            = 6
	ExpStateGrayRunningNotProgress = 7
)

var ExpStateMap = map[int]string{
	ExpStateDraft:                  "草稿",
	ExpStateTodo:                   "待启动",
	ExpStateRunning:                "运行中",
	ExpStateRunningNotProgress:     "运行中不进量",
	ExpStateClose:                  "关闭",
	ExpStateGrayRunning:            "灰度运行中",
	ExpStateGrayRunningNotProgress: "灰度运行中不进量",
}

// 实验方案，流量操作 1 关闭，2 重定向 3 运行中重定向
const (
	PlanOperationTypeClose   = 1
	PlanOperationTypeRestart = 2
	PlanOperationTypeRunning = 3
)
const (
	GroupValidateParamRuleGeneral = "general"
	GroupValidateParamRuleSpecial = "special"
)

// 实验组(方案)状态
// 2:待开始，3:运行中 4:运行中不进量，5:已关闭
const (
	GroupStateDraft              = 1
	GroupStateTodo               = 2
	GroupStateRunning            = 3
	GroupStateRunningNotProgress = 4
	GroupStateClose              = 5
)

var GroupStateMap = map[int]string{
	GroupStateDraft:              "草稿",
	GroupStateTodo:               "待启动",
	GroupStateRunning:            "运行中",
	GroupStateRunningNotProgress: "运行中不进量",
	GroupStateClose:              "关闭",
}

// split group type
const (
	SplitGroupTypeClose           = 1
	SplitGroupTypeRedirect        = 2
	SplitGroupTypeRunningRedirect = 3
)

// 实验步骤
const (
	ExpStepBaseInfo = 1
	ExpStepStrategy = 2
	ExpStepGroups   = 3
	ExpStepMeasure  = 4
)

const (
	StateOnline  = 1
	StateOffline = 2
)

// 分层级别
const (
	LayerNoLevel     int32 = 0
	LayerLevelLow    int32 = 1
	LayerLevelMedium int32 = 2
	LayerLevelHigh   int32 = 3
)

const (
	ApprovalInit      int32 = 1 // 审批中
	ApprovalCompleted int32 = 2 // 审批完成
	ApprovalClose     int32 = 3 // 审批关闭
)

var ApprovalStateMap = map[int32]string{
	ApprovalInit:      "审批中",
	ApprovalCompleted: "审批完成",
	ApprovalClose:     "审批关闭",
}

const (
	LogStateInit     int32 = 1
	LogStateApproval int32 = 2
	LogStateReject   int32 = 3
	LogStateRevoke   int32 = 4
)

var LogStateMap = map[int32]string{
	LogStateInit:     "发起审批",
	LogStateApproval: "审批通过",
	LogStateReject:   "审批驳回",
	LogStateRevoke:   "撤销审批",
}

const (
	ExpOpen                  = "exp_open"
	ExpStop                  = "exp_stop"
	ExpClose                 = "exp_close"
	ExpRestart               = "exp_restart"
	ExpOwnerChange           = "exp_owner_change"
	ExpLayerChange           = "exp_layer_change"
	ExpDaysChange            = "exp_days_change"
	ExpGroupLimitHoursChange = "exp_group_limit_hours_change"
	ExpTriggerUserChange     = "exp_trigger_User_change"
	ExpTriggerStopChange     = "exp_trigger_stop_change"
	ExpTriggerEndChange      = "exp_trigger_end_change"
	ExpNewGroup              = "exp_new_group"
	ExpStopGroup             = "exp_stop_group"
	ExpCloseGroup            = "exp_close_group"
	ExpRedirectGroup         = "exp_redirect_group"
	ExpTriggerOnce           = "exp_trigger_once"
)

var ApprovalNodeList = map[string]string{
	ExpOpen:                  "实验开启",
	ExpStop:                  "实验停止进量",
	ExpClose:                 "实验关闭",
	ExpRestart:               "实验重启",
	ExpOwnerChange:           "负责人变更",
	ExpLayerChange:           "流量变更",
	ExpDaysChange:            "实验天数变更",
	ExpGroupLimitHoursChange: "小时限量变更",
	ExpTriggerUserChange:     "触发用户规则变更",
	ExpTriggerStopChange:     "停止进量触发规则变更",
	ExpTriggerEndChange:      "关闭触发规则变更",
	ExpNewGroup:              "方案新增",
	ExpStopGroup:             "方案停止进量",
	ExpCloseGroup:            "方案关闭",
	ExpRedirectGroup:         "方案重定向",
	ExpTriggerOnce:           "进入实验后不再判断触发条件变更",
}

const (
	ExpStateChange int32 = 1 // 实验状态变更
	ExpMsgChange   int32 = 2 // 实验信息变更
)

var ExpChangeTypeMap = map[int32]string{
	ExpStateChange: "实验状态变更",
	ExpMsgChange:   "实验信息变更",
}
var ExpChangeTypeList = map[int32][]string{
	ExpStateChange: {
		ExpOpen,
		ExpStop,
		ExpClose,
		ExpRestart,
	},
	ExpMsgChange: {
		ExpOwnerChange,
		ExpLayerChange,
		ExpDaysChange,
		ExpGroupLimitHoursChange,
		ExpTriggerUserChange,
		ExpTriggerStopChange,
		ExpTriggerEndChange,
		ExpNewGroup,
		ExpStopGroup,
		ExpCloseGroup,
		ExpRedirectGroup,
		ExpTriggerOnce,
	},
}
var ExpChangeToTable = map[string]string{
	ExpOwnerChange:           "owner",
	ExpLayerChange:           "layer_rate",
	ExpDaysChange:            "days",
	ExpGroupLimitHoursChange: "group_limit_hour",
	ExpTriggerUserChange:     ExpTriggerUserChange,
	ExpTriggerStopChange:     ExpTriggerStopChange,
	ExpTriggerEndChange:      ExpTriggerEndChange,
	ExpOpen:                  ExpOpen,
	ExpStop:                  ExpStop,
	ExpClose:                 ExpClose,
	ExpRestart:               ExpRestart,
}

const (
	//下线
	ApprovalRuleStateOffline int32 = -1
	//开启
	ApprovalRuleStateOnline int32 = 1
	//关闭
	ApprovalRuleStateClose int32 = 2
)

var ApprovalRuleStateMap = map[int32]string{
	ApprovalRuleStateOffline: "下线",
	ApprovalRuleStateOnline:  "开启",
	ApprovalRuleStateClose:   "关闭",
}

const (
	ChangeNodeGroupParamType      = "change_node_group_param_type"
	ChangeNodeGroupStrategyType   = "change_node_group_strategy_type"
	ChangeNodeGroupNewGroupType   = "change_node_group_new_group_type"
	ChangeNodeGroupStopGroupType  = "change_node_group_stop_group_type"
	ChangeNodeGroupCloseGroupType = "change_node_group_close_group_type"
	ChangeNodeGroupRedirectType   = "change_node_group_redirect_type"
	ChangeNodeExpStateType        = "change_node_exp_state_type"
)

var ChangeNodeTypeMap = map[string]string{
	ChangeNodeGroupParamType:      "字段变更",
	ChangeNodeGroupStrategyType:   "策略变更",
	ChangeNodeGroupNewGroupType:   "方案新增",
	ChangeNodeGroupStopGroupType:  "方案停止进量",
	ChangeNodeGroupCloseGroupType: "方案关闭",
	ChangeNodeGroupRedirectType:   "方案重定向",
	ChangeNodeExpStateType:        "实验状态变更",
}

// 审批Error
var (
	ExpStateChangeNeedApprovalError = errors.New("exp_state_change_need_approval_error")
)
