// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameUser = "users"

// User mapped from table <users>
type User struct {
	ID       int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id,omitempty"`
	Account  string `gorm:"column:account;not null;comment:用户名" json:"account,omitempty"`                       // 用户名
	Password string `gorm:"column:password;not null;comment:密码" json:"password,omitempty"`                      // 密码
	Status   int32  `gorm:"column:status;not null;comment:0=禁用，1=启用,2:待提审, 3拒绝, 4.审核中" json:"status,omitempty"` // 0=禁用，1=启用,2:待提审, 3拒绝, 4.审核中
	// AdvertiserStatus bool      `gorm:"column:advertiser_status;not null;comment:优化师状态" json:"advertiser_status,omitempty"`              // 优化师状态
	BiDataManager   bool      `gorm:"column:bi_data_manager;not null;comment:BI数据是否查看全部数据" json:"bi_data_manager,omitempty"`           // BI数据是否查看全部数据
	CnOrder         int8      `gorm:"column:cn_order;not null;comment:国内消耗排序" json:"cn_order,omitempty"`                               // 国内消耗排序
	HwOrder         int8      `gorm:"column:hw_order;not null;comment:海外消耗排序" json:"hw_order,omitempty"`                               // 海外消耗排序
	Realname        string    `gorm:"column:realname;comment:真实姓名" json:"realname,omitempty"`                                          // 真实姓名
	Alias           string    `gorm:"column:alias;not null;comment:账户别名" json:"alias,omitempty"`                                       // 账户别名
	Email           string    `gorm:"column:email;comment:邮箱" json:"email,omitempty"`                                                  // 邮箱
	Telephone       string    `gorm:"column:telephone;comment:电话" json:"telephone,omitempty"`                                          // 电话
	Description     string    `gorm:"column:description;comment:备注" json:"description,omitempty"`                                      // 备注
	RememberToken   string    `gorm:"column:remember_token;comment:token" json:"remember_token,omitempty"`                             // token
	CreatedAt       time.Time `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP;comment:创建日期" json:"created_at,omitempty"`   // 创建日期
	LoginAt         time.Time `gorm:"column:login_at;comment:最新登陆日期" json:"login_at,omitempty"`                                        // 最新登陆日期
	UpdatedAt       time.Time `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP;comment:更新日期" json:"updated_at,omitempty"`   // 更新日期
	GamePower       string    `gorm:"column:game_power;comment:游戏权限" json:"game_power,omitempty"`                                      // 游戏权限
	Type            int32     `gorm:"column:type;not null;comment:0:默认,1:个人开发者，2:企业开发者,3:前端注册" json:"type,omitempty"`                  // 0:默认,1:个人开发者，2:企业开发者,3:前端注册
	Step            int32     `gorm:"column:step;not null;comment:0:默认 1:已完成认证信息，2:已填写财务信息 3:完成并创建游戏" json:"step,omitempty"`           // 0:默认 1:已完成认证信息，2:已填写财务信息 3:完成并创建游戏
	FinanceStatus   int32     `gorm:"column:finance_status;not null;comment:财务审核：0=禁用，1=启用,2:待审核,3拒绝" json:"finance_status,omitempty"` // 财务审核：0=禁用，1=启用,2:待审核,3拒绝
	ExpiredAt       time.Time `gorm:"column:expired_at;not null;comment:过期时间" json:"expired_at,omitempty"`                             // 过期时间
	Remark          string    `gorm:"column:remark;not null;comment:帐号备注" json:"remark,omitempty"`                                     // 帐号备注
	FinanceRemark   string    `gorm:"column:finance_remark;not null;comment:财务备注" json:"finance_remark,omitempty"`                     // 财务备注
	TouserOpenid    string    `gorm:"column:touser_openid;not null;comment:游益网络" json:"touser_openid,omitempty"`                       // 游益网络
	BixinOpenid     string    `gorm:"column:bixin_openid;comment:比心网络" json:"bixin_openid,omitempty"`                                  // 比心网络
	Groups          string    `gorm:"column:groups;comment:组别" json:"groups,omitempty"`                                                // 组别
	IsGroupleader   int32     `gorm:"column:is_groupleader;not null;comment:是否是组长 0不是 1是" json:"is_groupleader,omitempty"`             // 是否是组长 0不是 1是
	IsProjectLeader string    `gorm:"column:is_project_leader;not null;comment:项目组长" json:"is_project_leader,omitempty"`               // 项目组长
	MobilePhone     string    `gorm:"column:mobile_phone;not null;comment:手机号" json:"mobile_phone,omitempty"`                          // 手机号
}

// TableName User's table name
func (*User) TableName() string {
	return TableNameUser
}
