package model

import (
	"fmt"
)

func (e *Exp) AbUrl(baseUrl string) string {
	return fmt.Sprintf("%s/#/experimentManage/experimentDetail?exp_id=%d&projectId=%d", baseUrl, e.ID, e.ProjectID)
}

func (e *Exp) BIURL(baseUrl, pkgName string) string {
	switch pkgName {
	case "com.block.juggle":
		return fmt.Sprintf("%s/admin/hitAd/newABPlatform/new/blockGp?pici=%s", baseUrl, e.Batch.Name)
	case "com.blockpuzzle.us.ios":
		return fmt.Sprintf("%s/admin/hitAd/newABPlatform/new/blockIos?pici=%s", baseUrl, e.Batch.Name)
	}
	return ""
}
