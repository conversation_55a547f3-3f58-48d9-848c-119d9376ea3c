// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameApprovalRule = "approval_rules"

// ApprovalRule 审批规则表
type ApprovalRule struct {
	ID         int64      `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                                // 主键ID
	ProjectID  int64      `gorm:"column:project_id;not null;comment:项目ID" json:"project_id"`                                     // 项目ID
	Name       string     `gorm:"column:name;not null;comment:流程名称" json:"name" binding:"required"`                              // 流程名称
	RuleType   int32      `gorm:"column:rule_type;not null;comment:审批类型：1：实验状态变更；2：实验信息变更；" json:"rule_type"`                    // 审批类型：1：实验状态变更；2：实验信息变更；
	Desc       string     `gorm:"column:desc;not null;comment:流程描述" json:"desc"`                                                 // 流程描述
	State      int32      `gorm:"column:state;not null;default:1;comment:状态 -1:下线 1: 开启 2: 关闭" json:"state"`                     // 状态 -1:下线 1: 开启 2: 关闭
	Stages     []Stage    `gorm:"column:stages;default:'[]';comment:审批节点;serializer:json" json:"stages" binding:"required"`      // 审批节点
	Approvers  [][]int32  `gorm:"column:approvers;default:'[]';comment:审批人;serializer:json" json:"approvers" binding:"required"` // 审批人
	CreatorID  int32      `gorm:"column:creator_id;not null;comment:创建人ID" json:"creator_id"`                                    // 创建人ID
	SearchText []string   `gorm:"column:search_text;default:'[]';comment:关键词查询索引;serializer:json" json:"search_text"`            // 关键词查询索引
	CreatedAt  *time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`                    // 创建时间
	UpdatedAt  *time.Time `gorm:"column:updated_at;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`                    // 更新时间
}

// TableName ApprovalRule's table name
func (*ApprovalRule) TableName() string {
	return TableNameApprovalRule
}
