// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameExpLog = "exp_log"

// ExpLog 实验操作记录表
type ExpLog struct {
	ID          int64         `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	ExpID       int64         `gorm:"column:exp_id;not null;comment:实验ID" json:"exp_id"`                                // 实验ID
	ExpSnapshot *string       `gorm:"column:exp_snapshot;comment:实验快照" json:"exp_snapshot"`                             // 实验快照
	Changes     string        `gorm:"column:changes;not null;comment:变更内容" json:"changes"`                              // 变更内容
	Desc        string        `gorm:"column:desc;not null;comment:修改描述" json:"desc"`                                    // 修改描述
	UID         int64         `gorm:"column:uid;not null;comment:操作人ID" json:"uid"`                                     // 操作人ID
	Uname       string        `gorm:"column:uname;not null;comment:操作人名字" json:"uname"`                                 // 操作人名字
	CreateTime  time.Time     `gorm:"column:create_time;not null;comment:创建时间" json:"create_time"`                      // 创建时间
	State       int32         `gorm:"column:state;not null;default:1;comment:状态 1: 未生效 2: 已生效（发布） 3: 已失效" json:"state"` // 状态 1: 未生效 2: 已生效（发布） 3: 已失效
	Show        int32         `gorm:"column:show;not null;comment:状态 0: 不可见 1: 可见" json:"show"`                         // 状态 0: 不可见 1: 可见
	Notify      NotifyMessage `gorm:"-"`
}

// TableName ExpLog's table name
func (*ExpLog) TableName() string {
	return TableNameExpLog
}
