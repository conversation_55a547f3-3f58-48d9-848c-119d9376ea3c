// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameExp = "exp"

// Exp 实验表
type Exp struct {
	ID             int64           `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	ProjectID      int64           `gorm:"column:project_id;not null;comment:project关联id" json:"project_id"`                                             // project关联id
	LayerID        int64           `gorm:"column:layer_id;not null;comment:layer关联id" json:"layer_id"`                                                   // layer关联id
	Name           string          `gorm:"column:name;not null;comment:实验名称" json:"name"`                                                                // 实验名称
	LayerRate      int32           `gorm:"column:layer_rate;not null;comment:分层流量占比" json:"layer_rate"`                                                  // 分层流量占比
	ExpType        int32           `gorm:"column:exp_type;not null;comment:实验类型：1新增实验 2活跃实验" json:"exp_type"`                                            // 实验类型：1新增实验 2活跃实验
	TagID          string          `gorm:"column:tag_id;not null;comment:ab_tag关联id,多个，逗号链接" json:"tag_id"`                                              // ab_tag关联id,多个，逗号链接
	BatchID        int64           `gorm:"column:batch_id;not null;comment:ab_batch关联id" json:"batch_id"`                                                // ab_batch关联id
	Days           int32           `gorm:"column:days;not null;comment:实验天数" json:"days"`                                                                // 实验天数
	Strategy       *Strategy       `gorm:"column:strategy;default:'{}';comment:实验策略;serializer:json" json:"strategy"`                                    // 实验策略
	Csr            []*CSRData      `gorm:"column:csr;default:'[]';comment:csr;serializer:json" json:"csr"`                                               // csr
	UID            int32           `gorm:"column:uid;not null;comment:创建者id" json:"uid"`                                                                 // 创建者id
	Owner          string          `gorm:"column:owner;not null;comment:所有者" json:"owner"`                                                               // 所有者
	Desc           string          `gorm:"column:desc;not null;comment:描述" json:"desc"`                                                                  // 描述
	Step           int32           `gorm:"column:step;not null;default:1;comment:添加实验步骤，1：基本信息，2:设置策略，3:添加实验组（方案），4：设置实验指标" json:"step"`                 // 添加实验步骤，1：基本信息，2:设置策略，3:添加实验组（方案），4：设置实验指标
	State          int32           `gorm:"column:state;not null;default:1;comment:实验状态：1:草稿 2:待开始 3:运行中 4:运行中不进量 5:已关闭 6:灰度运行中 7:灰度运行中不进量" json:"state"` // 实验状态：1:草稿 2:待开始 3:运行中 4:运行中不进量 5:已关闭 6:灰度运行中 7:灰度运行中不进量
	IsDeleted      int32           `gorm:"column:is_deleted;not null;default:2;comment:状态：1禁用 2启用" json:"is_deleted"`                                    // 状态：1禁用 2启用
	StartTime      *time.Time      `gorm:"column:start_time;comment:实验开始时间" json:"start_time"`                                                           // 实验开始时间
	EndTime        *time.Time      `gorm:"column:end_time;comment:实验结束时间" json:"end_time"`                                                               // 实验结束时间
	Version        int64           `gorm:"column:version;not null;comment:实验版本" json:"version"`                                                          // 实验版本
	GroupLimitHour int32           `gorm:"column:group_limit_hour;not null;comment:单方案小时进量限制" json:"group_limit_hour"`                                   // 单方案小时进量限制
	Data           ExpData         `gorm:"column:data;default:'{}';serializer:json" json:"data"`
	CreateTime     time.Time       `gorm:"column:create_time;not null;comment:创建时间" json:"create_time"` // 创建时间
	UpdateTime     time.Time       `gorm:"column:update_time;not null;comment:更新时间" json:"update_time"` // 更新时间
	Groups         []*Group        `gorm:"foreignKey:exp_id" json:"groups"`
	ApprovalTasks  []*ApprovalTask `gorm:"foreignKey:exp_id" json:"approval_tasks"`
	Layer          Layer           `gorm:"foreignKey:layer_id" json:"layer"`
	Batch          Batch           `gorm:"foreignKey:batch_id" json:"batch"`
}

// TableName Exp's table name
func (*Exp) TableName() string {
	return TableNameExp
}
