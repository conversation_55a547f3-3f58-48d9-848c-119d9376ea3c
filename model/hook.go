package model

import (
	"gorm.io/gorm"
	"time"
)

func (m *Project) BeforeSave(_ *gorm.DB) error {
	if m.Data.AllowUserKeys == nil {
		m.Data.AllowUserKeys = []string{}
	}
	if m.FeatureConflict == nil {
		m.FeatureConflict = FeatureConflictRules{}
	}
	return nil
}

func (l *Layer) BeforeCreate(_ *gorm.DB) error {
	l.CreateTime = time.Now()
	l.UpdateTime = time.Now()
	l.State = 1
	return nil
}

func (l *Batch) BeforeCreate(_ *gorm.DB) error {
	l.CreateTime = time.Now()
	l.UpdateTime = time.Now()
	return nil
}

func (e *Exp) BeforeCreate(_ *gorm.DB) error {
	e.CreateTime = time.Now()
	e.UpdateTime = time.Now()
	e.Version = 1
	e.State = ExpStateDraft
	return nil
}

func (e *Exp) BeforeSave(_ *gorm.DB) error {
	e.UpdateTime = time.Now()
	e.Version = e.Version + 1
	return nil
}

func (g *Group) BeforeCreate(_ *gorm.DB) error {
	g.CreateTime = time.Now()
	g.UpdateTime = time.Now()
	return nil
}

func (g *Group) AfterSave(_ *gorm.DB) error {
	g.UpdateTime = time.Now()
	return nil
}
