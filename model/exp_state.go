package model

import (
	"git.7k7k.com/data/abAdmin/idls"
)

type ExpState = idls.ExpState
type ExpStatus = ExpState

// 实验状态：1:草稿 2:待开始 3:运行中 4:运行中不进量 5:已关闭 6:灰度运行中 7:灰度运行中不进量
const (
	ExpStatusDraft                  = idls.ExpStatusDraft
	ExpStatusPending                = idls.ExpStatusPending
	ExpStatusGrayRunning            = idls.ExpStatusGrayRunning
	ExpStatusRunningWithoutFlow     = idls.ExpStatusRunningWithoutFlow
	ExpStatusRunning                = idls.ExpStatusRunning
	ExpStatusGrayRunningWithoutFlow = idls.ExpStatusGrayRunningWithoutFlow
	ExpStatusClosed                 = idls.ExpStatusClosed

	// 兼容分流引流
	ExpStatusRunningNoFlow        = ExpStatusRunningWithoutFlow
	ExpStatusGrayRelease          = ExpStatusGrayRunning
	ExpStatusGrayReleaseNoNewFlow = ExpStatusGrayRunningWithoutFlow
)

// IsClosed 判断实验是否已关闭
func IsClosed[T ExpState | int32](state T) bool {
	return ExpState(state) == ExpStatusClosed
}

// IsFreezed 判断实验是否冻结
func IsFreezed[T ExpState | int32](state T) bool {
	return ExpState(state) == ExpStatusRunningWithoutFlow ||
		ExpState(state) == ExpStatusGrayRunningWithoutFlow
}
