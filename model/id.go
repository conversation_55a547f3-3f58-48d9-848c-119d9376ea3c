package model

func (m Project) GetID() int        { return int(m.ID) }
func (m Project) GetID64() int64    { return m.ID }
func (m Layer) GetID() int          { return int(m.ID) }
func (m Layer) GetID64() int64      { return m.ID }
func (m Exp) GetID() int            { return int(m.ID) }
func (m Exp) GetID64() int64        { return m.ID }
func (m Group) GetID() int          { return int(m.ID) }
func (m Group) GetID64() int64      { return m.ID }
func (m SplitGroup) GetID() int     { return int(m.ID) }
func (m SplitGroup) GetID64() int64 { return m.ID }

func (m User) GetID() int     { return int(m.ID) }
func (m User) GetID64() int64 { return int64(m.ID) }

func (m UserInfo) GetID() int     { return int(m.ID) }
func (m UserInfo) GetID64() int64 { return m.ID }
