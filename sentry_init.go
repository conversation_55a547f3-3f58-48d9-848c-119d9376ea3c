package main

import (
	"time"

	"git.7k7k.com/data/abAdmin/infra/config"
	"github.com/getsentry/sentry-go"
)

func SentryInit(c config.Sentry) (flush func(timeout time.Duration) bool) {
	err := sentry.Init(sentry.ClientOptions{
		Dsn:         c.DSN,
		Environment: c.Env,
		// Debug:            true,
		AttachStacktrace: true,
		EnableTracing:    true,
		TracesSampleRate: 1,
	})
	if err != nil {
		panic("sentry.Init: " + err.Error())
	}

	go func() {
		for range time.Tick(time.Second * 1) {
			// beginAt := time.Now()
			sentry.Flush(30 * time.Second)
			// slog.Info("sentry_flush", "cost", time.Since(beginAt).String())
		}
	}()

	// Flush buffered events before the program terminates.
	// Set the timeout to the maximum duration the program can afford to wait.
	return sentry.Flush
}
