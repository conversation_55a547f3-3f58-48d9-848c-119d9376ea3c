pipeline {
    agent any
    parameters {
        choice(name: 'ENV', choices: ['dev', 'prod'], description: '选择部署的环境')
        string(name: 'BUILD_DESCRIPTION', defaultValue: '', description: '构建描述')
        gitParameter branchFilter: 'origin/(.*)',
                    defaultValue: 'main',
                    name: 'BRANCH',
                    type: 'PT_BRANCH'
    }
    environment {
        // 环境变量
        REMOTE_USER = 'root'
        AWS_REGION = 'us-east-2'
        AWS_ACCOUNT_ID = '************'
        INTERNAL_PORT = 8001 //与dockerfile中的端口一致
        CONFIG_FILE = 'deploy_config.yml'
    }
    stages {
        stage('Initialize') {
            steps {
                script {
                    def jobName = env.JOB_NAME
                    def config = readYaml file: "${env.CONFIG_FILE}"

                    env.BUILD_NUMBER = env.BUILD_NUMBER
                    env.GERRIT_CHANGE_SUBJECT = config.project.name
                    env.REPO_URL = scm.userRemoteConfigs[0].url
//                     env.CORE_REPO_URL = scm.userRemoteConfigs[1].url
                    env.SERVER_PORT = config[params.ENV].vars.server_port
                    env.IMAGE_NAME = config.project.image_name
                    env.CONTAINER_NAME = "${config.project.image_name}-${params.ENV}"
                    env.IMAGE_TAG_NAME = "${config.project.image_name}:${params.ENV}-${env.BUILD_NUMBER}"
                    env.REMOTE_IMAGE_TAG_NAME = "${config.project.image_name}:${params.ENV}"
                    env.TARGET_HOSTS = config[params.ENV].hosts.join(' ')
                    env.PROJECT_DIR = config[params.ENV].vars.project_dir
                    env.CONTAINER_SCALE = config[params.ENV].vars.container_scale
                    env.WORK_DIR = "${WORKSPACE}"

                    if (params.BUILD_DESCRIPTION) {
                        currentBuild.description = params.BUILD_DESCRIPTION
                    }
                }
            }
        }
        stage('Checkout') {
            steps {
                dir("${env.WORK_DIR}") {
                    git branch: "${params.BRANCH}", url: "${env.REPO_URL}", credentialsId: "712675a5-f71f-4c27-8ff8-af3f11c62587"
                }
            }
        }
        stage('Build') {
            steps {
                script {
                    updateBuildDisplayName(isRestartedFromStage(), env.STAGE_NAME)
                    // Build the Docker image
                    docker.build(env.IMAGE_TAG_NAME, "--build-arg ENV=${params.ENV} .").inside("-e ENV=${params.ENV}") {
                        sh 'echo "Docker image built successfully."'
                    }
                }
            }
        }
        stage('Push Image') {
            steps {
                script {
                    updateBuildDisplayName(isRestartedFromStage(), env.STAGE_NAME)
                    def loginDocker = loginEcrDocker()
                    withCredentials([aws(accessKeyVariable: 'AWS_ACCESS_KEY_ID', credentialsId: "${AWS_ACCOUNT_ID}", secretKeyVariable: 'AWS_SECRET_ACCESS_KEY')]) {
                        sh """
                            ${loginDocker}
                            docker tag ${env.IMAGE_TAG_NAME} ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${env.REMOTE_IMAGE_TAG_NAME}
                            docker push ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${env.REMOTE_IMAGE_TAG_NAME}
                        """
                    }
                }
            }
        }

        stage('Deploy') {
            steps {
                script {
                    updateBuildDisplayName(isRestartedFromStage(), env.STAGE_NAME)
                    // Deploy Docker image to multiple hosts
                    def targetHosts = env.TARGET_HOSTS.split(' ')
                    targetHosts.each { targetHost ->
                        for (int i = 0; i < env.CONTAINER_SCALE.toInteger(); i++) {
                            def serverPort = env.SERVER_PORT.toInteger() + i
                            def containerName = "${env.CONTAINER_NAME}-${serverPort}"
                            def afterCommand = deployAfterCommand(serverPort, containerName)
                            withCredentials([aws(accessKeyVariable: 'AWS_ACCESS_KEY_ID', credentialsId: "${AWS_ACCOUNT_ID}", secretKeyVariable: 'AWS_SECRET_ACCESS_KEY')]) {
                                sh """
                                    ssh ${REMOTE_USER}@${targetHost} '${afterCommand}'
                                """
                            }
                        }
                    }
                }
            }
        }
    }
}

def deployAfterCommand(serverPort, containerName) {
    def loginDocker = loginEcrDocker()
    return """
    sudo mkdir -p ${env.PROJECT_DIR}/${serverPort}/logs
    sudo mkdir -p ${env.PROJECT_DIR}/${serverPort}/data
    ${loginDocker}
    sudo docker pull ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${env.REMOTE_IMAGE_TAG_NAME}
    sudo docker stop ${containerName} || true
    sudo docker rm ${containerName} || true
    sudo docker run -d --name ${containerName} \\
    -p ${serverPort}:${INTERNAL_PORT} \\
    --restart always \\
    -v ${env.PROJECT_DIR}/${serverPort}/data:/workspace/golang/data \\
    -v ${env.PROJECT_DIR}/${serverPort}/logs:/workspace/golang/logs \\
    ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${env.REMOTE_IMAGE_TAG_NAME}
    """
}

def loginEcrDocker() {
    return """
    aws ecr get-login-password --region ${AWS_REGION} | sudo docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com
    """.trim()
}

def isRestartedFromStage() {
    def causes = currentBuild.getBuildCauses('org.jenkinsci.plugins.pipeline.modeldefinition.causes.RestartDeclarativePipelineCause')
    return !causes.isEmpty()
}

def updateBuildDisplayName(isRestart = false, currentStage = null) {
    if (isRestart == false) {
        currentBuild.displayName = "${env.GERRIT_CHANGE_SUBJECT}-${env.ENV}-发布#${BUILD_NUMBER}【${currentStage}】"
    } else {
        currentBuild.displayName = "${env.GERRIT_CHANGE_SUBJECT}-${env.ENV}-执行${currentStage}#${currentBuild.number}回滚到#${BUILD_NUMBER}"
    }
}
