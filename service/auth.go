package service

import (
	"context"
	"fmt"
	"log/slog"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/spf13/cast"
	"gorm.io/gorm"

	"git.7k7k.com/data/abAdmin/infra/auth"
	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/repository"
	"git.7k7k.com/data/abAdmin/repository/dao"
	"git.7k7k.com/pkg/common/gosentry"
	"github.com/panjf2000/ants/v2"
	"github.com/samber/lo"
)

// AuthService 查询服务
// @autowire(set=service)
type AuthService struct {
	_curd *CurdBase[model.UserInfo] `wire:"-"`

	UserInfoDo dao.IUserInfoDo

	Query        *dao.Query
	QueryForBIv1 *repository.QueryForBIv1
}

func (c *AuthService) curd() *CurdBase[model.UserInfo] {
	if c._curd == nil {
		c._curd = &CurdBase[model.UserInfo]{
			db: c.UserInfoDo.UnderlyingDB(),
		}
	}
	return c._curd
}

type SaveUserInfoIn struct {
	model.UserInfo
}

type SaveUserInfoOut struct {
	model.UserInfo
}

func (s *AuthService) SaveUserInfo(ctx context.Context, in SaveUserInfoIn) (out SaveUserInfoOut, err error) {
	if in.UserInfo.UserID < 1 {
		err = errors.Errorf("用户ID不能为空")
		return
	}

	user, err := s.QueryForBIv1.User.
		Where(s.QueryForBIv1.User.ID.Eq(int32(in.UserInfo.UserID))).
		Select(s.QueryForBIv1.User.ID, s.QueryForBIv1.User.Account, s.QueryForBIv1.User.Realname).
		First()
	if err != nil {
		return
	}
	in.UserInfo.User = user
	in.UserInfo.SearchTextList = []any{in.User.Account, in.User.Realname}

	for _, prjRole := range in.UserInfo.Data.PrjRoles {
		if prjRole.PrjId == 0 || len(prjRole.RoleIds) == 0 {
			err = errors.Errorf("项目ID和角色ID不能为空")
			return
		}

		roles, err := s.QueryForBIv1.Role.WithContext(ctx).Where(s.QueryForBIv1.Role.ID.In(prjRole.RoleIds...)).WithContext(ctx).Find()
		if err != nil {
			return out, err
		}
		prjRole.Roles = roles

		project, err := s.Query.Project.WithContext(ctx).Where(s.Query.Project.ID.Eq(int64(prjRole.PrjId))).First()
		if err != nil {
			return out, err
		}
		prjRole.Project = project
		displays := []string{}
		for _, role := range roles {
			displays = append(displays, fmt.Sprintf("%s-%s", prjRole.Project.Name, role.ABName()))
		}
		prjRole.Display = strings.Join(displays, "，")

		in.UserInfo.SearchTextList = append(in.UserInfo.SearchTextList,
			project.Name,
			fmt.Sprintf("prj_id_%d", prjRole.PrjId),
		)
		for _, role := range roles {
			in.UserInfo.SearchTextList = append(in.UserInfo.SearchTextList,
				role.ABName(),
				fmt.Sprintf("role_id_%d", role.ID),
			)
		}
	}

	err = s.UserInfoDo.WithContext(ctx).Save(&in.UserInfo)
	if err != nil {
		return
	}

	// if len(in.UserInfo.Data.PrjRoles) == 0 {
	// 	_, _ = s.UserInfoDo.WithContext(ctx).Delete(&in.UserInfo)
	// 	return
	// }

	// TODO clear auth cache
	err = s.RefreshCacheCtx(ctx)

	out.UserInfo = in.UserInfo

	return
}

type UserInfoListIn struct {
	ListIn[model.UserInfo]
	UserID  int64  `json:"user_id,omitempty" form:"user_id"`
	Account string `json:"account,omitempty" form:"account"`
	PrjId   int    `json:"prj_id,omitempty" form:"prj_id"`
	RoleId  string `json:"role_id,omitempty" form:"role_id"`
}

func (c *AuthService) ListUser(ctx context.Context, in UserInfoListIn) (out ListOut[model.UserInfo], err error) {
	query := c.UserInfoDo.UnderlyingDB()
	in.WithQuery(query)
	if in.UserID != 0 {
		in.filter = in.filter.WithContext(ctx).Where("user_id = ?", in.UserID)
	}
	if in.Account != "" {
		in.ListIn.SearchTexts = append(in.ListIn.SearchTexts, in.Account)
	}
	if in.PrjId != 0 {
		in.ListIn.SearchTexts = append(in.ListIn.SearchTexts, fmt.Sprintf("#prj_id_%d#", in.PrjId))
	}
	cnt := 0
	subq := query.Or("FALSE")
	for _, roleId := range strings.Split(in.RoleId, ",") {
		id, _ := strconv.Atoi(roleId)
		if id > 0 {
			subq = subq.Or("search_text LIKE ?", fmt.Sprintf("%%#role_id_%d#%%", id))
			cnt++
		}
	}
	if cnt > 0 {
		in.ListIn.WithQuery(query.WithContext(ctx).Where(subq))
	}

	list, err := c.curd().List(ctx, in.ListIn)
	if err != nil {
		return
	}

	uids := lo.Map(list.Rows, func(item *model.UserInfo, _ int) int32 { return int32(item.UserID) })

	uq := c.QueryForBIv1.User
	users, err := uq.WithContext(ctx).
		Where(uq.ID.In(uids...)).
		Select(uq.ID, uq.Account, uq.Realname, uq.Email, uq.Telephone, uq.Description, uq.CreatedAt, uq.UpdatedAt, uq.LoginAt).
		Find()
	if err != nil {
		return
	}

	userMap := lo.KeyBy(users, func(item *model.User) int32 { return item.ID })
	for _, item := range list.Rows {
		item.User = userMap[int32(item.UserID)]
	}

	return list, nil
}

type StateIn struct {
}

type StateOut struct {
	IsAdmin      bool                       `json:"is_admin"`
	User         *model.User                `json:"user"`
	Perms        []*auth.PermSimple         `json:"perms"`
	ProjectPerms map[int][]*auth.PermSimple `json:"project_perms"`
}

func (c *AuthService) State(ctx context.Context, in StateIn) (out StateOut, err error) {
	uid := auth.GetUserId(ctx)
	out.IsAdmin = auth.IsAdmin(ctx)
	out.Perms = []*auth.PermSimple{}
	out.ProjectPerms = map[int][]*auth.PermSimple{}

	uq := c.QueryForBIv1.User
	user, err := uq.WithContext(ctx).
		Where(uq.ID.Eq(int32(uid))).
		Select(uq.ID, uq.Account, uq.Realname, uq.Email, uq.Telephone, uq.Description, uq.CreatedAt, uq.UpdatedAt, uq.LoginAt).
		First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.Errorf("用户未开放AB平台权限 uid: %d", uid)
			return
		}
		return
	}
	out.User = user

	prjId := auth.GetProjectId(ctx)
	store := auth.GetProjectStore(prjId)
	if store != nil {
		for _, pid := range store.UserPerms[uid] {
			out.Perms = append(out.Perms, store.Perms[pid])
		}
	}

	prjs, err := c.Query.Project.WithContext(ctx).WithContext(ctx).Where(c.Query.Project.State.Eq(1)).WithContext(ctx).Find()
	if err != nil {
		return
	}
	for _, prj := range prjs {
		if auth.InProject(ctx, int(prj.ID)) {
			store := auth.GetProjectStore(int(prj.ID))
			if store != nil {
				for _, pid := range store.UserPerms[uid] {
					out.ProjectPerms[int(prj.ID)] = append(out.ProjectPerms[int(prj.ID)], store.Perms[pid])
				}
			}
		}
	}

	slog.InfoContext(ctx, "state", "is_admin", out.IsAdmin, "user_id", uid, "user_name", user.Realname, "project_id", prjId)

	return
}

type SyncUserIn struct {
	AfterTs int64 `json:"after_ts" form:"after_ts"` // 0=today -1=all
}

type SyncUserOut struct {
	UserIDs   []int32  `json:"user_ids"`
	UserNames []string `json:"user_names"`
}

// SyncUser 同步用户
func (c *AuthService) SyncUser(ctx context.Context, in SyncUserIn) (out SyncUserOut, err error) {
	udo := c.QueryForBIv1.User
	uq := udo.WithContext(ctx).WithContext(ctx).Where(udo.Status.Eq(1))
	if in.AfterTs == 0 {
		in.AfterTs = time.Now().Truncate(time.Hour*24).Unix() - 86400*3
	}
	if in.AfterTs > 0 {
		uq = uq.WithContext(ctx).Where(udo.CreatedAt.Gt(time.Unix(in.AfterTs, 0)))
	}
	users, err := uq.WithContext(ctx).Find()
	if err != nil {
		return
	}

	if len(users) == 0 {
		return
	}

	// exists
	exists, err := c.Query.UserInfo.WithContext(ctx).WithContext(ctx).Find()
	if err != nil {
		return
	}
	existsMap := lo.KeyBy(exists, func(item *model.UserInfo) int64 { return item.UserID })

	pool, err := ants.NewPool(len(users)/20 + 1)
	if err != nil {
		return
	}

	var wg sync.WaitGroup
	for _, user := range users {
		wg.Add(1)
		pool.Submit(func() {
			defer wg.Done()

			if _, ok := existsMap[int64(user.ID)]; ok {
				return
			}

			ui := model.UserInfo{
				UserID: int64(user.ID),
				User:   user,
			}
			_, err = c.SaveUserInfo(ctx, SaveUserInfoIn{UserInfo: ui})
			if err != nil {
				slog.ErrorContext(ctx, "sync user error", "error", err)
				return
			}

			slog.InfoContext(ctx, "sync user", "user_id", user.ID, "user_name", user.Realname, "created_at", user.CreatedAt)
		})
	}
	wg.Wait()
	slog.InfoContext(ctx, "sync user done")

	out.UserIDs = lo.Map(users, func(item *model.User, _ int) int32 { return int32(item.ID) })
	out.UserNames = lo.Map(users, func(item *model.User, _ int) string { return item.Realname })

	err = c.RefreshCacheCtx(ctx)

	return
}

// ShowCacheIn 查询请求
type ShowCacheIn struct {
	ProjectIDs []int
}

// ShowCacheOut 查询结果
type ShowCacheOut struct {
	ProjectAuth map[int]*auth.MemStore `json:"project_auth"`
}

func (q *AuthService) ShowCache(ctx context.Context, in ShowCacheIn) (out ShowCacheOut, err error) {
	projects, err := q.Query.Project.WithContext(ctx).Find()
	if err != nil {
		return
	}

	in.ProjectIDs = lo.Map(projects, func(item *model.Project, _ int) int { return int(item.ID) })
	out.ProjectAuth = make(map[int]*auth.MemStore, len(in.ProjectIDs))

	for _, pid := range in.ProjectIDs {
		store := auth.GetProjectStore(pid)
		out.ProjectAuth[pid] = store
	}

	return
}

// Deprecated
func (s *AuthService) RefreshCache() (err error) {
	return s.RefreshCacheCtx(context.Background())
}

func (s *AuthService) RefreshCacheCtx(ctx context.Context) (err error) {
	// users, err := s.Query.User.WithContext(ctx).Find()
	// if err != nil {
	// 	return
	// }

	_, finish := gosentry.StartSpan(ctx, "select:roles", "roles", "")
	roles, err := s.QueryForBIv1.Role.WithContext(ctx).Where(s.QueryForBIv1.Role.Name.Like("abtest-%")).WithContext(ctx).Find()
	finish()
	if err != nil {
		return
	}
	roleIds := lo.Map(roles, func(item *model.Role, _ int) int32 { return item.ID })

	_, finish = gosentry.StartSpan(ctx, "select:permRole", "permRole", "")
	permRoles, err := s.QueryForBIv1.PermissionRole.WithContext(ctx).Where(s.QueryForBIv1.PermissionRole.RoleID.In(roleIds...)).WithContext(ctx).Find()
	finish()
	if err != nil {
		return
	}
	permIds := lo.Map(permRoles, func(item *model.PermissionRole, _ int) int32 { return item.PermissionID })

	_, finish = gosentry.StartSpan(ctx, "select:perms", "perms", "")
	perms, err := s.QueryForBIv1.Permission.WithContext(ctx).Where(s.QueryForBIv1.Permission.ID.In(permIds...)).WithContext(ctx).Find()
	finish()
	if err != nil {
		return
	}

	_, finish = gosentry.StartSpan(ctx, "select:userinfo", "userinfo", "")
	userInfos, err := s.Query.UserInfo.WithContext(ctx).Find()
	finish()
	if err != nil {
		return
	}

	_, finish = gosentry.StartSpan(ctx, "select:projects", "projects", "")
	projects, err := s.Query.Project.WithContext(ctx).Find()
	finish()
	if err != nil {
		return
	}

	for _, project := range projects {
		_userRoles := map[int][]int{}
		for _, ui := range userInfos {
			for _, prjRole := range ui.Data.PrjRoles {
				if prjRole.PrjId == int(project.ID) {
					_userRoles[int(ui.UserID)] = append(_userRoles[int(ui.UserID)], cast.ToIntSlice(prjRole.RoleIds)...)
				}
			}
		}

		_permRoles := map[int][]int{}
		_rolePerms := map[int][]int{}
		{
			for _, permRole := range permRoles {
				_permRoles[int(permRole.PermissionID)] = append(_permRoles[int(permRole.PermissionID)], int(permRole.RoleID))
				_rolePerms[int(permRole.RoleID)] = append(_rolePerms[int(permRole.RoleID)], int(permRole.PermissionID))
			}
		}

		_userPerms := map[int][]int{}
		{
			for uid, rs := range _userRoles {
				for _, r := range rs {
					_userPerms[uid] = append(_userPerms[uid], _rolePerms[r]...)
				}
				_userPerms[uid] = lo.Uniq(_userPerms[uid])
				sort.Ints(_userPerms[uid])
			}
		}

		_permNameIdx := map[string]int{}
		{
			for _, perm := range perms {
				_permNameIdx[perm.Name] = int(perm.ID)
			}
		}

		_perms := map[int]*auth.PermSimple{}
		{
			for _, perm := range perms {
				_perms[int(perm.ID)] = &auth.PermSimple{
					ID:          perm.ID,
					Name:        perm.Name,
					DisplayName: perm.DisplayName,
				}
			}
		}

		m := auth.MemStore{
			UserRoles:   _userRoles,
			PermRoles:   _permRoles,
			PermNameIdx: _permNameIdx,
			UserPerms:   _userPerms,
			Perms:       _perms,
		}
		auth.RefreshProjects(int(project.ID), &m)

		if project.ID == 1 {
			// dump.Dump(m)
		}
		// slog.Info("refresh project auth", "project_id", project.ID, "result", auth.GetProjectStore(int(project.ID)))
	}

	return
}

// hints
// 如何生成接口文档，提示词
// 这个文件是http的后端接口，请根据struct的定义，写一份接口文档。注意接口字段名不要直接使用Go字段名，而要使用 json tag。请用表格列出所有字段、类型、说明。并给出一份示例请求和响应。
