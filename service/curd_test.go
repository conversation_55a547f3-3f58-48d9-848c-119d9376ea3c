package service

import (
	"context"
	"testing"

	"github.com/glebarez/sqlite"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

type ModelB struct{}

// 定义一个测试用的模型
type TestModel struct {
	ID         uint `gorm:"primarykey" json:"id,string"`
	Name       string
	SearchText string
	ModelB     ModelB `gorm:"column:b;default:'{}';serializer:json" json:"data"`
}

func (c TestModel) GetID() int {
	return int(c.ID)
}

func (TestModel) TableName() string {
	return "test_models"
}

func TestCurdBaseList(t *testing.T) {
	// 设置内存数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	assert.NoError(t, err)

	// 迁移测试模型
	err = db.AutoMigrate(&TestModel{})
	assert.NoError(t, err)

	// 插入测试数据
	testData := []TestModel{
		{Name: "Test1", SearchText: "ABC"},
		{Name: "Test2", SearchText: "DEF"},
		{Name: "Test3", SearchText: "GHI"},
	}
	err = db.Create(&testData).Error
	assert.NoError(t, err)

	// 创建 CurdBase 实例
	curdBase := &CurdBase[TestModel]{db: db}

	// 测试列表查询
	t.Run("List all items", func(t *testing.T) {
		in := ListIn[TestModel]{
			Page: Page{Limit: 10},
		}
		out, err := curdBase.List(context.Background(), in)
		assert.NoError(t, err)
		assert.Len(t, out.Rows, 3)
	})

	// 测试搜索功能
	t.Run("Search items", func(t *testing.T) {
		in := ListIn[TestModel]{
			Page:       Page{Limit: 10},
			SearchText: "ABC",
		}
		out, err := curdBase.List(context.Background(), in)
		assert.NoError(t, err)
		assert.Len(t, out.Rows, 1)
		assert.Equal(t, "Test1", out.Rows[0].Name)
	})

	// 测试分页
	t.Run("Pagination", func(t *testing.T) {
		in := ListIn[TestModel]{
			Page: Page{Limit: 2, PageIndex: 1},
		}
		out, err := curdBase.List(context.Background(), in)
		assert.NoError(t, err)
		assert.Len(t, out.Rows, 2)

		in.Page.PageIndex = 2
		out, err = curdBase.List(context.Background(), in)
		assert.NoError(t, err)
		assert.Len(t, out.Rows, 1)
	})
}
