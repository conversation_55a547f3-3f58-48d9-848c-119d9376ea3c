package service

import (
	"context"
	"git.7k7k.com/data/abAdmin/httpd/request"
	"git.7k7k.com/data/abAdmin/infra/auth"
	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/repository/dao"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"math"
	"time"
)

// @autowire(set=service)
type ExpExtraService struct {
	DS *dao.Query
	ExpService
}

// 对外创建实验
func (s *ExpExtraService) Create(ctx context.Context, req *request.ExtraExp) (any, error) {
	// tx_id 校验
	// 1. 校验实验名称
	err := s.verfiyExpName(req.Name, nil, ctx)
	if err != nil {
		return nil, err
	}
	// 2. 校验实验策略参数
	err = strategyValid(req.Strategy, req.Csr)
	if err != nil {
		return nil, err
	}
	// 3. 校验实验组
	err = s.isValidGroup(ctx, req.Groups)
	if err != nil {
		return nil, err
	}
	exp := &model.Exp{}
	groups := make([]*model.Group, 0, 100)
	err = copier.Copy(exp, req)
	if err != nil {
		return nil, err
	}
	err = copier.Copy(&groups, req.Groups)
	if err != nil {
		return nil, err
	}
	// 3. 创建实验
	exp.State = model.ExpStateTodo
	exp.UpdateTime = time.Now()
	exp.CreateTime = time.Now()
	exp.UID = cast.ToInt32(auth.GetUserId(ctx))
	exp.Step = 4
	exp.Version = 1
	exp.ProjectID = auth.GetProjectId64(ctx)
	for _, g := range exp.Groups {
		g.ExpID = exp.ID
		g.CreateTime = time.Now()
		g.UpdateTime = time.Now()
		g.State = model.GroupStateTodo
	}

	err = s.DS.Transaction(func(tx *dao.Query) error {
		err = tx.Exp.WithContext(ctx).Debug().Create(exp)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return nil, nil
}

// 对外更新实验
func (s *ExpExtraService) Update(ctx context.Context, req *request.ExtraExp) (any, error) {
	expDao := s.DS.Exp
	groupDao := s.DS.Group
	// 校验实验信息
	originExp, err := s.isValidExp(req.Id, math.MaxInt64, ctx)
	if err != nil {
		return nil, err
	}
	err = copier.Copy(originExp, req)
	if err != nil {
		return nil, err
	}
	err = s.isValidGroup(ctx, req.Groups)
	if err != nil {
		return nil, err
	}
	// todo 是否校验实验状态，运行中，不允许修改
	originGroups, _ := groupDao.Select(groupDao.ID, groupDao.ExpID).Where(groupDao.ExpID.Eq(req.Id)).Where(groupDao.IsDeleted.Eq(model.NotDeleted)).Find()
	addGroups := make([]*model.Group, 0, 10)
	updateGroups := make([]*model.Group, 0, 10)
	for _, groupReq := range req.Groups {
		group := &model.Group{}
		err = copier.Copy(group, groupReq)
		if err != nil {
			return 0, err
		}
		group.State = originExp.State
		if groupReq.GroupId != 0 {
			group.ID = groupReq.GroupId
			group.ExpID = req.Id
			group.Name = groupReq.Name
			group.UpdateTime = time.Now()
			updateGroups = append(updateGroups, group)
		} else {
			group.ExpID = req.Id
			group.CreateTime = time.Now()
			group.UpdateTime = time.Now()
			addGroups = append(addGroups, group)
		}
	}
	updateGroupMap := lo.KeyBy(updateGroups, func(group *model.Group) int64 {
		return group.ID
	})
	deleteGroupIds := make([]int64, 0, len(updateGroupMap))
	for _, group := range originGroups {
		if _, ok := updateGroupMap[group.ID]; !ok {
			deleteGroupIds = append(deleteGroupIds, group.ID)
		}

	}
	originExp.Groups = nil
	err = s.DS.Transaction(func(tx *dao.Query) error {
		// 2. 更新方案
		if len(deleteGroupIds) > 0 {
			_, err = tx.Group.Debug().
				Where(groupDao.ExpID.Eq(req.Id)).
				Where(groupDao.ID.In(deleteGroupIds...)).
				Update(groupDao.IsDeleted, model.Deleted)
			if err != nil {
				return err
			}
		}
		if len(addGroups) > 0 {
			err = tx.Group.Debug().CreateInBatches(addGroups, len(addGroups))
			if err != nil {
				return err
			}
		}
		if len(updateGroups) > 0 {
			for _, ug := range updateGroups {
				_, err = tx.Group.Debug().Where(groupDao.ID.Eq(ug.ID)).Where(groupDao.ExpID.Eq(ug.ExpID)).Updates(ug)
				if err != nil {
					return err
				}
			}
		}
		if err != nil {
			return err
		}
		return expDao.Where(expDao.ID.Eq(req.Id)).Save(originExp)
	})
	if err != nil {
		return nil, err
	}
	return nil, nil
}
