package service

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"git.7k7k.com/data/abAdmin/httpd/response"
	"git.7k7k.com/data/abAdmin/infra/auth"
	"git.7k7k.com/data/abAdmin/infra/config"
	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/model/dto"
	"git.7k7k.com/data/abAdmin/repository/dao"
	"github.com/cockroachdb/errors"
	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
	"log/slog"
	"net/url"
	"strings"
	"time"
)

// Query 查询服务
// @autowire(set=service)
type ExpBatchService struct {
	DS              *dao.Query
	LayerService    *LayerService
	MetricService   *MetricService
	ExpStateService *ExpStateService
	ExpLogService   *ExpLogService
	GroupService    *GroupService
	ExpService      *ExpService
	StrategyService *StrategyService
}
type ErrorInfo struct {
	ExpName string `json:"exp_name"`
	Msg     string `json:"msg"`
}

var ErrLayerIsEmpty = errors.New("流量层流量限制，调整为0%")

func (s ExpBatchService) GetExpLayerList(ctx context.Context, expList []*model.ExpInfo) ([]*model.Exp, error) {
	layerName := lo.Map(expList, func(item *model.ExpInfo, index int) string {
		return item.LayerName
	})
	layerDao := s.DS.Layer
	layerList, err := layerDao.
		Where(layerDao.Name.In(layerName...)).
		Where(layerDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Where(layerDao.State.In(model.StateOnline)).
		Find()
	if err != nil {
		return nil, err
	}
	layerIds := lo.Map(layerList, func(item *model.Layer, index int) int64 {
		return item.ID
	})
	expDao := s.DS.Exp
	expLayerList, err := expDao.
		Where(expDao.LayerID.In(layerIds...)).
		Where(expDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Where(expDao.State.In(model.ExpStateRunning, model.ExpStateGrayRunning, model.ExpStateTodo)).
		Where(expDao.IsDeleted.Eq(model.NotDeleted)).
		Find()
	if err != nil {
		return nil, err
	}
	return expLayerList, nil
}
func GenerateExpKey(explist []*model.ExpInfo) (string, error) {
	jsonString, err := json.Marshal(explist)
	if err != nil {
		return "", fmt.Errorf("json marshal error: %v", err)
	}
	hasher := sha256.New()
	hasher.Write([]byte(jsonString))
	expKey := hex.EncodeToString(hasher.Sum(nil))
	return expKey, nil
}
func IsValidExpKey(expList *model.ExpInfoList, key string) (bool, error) {
	expKey, err := GenerateExpKey(expList.List)
	if err != nil {
		return false, err
	}
	if expKey == key {
		return true, nil
	}
	return false, nil
}
func (s *ExpBatchService) Upload(ctx *gin.Context, expList *model.ExpInfoList) (any, error) {
	if len(expList.List) == 0 {
		return nil, fmt.Errorf("expList is empty")
	}
	isValid, err := IsValidExpKey(expList, expList.Key)
	if err != nil {
		return nil, err
	}
	if !isValid {
		return nil, fmt.Errorf("文件修改后无法上传，请确认文件从平台导出后直接上传，请勿修改")
	}
	slog.InfoContext(ctx, "Exp_Batch_Create", slog.Any("params", expList), "uid", auth.GetUserId(ctx))
	ret := make(map[string]any, 0)
	var errorInfoList []*ErrorInfo

	// 校验参数
	expLayerList, err := s.GetExpLayerList(ctx, expList.List)
	if err != nil {
		return nil, err
	}
	// 分层校验
	for _, expInfo := range expList.List {
		err = s.checkExpInfo(ctx, expInfo, &expLayerList)
		if err != nil {
			errorInfoList = append(errorInfoList, &ErrorInfo{
				ExpName: expInfo.Name,
				Msg:     err.Error(),
			})
		}
	}
	if len(errorInfoList) > 0 {
		ret["tips"] = make([]ErrorInfo, 0)
		ret["errors"] = make([]ErrorInfo, 0)
		for _, errorInfo := range errorInfoList {
			if errorInfo.Msg == ErrLayerIsEmpty.Error() {
				ret["tips"] = append(ret["tips"].([]ErrorInfo), *errorInfo)
			} else {
				ret["errors"] = append(ret["errors"].([]ErrorInfo), *errorInfo)
				return ret, nil
			}
		}

	}
	expParams := make([]*model.Exp, 0)
	// 数据转化
	for _, expInfo := range expList.List {
		expParam := &model.Exp{}
		err := s.convertExpInfoToModelExp(ctx, expInfo, expParam, s.DS)
		if err != nil {
			return nil, err
		}
		expParams = append(expParams, expParam)
	}

	// 校验数据
	conflictList := make([]*response.ConflictGroup, 0)
	err = s.DS.Transaction(func(tx *dao.Query) error {
		for _, expParam := range expParams {
			// 基本信息
			_, err := s.ExpService.SaveBaseV2(ctx, expParam, tx)
			if err != nil {
				return err
			}
			// 策略
			_, err = s.ExpService.SaveStrategyV2(ctx, expParam, tx)
			if err != nil {
				return err
			}
			// 添加方案
			_, conflict, err := s.ExpService.SaveGroupsV2(ctx, expParam, true, tx)
			if err != nil {
				return err
			}
			if len(conflict) != 0 {
				conflictList = append(conflictList, conflict...)
			}
			// 修改状态
			_, err = s.ExpService.SaveStatus(ctx, expParam, tx)
			if err != nil {
				return err
			}
		}

		return nil
	})
	if err != nil {
		return nil, err
	}
	if len(conflictList) != 0 {
		ret["conflict"] = conflictList
	}
	return ret, nil
}

func (s *ExpBatchService) convertExpInfoToModelExp(ctx context.Context, expInfo *model.ExpInfo, exp *model.Exp, tx *dao.Query) error {
	err := copier.Copy(exp, expInfo)
	if err != nil {
		return err
	}
	// 批次处理
	var batch *model.Batch
	if expInfo.BatchName != "" {
		// 查看批次是否存在
		batch, err = tx.Batch.WithContext(ctx).
			Where(tx.Batch.Name.Eq(expInfo.BatchName)).
			Where(tx.Batch.IsDeleted.Eq(model.NotDeleted)).
			Where(tx.Batch.ProjectID.Eq(auth.GetProjectId64(ctx))).
			First()
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
		if batch != nil {
			exp.BatchID = batch.ID
		} else {
			// 创建批次
			batch = &model.Batch{
				Name:      expInfo.BatchName,
				ProjectID: auth.GetProjectId64(ctx),
			}
			err = tx.Batch.WithContext(ctx).Create(batch)
			if err != nil {
				return err
			}
			exp.BatchID = batch.ID
		}
	}
	layer, err := tx.Layer.WithContext(ctx).
		Where(tx.Layer.Name.Eq(expInfo.LayerName)).
		Where(tx.Layer.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Take()
	if err != nil {
		return err
	}
	exp.LayerID = layer.ID
	// 标签处理
	if expInfo.TagName != "" {
		tagIds, err := tx.Tag.WithContext(ctx).
			Where(tx.Tag.ProjectID.Eq(auth.GetProjectId64(ctx))).
			Where(tx.Tag.Name.In(strings.Split(expInfo.TagName, ",")...)).
			Find()
		if err != nil {
			return err
		}
		exp.TagID = strings.Join(lo.Map(tagIds, func(item *model.Tag, index int) string {
			return cast.ToString(item.ID)
		}), ",")
	}
	for _, group := range expInfo.Groups {
		group.ID = 0
		group.ExpID = 0
	}
	return nil
}

func (s *ExpBatchService) checkExpInfo(ctx context.Context, expInfo *model.ExpInfo, expLayerList *[]*model.Exp) error {
	// 实验名字为空，校验
	if strings.TrimSpace(expInfo.Name) == "" {
		return errors.New("实验名字不能为空")
	}
	expDao := s.DS.Exp
	exist, err := expDao.WithContext(ctx).
		Where(expDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Where(expDao.Name.Eq(expInfo.Name)).
		Where(expDao.IsDeleted.Eq(model.NotDeleted)).
		Take()
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
	}
	if exist != nil {
		return fmt.Errorf("【%s】已存在", expInfo.Name)
	}
	if _, ok := model.ExpTypeMap[int(expInfo.ExpType)]; !ok {
		return errors.New("实验类型不合法")
	}
	// 分层处理
	var layer *model.Layer
	if expInfo.LayerName != "" {
		layerDao := s.DS.Layer
		// 查看分层是否存在
		layer, err = layerDao.WithContext(ctx).
			Where(layerDao.Name.Eq(expInfo.LayerName)).
			Where(layerDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
			Take()
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("不存在流量层【%s】", expInfo.LayerName)
			}
		}
	}
	// 策略校验
	if expInfo.Strategy != nil {
		err = s.ValidStrategies(ctx, expInfo)
		if err != nil {
			return err
		}
	}
	// 分层流量校验
	if expInfo.LayerRate != 0 && layer != nil {
		expWithLayers := lo.GroupBy(*expLayerList, func(item *model.Exp) int64 {
			return item.LayerID
		})
		expWithLayersList, ok := expWithLayers[layer.ID]
		if !ok {
			*expLayerList = append(*expLayerList, &model.Exp{
				LayerID:   layer.ID,
				LayerRate: expInfo.LayerRate,
				Name:      expInfo.Name,
			})
			return nil
		}
		sumLayerRate := lo.SumBy(expWithLayersList, func(item *model.Exp) int32 {
			return item.LayerRate
		})
		// 分层容量校验
		if sumLayerRate+expInfo.LayerRate > 100 {
			expInfo.LayerRate = 0
			*expLayerList = append(*expLayerList, &model.Exp{
				LayerID:   layer.ID,
				LayerRate: 0,
				Name:      expInfo.Name,
			})
			return ErrLayerIsEmpty
		} else {
			*expLayerList = append(*expLayerList, &model.Exp{
				LayerID:   layer.ID,
				LayerRate: expInfo.LayerRate,
				Name:      expInfo.Name,
			})
			return nil
		}
	}
	return nil
}
func GetMissingKeys(filter *model.Condition, allowed []string) []string {
	keys := make(map[string]struct{})
	collectKeys(filter, keys)

	allowedSet := make(map[string]struct{})
	for _, k := range allowed {
		allowedSet[k] = struct{}{}
	}

	var missing []string
	for key := range keys {
		if _, ok := allowedSet[key]; !ok {
			missing = append(missing, key)
		}
	}

	return missing
}

func collectKeys(f *model.Condition, keys map[string]struct{}) {
	if f == nil {
		return
	}
	if f.Key != "" {
		keys[f.Key] = struct{}{}
	}
	for _, cond := range f.Conditions {
		collectKeys(cond, keys)
	}
}
func (s *ExpBatchService) ValidStrategies(ctx context.Context, expInfo *model.ExpInfo) error {
	projectDao := s.DS.Project
	prj, err := projectDao.Where(projectDao.ID.Eq(auth.GetProjectId64(ctx))).First()
	if err != nil {
		return errors.Wrapf(err, "查询项目失败")
	}
	allUserKeys := lo.Map(lo.Values(config.ExpressionKeys), func(item *dto.ExpressionKey, index int) string {
		return item.Value
	})
	var getMissingKeys []string
	if expInfo.Strategy.TriggerUserKey != nil {
		if len(prj.Data.AllowUserKeys) > 0 {
			getMissingKeys = GetMissingKeys(expInfo.Strategy.TriggerUserKey, prj.Data.AllowUserKeys)
		} else {
			getMissingKeys = GetMissingKeys(expInfo.Strategy.TriggerUserKey, allUserKeys)
		}
		if len(getMissingKeys) != 0 {
			return fmt.Errorf("触发用户规则无关联字段：%s", strings.Join(getMissingKeys, ","))
		}
	}
	if expInfo.Strategy.TriggerEndKey != nil {
		getMissingKeys = GetMissingKeys(expInfo.Strategy.TriggerEndKey, allUserKeys)
		if len(getMissingKeys) != 0 {
			return fmt.Errorf("关闭触发规则无关联字段：%s", strings.Join(getMissingKeys, ","))
		}
	}
	if expInfo.Strategy.TriggerStopKey != nil {
		getMissingKeys = GetMissingKeys(expInfo.Strategy.TriggerStopKey, allUserKeys)
		if len(getMissingKeys) != 0 {
			return fmt.Errorf("停止进量触发规则无关联字段：%s", strings.Join(getMissingKeys, ","))
		}
	}
	return nil
}

func (s *ExpBatchService) Download(ctx *gin.Context, downloadType string, expIds []int64) error {
	switch downloadType {
	case "excel":
		return s.Excel(ctx, expIds)
	case "json":
		return s.Json(ctx, expIds)
	default:
		return fmt.Errorf("unknown download type")
	}
}

// 获取 explist
func (s *ExpBatchService) getExpList(ctx context.Context, expIds []int64) (list []*model.Exp, err error) {
	expDao := s.DS.Exp
	groupDao := s.DS.Group
	expInfoList, err := expDao.WithContext(ctx).
		Preload(expDao.Groups.On(groupDao.IsDeleted.Eq(model.NotDeleted)), expDao.Layer, expDao.Batch).
		Where(expDao.ID.In(expIds...)).Find()
	return expInfoList, err
}

// 返回json
func (s *ExpBatchService) Json(ctx *gin.Context, expIds []int64) error {
	expList, err := s.getExpList(ctx, expIds)
	if err != nil {
		return err
	}

	list := make([]*model.ExpInfo, 0, len(expList))
	for _, expInfo := range expList {
		tagName, _ := s.ExpService.TagIdsToTagNames(expInfo.TagID)
		newExpInfo := &model.ExpInfo{
			Name:           expInfo.Name,
			ExpType:        expInfo.ExpType,
			BatchName:      expInfo.Batch.Name,
			TagName:        tagName,
			Days:           expInfo.Days,
			Owner:          expInfo.Owner,
			LayerName:      expInfo.Layer.Name,
			LayerRate:      expInfo.LayerRate,
			Desc:           expInfo.Desc,
			Strategy:       expInfo.Strategy,
			GroupLimitHour: expInfo.GroupLimitHour,
			Csr:            expInfo.Csr,
			Groups:         expInfo.Groups,
		}
		list = append(list, newExpInfo)
	}
	key, err := GenerateExpKey(list)
	if err != nil {
		return err
	}
	expInfoList := &model.ExpInfoList{
		List: list,
		Key:  key,
	}
	// 序列化为 JSON（带缩进，方便阅读）
	jsonData, _ := json.MarshalIndent(expInfoList, "", "  ")
	// 生成文件名字
	fileName := fmt.Sprintf("AB导出实验_%d.json", time.Now().Unix())
	encodedFilename := url.QueryEscape(fileName) //
	// 设置响应头，触发文件下载
	ctx.Header("Content-Disposition", "attachment; filename="+encodedFilename)
	ctx.Header("Content-Transfer-Encoding", "binary")
	ctx.Data(200, "application/octet-stream", jsonData)
	return nil
}

// 返回excel
func (s *ExpBatchService) Excel(ctx *gin.Context, expIds []int64) error {
	expInfoList, err := s.getExpList(ctx, expIds)
	if err != nil {
		return err
	}
	// 初始化 Excel 文件
	file := excelize.NewFile()
	defer func() {
		if err = file.Close(); err != nil {
			return
		}
	}()
	// 设置表头
	err = s.Header(file)
	if err != nil {
		return err
	}

	var end int
	start := 2
	// 写入实验数据
	for _, expInfo := range expInfoList {
		row := start
		groupRate := s.GetGroupRate(expInfo.Groups)
		for _, group := range expInfo.Groups {
			s.setCellValues(file, row, expInfo, group, groupRate)
			row++
		}
		end = row
		// 合并单元格
		if err = mergeColumn("Sheet1", start, end-1, 1, 20, file); err != nil {
			return err
		}
		start = row
	}
	// 设置响应头
	fileName := fmt.Sprintf("AB导出实验_%d.xlsx", time.Now().Unix())
	encodedFilename := url.QueryEscape(fileName) //
	ctx.Header("Content-Type", "application/octet-stream")
	ctx.Header("Content-Disposition", "attachment; filename="+encodedFilename)
	ctx.Header("Content-Transfer-Encoding", "binary")
	// 保存并返回文件
	if err = file.Write(ctx.Writer); err != nil {
		return err
	}
	return nil
}

// 设置单元格
func (s *ExpBatchService) setCellValues(file *excelize.File, row int, expInfo *model.Exp, group *model.Group, groupRate string) {
	setCell := func(col string, value interface{}) {
		_ = file.SetCellValue("Sheet1", col+cast.ToString(row), value)
	}
	// 文字 转化
	convert := func(b bool) string {
		if b {
			return "是"
		}
		return "否"
	}
	// csr 转化
	getCsr := func(csrArr []*model.CSRData) string {
		if len(csrArr) == 0 {
			return ""
		}
		var ret string
		for _, csr := range csrArr {
			val := csr.GenerateExpression()
			ret += val
			ret += "\n"
		}
		return ret
	}

	// 实验信息
	setCell("A", expInfo.ID)
	setCell("B", expInfo.Name)
	setCell("C", model.ExpStateMap[int(expInfo.State)])
	setCell("D", model.ExpTypeMap[int(expInfo.ExpType)])
	setCell("F", cast.ToString(expInfo.Days)+"天")

	// 时间信息
	var startTime, endTime string
	if expInfo.StartTime != nil {
		startTime = cast.ToTime(expInfo.StartTime).Format("2006-01-02 15:04:05")
		endTime = cast.ToTime(expInfo.EndTime).Format("2006-01-02 15:04:05")
	}
	expRunTime, _ := getDaysHours(startTime, endTime)
	setCell("E", expRunTime)
	setCell("G", startTime)

	// 负责人和标签
	var OwnerName string
	if expInfo.Owner != "" {
		userNames, _ := s.ExpService.UserIdsToUserNames(expInfo.Owner)
		OwnerName = strings.Join(userNames, ",")
	}
	setCell("H", OwnerName)

	var tagName string
	if expInfo.TagID != "" {
		tagNames, _ := s.ExpService.TagIdsToTagNames(expInfo.TagID)
		tagName = tagNames
	}
	setCell("I", tagName)

	// 实验基本信息
	setCell("J", expInfo.Batch.Name)
	setCell("K", expInfo.Desc)
	setCell("L", expInfo.Layer.Name)
	setCell("M", cast.ToString(expInfo.LayerRate)+"%")
	setCell("N", expInfo.GroupLimitHour)

	// 策略信息
	setCell("O", convert(expInfo.Strategy.TriggerOnce))
	setCell("P", s.StrategyService.GenerateExpression(expInfo.Strategy.TriggerUserKey))
	setCell("Q", s.StrategyService.GenerateExpression(expInfo.Strategy.TriggerStopKey))
	setCell("R", s.StrategyService.GenerateExpression(expInfo.Strategy.TriggerEndKey))
	setCell("S", getCsr(expInfo.Csr))

	// 方案信息
	setCell("T", group.ID)
	setCell("U", group.Name)
	setCell("V", model.GroupTypeMap[int(group.GroupType)])
	setCell("W", group.Desc)
	setCell("X", group.ParamsContent)
	setCell("Y", expInfo.Batch.Name)

	// 流量和状态
	if (group.State == model.GroupStateRunning && group.IsProgress == 1) || (group.State == model.GroupStateTodo) {
		setCell("Z", groupRate)
	} else {
		setCell("Z", "0%")
	}
	setCell("AA", model.GroupStateMap[int(group.State)])
}

func (s *ExpBatchService) GetGroupRate(groups []*model.Group) string {
	var num int
	for _, group := range groups {
		if (group.State == model.GroupStateRunning && group.IsProgress == 1) || group.State == model.GroupStateTodo {
			num++
		}
	}
	rate := float64(1) / float64(num) * 100
	// 如果rate是整数，返回整数形式，否则返回保留一位小数的字符串
	if rate == float64(int(rate)) {
		return fmt.Sprintf("%d%%", int(rate))
	}
	return fmt.Sprintf("%.1f%%", rate)
}

func (s *ExpBatchService) Header(file *excelize.File) error {
	// 表头
	headers := []string{
		"实验ID", "实验名字", "实验状态", "实验类型", "实验时长",
		"运行时间", "生效时间", "负责人", "实验标签", "实验批次",
		"实验描述", "流量层", "流量占比", "小时限量", "进入试验后不再判断触发条件",
		"触发用户规则", "停止进量触发规则", "关闭触发规则", "分流用户分群划分",
		"方案ID", "方案名称", "对照组/实验组", "描述", "参数", "批次", "流量", "状态",
	}
	for col, header := range headers {
		cell, err := excelize.CoordinatesToCellName(col+1, 1)
		if err != nil {
			return err
		}
		err = file.SetCellValue("Sheet1", cell, header)
		if err != nil {
			return err
		}
	}

	// 可选：加粗标题行
	boldStyle, err := file.NewStyle(&excelize.Style{Font: &excelize.Font{Bold: true}})
	if err != nil {
		return err
	}
	err = file.SetRowStyle("Sheet1", 1, 1, boldStyle)
	if err != nil {
		return err
	}
	return nil
}

// 合并单元格
func mergeColumn(sheet string, rowIndex int, rowIndexEnd int, colIndex int, colIndexEnd int, f *excelize.File) error {
	for j := colIndex; j < colIndexEnd; j++ {
		startCell, _ := excelize.CoordinatesToCellName(j, rowIndex)
		endCell, _ := excelize.CoordinatesToCellName(j, rowIndexEnd)
		if err := f.MergeCell(sheet, startCell, endCell); err != nil {
			return err
		}
	}
	centerStyle, err := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{
			Horizontal: "center", // 水平居中
			Vertical:   "center", // 垂直居中
		},
	})
	if err != nil {
		return err
	}
	if err = f.SetColStyle(sheet, "A:S", centerStyle); err != nil {
		return err
	}

	return nil
}
