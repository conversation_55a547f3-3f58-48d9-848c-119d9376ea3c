package service

import (
	"fmt"
	"testing"
	"time"

	"git.7k7k.com/data/abAdmin/httpd/request"
	"git.7k7k.com/data/abAdmin/infra/auth"
	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/repository/dao"
	"github.com/gin-gonic/gin"
	"github.com/glebarez/sqlite"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

// setupTestDB 设置测试数据库
func setupTestDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	assert.NoError(t, err)

	// 迁移表结构
	err = db.AutoMigrate(&model.Layer{}, &model.Exp{})
	assert.NoError(t, err)

	return db
}

// setupTestContext 设置测试上下文
func setupTestContext() *gin.Context {
	ctx := gin.Context{}
	ctx.Set(auth.ContextKeyProjectID, int64(1))
	return &ctx
}

func TestLayerService_Add(t *testing.T) {
	// 设置测试数据库
	db := setupTestDB(t)
	query := dao.Use(db)
	layerService := &LayerService{DS: query}

	// 设置测试上下文
	ctx := setupTestContext()

	tests := []struct {
		name    string
		req     request.AddLayerReq
		wantErr bool
	}{
		{
			name: "正常添加流量层",
			req: request.AddLayerReq{
				Name:        "test_layer",
				IsExclusion: 0,
			},
			wantErr: false,
		},
		{
			name: "添加重复名称的流量层",
			req: request.AddLayerReq{
				Name:        "test_layer",
				IsExclusion: 0,
			},
			wantErr: true,
		},
		{
			name: "添加互斥流量层",
			req: request.AddLayerReq{
				Name:        "exclusive_layer",
				IsExclusion: 1,
			},
			wantErr: false,
		},
		{
			name: "添加第二个互斥流量层",
			req: request.AddLayerReq{
				Name:        "exclusive_layer_2",
				IsExclusion: 1,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := layerService.Add(ctx, tt.req)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestLayerService_List(t *testing.T) {
	// 设置测试数据库
	db := setupTestDB(t)
	query := dao.Use(db)
	layerService := &LayerService{DS: query}

	// 设置测试上下文
	ctx := setupTestContext()

	// 插入测试数据
	testLayers := []model.Layer{
		{
			Name:       "layer1",
			ProjectID:  1,
			State:      1,
			CreateTime: time.Now(),
			UpdateTime: time.Now(),
		},
		{
			Name:       "layer2",
			ProjectID:  1,
			State:      1,
			CreateTime: time.Now(),
			UpdateTime: time.Now(),
		},
	}
	err := db.Create(&testLayers).Error
	assert.NoError(t, err)

	tests := []struct {
		name      string
		req       request.ListLayerReq
		wantCount int
		wantErr   bool
	}{
		{
			name: "列出所有在线流量层",
			req: request.ListLayerReq{
				Page:     1,
				PageSize: 10,
				State:    1,
			},
			wantCount: 2,
			wantErr:   false,
		},
		{
			name: "按名称搜索流量层",
			req: request.ListLayerReq{
				Page:     1,
				PageSize: 10,
				State:    1,
				Search:   "layer1",
			},
			wantCount: 1,
			wantErr:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := layerService.List(ctx, tt.req)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				resultMap := result.(map[string]any)
				list := resultMap["list"].([]*model.Layer)
				assert.Equal(t, tt.wantCount, len(list))
			}
		})
	}
}

func TestLayerService_Update(t *testing.T) {
	// 设置测试数据库
	db := setupTestDB(t)
	query := dao.Use(db)
	layerService := &LayerService{DS: query}

	// 设置测试上下文
	ctx := setupTestContext()

	// 插入测试数据
	testLayer := model.Layer{
		Name:        "test_layer",
		ProjectID:   1,
		State:       1,
		IsExclusion: 0,
		CreateTime:  time.Now(),
		UpdateTime:  time.Now(),
	}
	err := db.Create(&testLayer).Error
	assert.NoError(t, err)

	tests := []struct {
		name    string
		req     request.UpdateLayerReq
		wantErr bool
	}{
		{
			name: "正常更新流量层",
			req: request.UpdateLayerReq{
				Id:          testLayer.ID,
				Name:        "updated_layer",
				IsExclusion: 0,
			},
			wantErr: false,
		},
		{
			name: "尝试更改互斥状态",
			req: request.UpdateLayerReq{
				Id:          testLayer.ID,
				Name:        "updated_layer",
				IsExclusion: 1,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := layerService.Update(tt.req, ctx)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)

				// 验证更新结果
				var updatedLayer model.Layer
				err = db.First(&updatedLayer, testLayer.ID).Error
				assert.NoError(t, err)
				assert.Equal(t, tt.req.Name, updatedLayer.Name)
			}
		})
	}
}

func TestLayerService_AvailableRate(t *testing.T) {
	// 设置测试数据库
	db := setupTestDB(t)
	query := dao.Use(db)
	layerService := &LayerService{DS: query}

	// 创建测试流量层
	testLayer := model.Layer{
		ID:         1,
		Name:       "test_layer",
		ProjectID:  1,
		State:      1,
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
	}
	err := db.Create(&testLayer).Error
	assert.NoError(t, err)

	// 创建测试实验
	testExps := []model.Exp{
		{
			LayerID:    1,
			LayerRate:  30,
			State:      model.ExpStateRunning,
			IsDeleted:  model.NotDeleted,
			CreateTime: time.Now(),
			UpdateTime: time.Now(),
		},
		{
			LayerID:    1,
			LayerRate:  40,
			State:      model.ExpStateRunning,
			IsDeleted:  model.NotDeleted,
			CreateTime: time.Now(),
			UpdateTime: time.Now(),
		},
	}
	err = db.Create(&testExps).Error
	assert.NoError(t, err)

	tests := []struct {
		name       string
		layerId    int64
		excludeId  int64
		rate       int32
		wantErr    bool
		errMessage string
	}{
		{
			name:      "可用流量充足",
			layerId:   1,
			excludeId: 0,
			rate:      20,
			wantErr:   false,
		},
		{
			name:       "流量超出限制",
			layerId:    1,
			excludeId:  0,
			rate:       1000,
			wantErr:    true,
			errMessage: "流量层缺乏足够的流量容量",
		},
		{
			name:      "排除一个实验后流量充足",
			layerId:   1,
			excludeId: testExps[0].ID,
			rate:      40,
			wantErr:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := layerService.AvailableRate(tt.layerId, tt.excludeId, tt.rate)
			if tt.wantErr {
				assert.Error(t, err)
				fmt.Println(err, tt.wantErr)
				assert.Contains(t, err.Error(), tt.errMessage)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestLayerService_QueryLayerByIds(t *testing.T) {
	// 设置测试数据库
	db := setupTestDB(t)
	query := dao.Use(db)
	layerService := &LayerService{DS: query}

	// 插入测试数据
	testLayers := []model.Layer{
		{
			Name:       "layer1",
			ProjectID:  1,
			State:      1,
			CreateTime: time.Now(),
			UpdateTime: time.Now(),
		},
		{
			Name:       "layer2",
			ProjectID:  1,
			State:      1,
			CreateTime: time.Now(),
			UpdateTime: time.Now(),
		},
	}
	err := db.Create(&testLayers).Error
	assert.NoError(t, err)

	tests := []struct {
		name      string
		ids       []int64
		wantCount int
		wantErr   bool
	}{
		{
			name:      "查询单个流量层",
			ids:       []int64{testLayers[0].ID},
			wantCount: 1,
			wantErr:   false,
		},
		{
			name:      "查询多个流量层",
			ids:       []int64{testLayers[0].ID, testLayers[1].ID},
			wantCount: 2,
			wantErr:   false,
		},
		{
			name:    "空ID列表",
			ids:     []int64{},
			wantErr: true,
		},
		{
			name:      "查询不存在的流量层",
			ids:       []int64{999},
			wantCount: 0,
			wantErr:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			layers, err := layerService.QueryLayerByIds(tt.ids...)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantCount, len(layers))
			}
		})
	}
}
