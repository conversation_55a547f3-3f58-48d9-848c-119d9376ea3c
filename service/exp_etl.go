package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/samber/lo"
	"io"
	"log/slog"
	"net/http"
	"regexp"
	"sort"
	"strings"
	"time"

	"git.7k7k.com/data/abAdmin/infra/config"
	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/repository/dao"
	"git.7k7k.com/pkg/common/ctxs"
	"git.7k7k.com/pkg/common/logs"
	"git.7k7k.com/pkg/storage/codec"
	"github.com/cockroachdb/errors"
	"github.com/spf13/cast"
)

// ExpSyncService 实验同步服务
type ExpSyncService struct {
	DS           *dao.Query
	ExpService   *ExpService
	GroupService *GroupService
	Events       *Events
	syncers      []anySyncer // 使用类型擦除包装器
}

// SyncContext 同步上下文
type SyncContext struct {
	Exp     *model.Exp     // 实验数据
	Tags    []*model.Tag   // 标签数据
	Groups  []*model.Group // 实验组数据
	Project *model.Project // 项目数据
}

// ExpSyncAdapter 实验同步接口
type ExpSyncAdapter[T any] interface {
	// Transform 转换实验数据为目标系统需要的格式
	Transform(ctx context.Context, syncCtx *SyncContext) (T, error)
	// Sync 同步数据到目标系统
	Sync(ctx context.Context, data T) error
}

// anySyncer 类型擦除包装器
type anySyncer interface {
	transformAndSync(ctx context.Context, syncCtx *SyncContext) error
}

// syncerWrapper 包装具体的同步器实现
type syncerWrapper[T any] struct {
	syncer ExpSyncAdapter[T]
}

func (w *syncerWrapper[T]) transformAndSync(ctx context.Context, syncCtx *SyncContext) error {
	data, err := w.syncer.Transform(ctx, syncCtx)
	if err != nil {
		return err
	}
	return w.syncer.Sync(ctx, data)
}

// @autowire(set=service)
func NewExpSyncService(ds *dao.Query, expService *ExpService, groupService *GroupService, EventService *Events, config *config.Config) *ExpSyncService {
	service := &ExpSyncService{
		DS:           ds,
		ExpService:   expService,
		GroupService: groupService,
		Events:       EventService,
		syncers:      make([]anySyncer, 0),
	}

	for _, syncer := range config.ExpSync {
		service.RegisterSyncer(&syncerWrapper[*RemoteExpBI]{
			syncer: &BISyncAdapter{
				ApiURL:  syncer.ApiURL,
				HttpCli: &http.Client{Timeout: 5 * time.Second},
			},
		})
	}

	return service
}

// RegisterSyncer 注册同步器 - 直接接收 anySyncer 接口
func (s *ExpSyncService) RegisterSyncer(syncer anySyncer) {
	s.syncers = append(s.syncers, syncer)
}

// SyncFullExps 全量同步实验配置到所有系统
func (s *ExpSyncService) SyncFullExps(ctx context.Context, skipBeforeDay int) error {
	beginAt := time.Now()
	defer func() {
		slog.InfoContext(ctx, "sync_exp_finished", "duration", time.Since(beginAt).String())
	}()

	offset := 0
	limit := 100
	expDao := s.DS.Exp

	// 指定14天的判断时间
	skipBeforeTime := time.Now().AddDate(0, 0, -skipBeforeDay)

	expList := make([]*model.Exp, 0)

	for {
		// 1. 提取数据
		list, err := expDao.Where(
			expDao.State.Neq(model.ExpStateDraft),
			expDao.State.Neq(model.ExpStateTodo),
			expDao.IsDeleted.Eq(model.NotDeleted),
			expDao.ExpType.In(model.ExpTypeNew, model.ExpTypeActive),
		).Order(expDao.ID.Desc()).
			Offset(offset).Limit(limit).
			Find()

		if err != nil {
			return err
		}
		if len(list) == 0 {
			break
		}
		offset += limit

		expList = append(expList, list...)
	}

	sort.Slice(expList, func(i, j int) bool {
		return expList[i].ID < expList[j].ID
		// return expList[i].StartTime.Unix() < expList[j].StartTime.Unix()
	})

	// 2. 同步每个实验到所有系统
	for _, exp := range expList {
		// 分别判断状态和时间
		if model.IsClosed(model.ExpState(exp.State)) && exp.UpdateTime.Before(skipBeforeTime) {
			continue
		}

		if err := s.SyncExp(ctx, exp.ID); err != nil {
			slog.ErrorContext(ctx, "sync_exp_failed", "exp_id", exp.ID, "err", err.Error())
		}
	}

	return nil
}

// SyncExp 同步单个实验到所有系统
func (s *ExpSyncService) SyncExp(ctx context.Context, expId int64) error {

	if ctxs.TraceId(ctx) == "" {
		ctx = ctxs.Set(ctx, ctxs.KeyTraceID, logs.GenerateTraceID())
	}

	// 1. 统一获取所需的所有数据
	exp, err := s.ExpService.Detail(expId)
	if err != nil {
		return err
	}

	if exp.StartTime == nil {
		return errors.New("exp_sync_nil_starttime")
	}

	prjDao := s.DS.Project
	project, err := prjDao.Where(prjDao.ID.Eq(exp.ProjectID)).First()
	if err != nil {
		return err
	}

	if project.Data.BundleID == "" {
		return errors.New("exp_sync_nil_bundleid")
	}

	groups, err := s.GroupService.QueryGroupByExpId(ctx, expId)
	if err != nil {
		return err
	}
	tagStrIds := strings.Split(exp.TagID, ",")
	tagIds := lo.Map(tagStrIds, func(s string, _ int) int64 {
		return cast.ToInt64(s)
	})
	var tags []*model.Tag
	if len(tagIds) > 0 {
		tag, err := s.DS.Tag.Where(s.DS.Tag.ID.In(tagIds...)).Find()
		if err != nil {
			return err
		}
		tags = append(tags, tag...)
	}

	// 2. 构建统一的同步上下文
	syncCtx := &SyncContext{
		Exp:     exp,
		Tags:    tags,
		Groups:  groups,
		Project: project,
	}

	// 3. 同步到每个系统
	for _, syncer := range s.syncers {
		if err := syncer.transformAndSync(ctx, syncCtx); err != nil {
			slog.ErrorContext(ctx, "transformAndSync", "err", err.Error(), "exp_id", expId)
			return err
		}
	}

	return nil
}

var _ ExpSyncAdapter[*RemoteExpBI] = (*BISyncAdapter)(nil)

// BISyncAdapter BI系统的同步实现
type BISyncAdapter struct {
	ApiURL  string
	HttpCli *http.Client
}

// RemoteExpBI BI系统需要的实验配置格式
type RemoteExpBI struct {
	ProjectId  int64            `json:"project_id"`
	BundleID   string           `json:"bundle_id"`
	Bucketv2ID int              `json:"bucketv2_id"`
	IsFentong  int              `json:"is_fentong"`
	ExpID      int64            `json:"exp_id"`
	ExpName    string           `json:"exp_name"`
	State      int32            `json:"state"`
	UserType   int32            `json:"user_type"` // 用户类型(新增/活跃)
	GroupData  []*RemoteGroupBI `json:"data"`
}

type RemoteGroupBI struct {
	GroupID     int64  `json:"group_id"`
	Plan        string `json:"plan"`
	Desc        string `json:"desc"`
	Type        int32  `json:"type"`
	PiciID      int64  `json:"pici_id"`
	PiciName    string `json:"pici_name"`
	State       int32  `json:"state"`
	IngTime     int64  `json:"ing_time"`     // 进量时间
	FreezeTime  int64  `json:"freeze_time"`  // 停量时间
	ReleaseTime int64  `json:"release_time"` // 释放时间
}

func (s *BISyncAdapter) handleTag(tags []*model.Tag) int {
	if len(tags) == 0 || tags == nil {
		return 0
	}
	var isFentong int
	if len(tags) >= 1 {
		pattern := regexp.MustCompile(`^BI_.+_\d+$`)
		if pattern.MatchString(tags[0].Name) {
			strArr := strings.Split(tags[0].Name, "_")
			if len(strArr) >= 2 {
				index := len(strArr) - 1
				isFentong = cast.ToInt(strArr[index])
			}
		}
	}
	return isFentong
}
func (s *BISyncAdapter) Transform(ctx context.Context, syncCtx *SyncContext) (*RemoteExpBI, error) {
	exp := syncCtx.Exp
	groups := syncCtx.Groups
	isFentong := s.handleTag(syncCtx.Tags)
	validTime := time.Unix(1046656000, 0)

	biGroups := make([]*RemoteGroupBI, 0, len(groups))
	for _, g := range groups {

		freezeAt := int64(0)
		if model.IsFreezed(exp.State) || model.IsFreezed(g.State) {
			freezeAt = max(freezeAt, exp.Data.FreezedAt.Unix(), g.Data.FreezedAt.Unix())
		}

		closeAt := int64(0)
		if model.IsClosed(g.State) && g.Data.ClosedAt.After(validTime) { // 优先取方案关闭时间，因为实验关闭后方案也会关闭
			closeAt = g.Data.ClosedAt.Unix()
		} else if model.IsClosed(exp.State) && exp.Data.ClosedAt.After(validTime) {
			closeAt = exp.Data.ClosedAt.Unix()
		}
		if closeAt < 0 {
			closeAt = 0
		}

		biGroups = append(biGroups, &RemoteGroupBI{
			GroupID:     g.ID,
			Plan:        g.Name,
			Desc:        g.Desc,
			Type:        g.GroupType,
			PiciID:      g.BatchID, // 直接使用原group的BatchID
			PiciName:    g.Data.BatchName,
			State:       g.State,
			IngTime:     exp.StartTime.Unix(), // 从实验状态历史获取进量时间
			FreezeTime:  freezeAt,             // 从实验状态历史获取停量时间
			ReleaseTime: closeAt,              // 从实验状态历史获取释放时间
		})
	}

	return &RemoteExpBI{
		ProjectId:  syncCtx.Project.ID,
		BundleID:   syncCtx.Project.Data.BundleID,
		Bucketv2ID: syncCtx.Project.Data.Bucketv2ID,
		IsFentong:  isFentong,
		ExpID:      exp.ID,
		ExpName:    exp.Name,
		State:      exp.State,
		UserType:   exp.ExpType,
		GroupData:  biGroups,
	}, nil
}

func (s *BISyncAdapter) Sync(ctx context.Context, data *RemoteExpBI) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("marshal data failed: %w", err)
	}

	slog.InfoContext(ctx, "http_by_sync", "uri", s.ApiURL, "req", string(jsonData))
	req, err := http.NewRequestWithContext(ctx, "POST", s.ApiURL, bytes.NewReader(jsonData))
	if err != nil {
		return fmt.Errorf("create request failed: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set(ctxs.KeyTraceID, ctxs.TraceId(ctx))

	resp, err := s.HttpCli.Do(req)
	if err != nil {
		return fmt.Errorf("do request failed: %w", err)
	}
	defer resp.Body.Close()
	body, _ := io.ReadAll(resp.Body)

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("sync failed with status %d", resp.StatusCode)
	}

	respm := map[string]any{}
	err = codec.Default.Unmarshal(body, &respm)
	if err != nil {
		return fmt.Errorf("unmarshal response failed: %w", err)
	}

	slog.InfoContext(ctx, "http_by_sync", "resp", respm)
	if cast.ToInt(respm["errno"]) != 200 {
		return fmt.Errorf("sync failed with response %s", respm["msg"])
	}

	return nil
}
