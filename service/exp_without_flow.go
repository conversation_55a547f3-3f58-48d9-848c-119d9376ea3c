package service

import (
	"context"
	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/repository/dao"
)

// WithoutFlowStrategy 停止进量，运行中不进量，灰度运行中不进量
type WithoutFlowStrategy struct {
	Status       model.ExpStatus
	ApprovalNode string
}

// WithoutFlow 运行中->运行中不进量或者灰度运行中->灰度运行中不进量
func (s *ExpService) WithoutFlow(ctx context.Context, exp *model.Exp) error {
	return s.ExpStateService.ToggleExpStatus(ctx, exp, &WithoutFlowStrategy{ApprovalNode: model.ExpStop})
}

// TryTransStatus 运行中/灰度运行中
func (o *WithoutFlowStrategy) TryTransStatus(status model.ExpStatus) bool {
	switch status {
	case model.ExpStatusRunning:
		o.Status = model.ExpStatusRunningWithoutFlow
		return true
	case model.ExpStatusGrayRunning:
		o.Status = model.ExpStatusGrayRunningWithoutFlow
		return true
	default:
		return false
	}
}

// GetStatusAfterTrans 获取状态，灰度运行中不进量/运行中不进量
func (o *WithoutFlowStrategy) GetStatusAfterTrans() (status model.ExpStatus) {
	return o.Status
}

// GetOperate 停止进量
func (o *WithoutFlowStrategy) GetOperate() ExpOpt {
	return expWithoutFlowOpt
}
func (o *WithoutFlowStrategy) GetApprovalNode() string {
	return o.ApprovalNode
}

// CreateOrUpdateTask 灰度运行中-->灰度运行中不进量,运行中->运行中不进量
func (o *WithoutFlowStrategy) CreateOrUpdateTask(tx *dao.Query, newExp *model.Exp) error {
	return UpdateTask(tx, newExp, model.TaskStatusPending)
}

func (o *WithoutFlowStrategy) PrepareGroupData(groups []*model.Group) error {
	err := GroupRunningWithoutFlow(groups)
	if err != nil {
		return err
	}
	return nil
}

// UpdateGroupStatus 实验组停止进量
func (o *WithoutFlowStrategy) UpdateGroupStatus(tx *dao.Query, groups []*model.Group) error {
	err := UpdateGroup(tx, groups)
	if err != nil {
		return err
	}
	return nil
}
