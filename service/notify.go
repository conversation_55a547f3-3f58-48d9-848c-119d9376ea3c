package service

import (
	"context"
	"encoding/json"
	"strings"

	"git.7k7k.com/data/abAdmin/infra/config"
	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/model/dto"
	"github.com/blinkbean/dingtalk"
)

// @autowire(set=service)
type NotifyService struct {
	ExpService     *ExpService
	Config         *config.Config
	ProjectService *ProjectService
}

type Line = model.Line

func (s *NotifyService) NotifyExpChange(ctx context.Context, log *model.ExpLog) error {
	if len(log.Notify.Lines) == 0 {
		return nil
	}

	snapshot := &dto.Snapshot{}
	err := json.Unmarshal([]byte(*log.ExpSnapshot), snapshot)
	if err != nil {
		return err
	}

	exp, err := s.ExpService.Detail(log.ExpID) // 尽量先用 snapshot，除非字段缺失
	if err != nil {
		return err
	}

	prj, err := s.ProjectService.Detail(ctx, exp.ProjectID)
	if err != nil {
		return err
	}

	if !prj.Data.CanSendDingding() {
		return nil
	}

	title := snapshot.ExpInfo.Name + "变更提醒" // 显示在对话列表里
	md := &dto.Markdown{}
	md.AddLinef("## 实验变更：%s", snapshot.ExpInfo.Name)
	md.AddLine("---")
	md.AddLinef("项目：%s", prj.Name)
	for _, line := range log.Notify.Lines {
		md.AddLine(line.Text)
	}
	md.AddLine("<br/>操作人：" + log.Uname)

	btns := []dingtalk.ActionCardMultiBtnModel{
		{
			Title:     "实验详情",
			ActionURL: md.URL(exp.AbUrl(s.Config.ABURL)),
		},
	}
	if url := exp.BIURL(s.Config.BIURL, prj.Data.BundleID); url != "" {
		btns = append(btns, dingtalk.ActionCardMultiBtnModel{
			Title:     "实验数据",
			ActionURL: md.URL(url),
		})
	}

	keyword := "."
	tokens := prj.Data.GetDingdingTokens()
	for _, token := range tokens {
		tokenList := strings.Split(token, ",")
		dt := dingtalk.InitDingTalk(tokenList, keyword)
		err := dt.SendActionCardMessageBySlice(title, md.Lines, dingtalk.WithCardBtnVertical(), dingtalk.WithCardBtns(btns))
		if err != nil {
			return err
		}
	}
	return nil
}
