package service

import (
	"context"
	"fmt"
	"git.7k7k.com/data/abAdmin/httpd/base"
	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/repository/dao"
)

// 重启实验策略实现, 分两种情况，运行中不进量的重启和灰度运行中不进量的重启

// RestartStrategy  运行中不进量和灰度运行中不进量
type RestartStrategy struct {
	Status       model.ExpStatus
	ApprovalNode string
}

// Restart 重启，有两种情况，运行中不进量->运行中, 灰度运行中不进量->灰度运行
func (s *ExpService) Restart(ctx context.Context, exp *model.Exp) error {
	err := s.LayerService.AvailableRate(exp.LayerID, exp.ID, exp.LayerRate)
	if err != nil {
		return base.NewAlertTip(fmt.Sprintf("重启失败，%s", err.Error()))
	}
	return s.ExpStateService.ToggleExpStatus(ctx, exp, &RestartStrategy{ApprovalNode: model.ExpRestart})
}

// TryTransStatus 灰度不进量和运行中不进量
func (o *RestartStrategy) TryTransStatus(status model.ExpStatus) bool {
	switch status {
	case model.ExpStatusRunningWithoutFlow:
		o.Status = model.ExpStatusRunning
		return true
	case model.ExpStatusGrayRunningWithoutFlow:
		o.Status = model.ExpStatusGrayRunning
		return true
	default:
		return false
	}
}

// GetStatusAfterTrans 获取状态，运行中/灰度运行中
func (o *RestartStrategy) GetStatusAfterTrans() (status model.ExpStatus) {
	return o.Status
}

// GetOperate 重启实验
func (o *RestartStrategy) GetOperate() ExpOpt {
	return expRestartOpt
}
func (o *RestartStrategy) GetApprovalNode() string {
	return o.ApprovalNode
}

// CreateOrUpdateTask 将运行中不进量修改为运行中，灰度运行中不进量修改为灰度运行中
func (o *RestartStrategy) CreateOrUpdateTask(tx *dao.Query, newExp *model.Exp) error {
	return UpdateTask(tx, newExp, model.TaskStatusPending)
}
func (o *RestartStrategy) PrepareGroupData(groups []*model.Group) error {
	err := RestartGroup(groups)
	if err != nil {
		return err
	}
	return nil
}

// UpdateGroupStatus 重启实验组
func (o *RestartStrategy) UpdateGroupStatus(tx *dao.Query, groups []*model.Group) error {
	err := UpdateGroup(tx, groups)
	if err != nil {
		return err
	}
	return nil
}
