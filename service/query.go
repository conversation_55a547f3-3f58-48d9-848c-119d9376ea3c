package service

import (
	"git.7k7k.com/data/abAdmin/httpd/request"
	"gorm.io/gen"
)

// Query 查询服务
// @autowire(set=service)
type Query struct {
}

// CalcOffsetLimit 计算分页的偏移量和限制数量，校验输入参数非负
func (q *Query) CalcOffsetLimit(page int, size int) (offset int, limit int) {
	if page < 1 {
		page = 1
	}
	if size < 1 {
		size = request.DefaultSize
	}
	return (page - 1) * size, size
}
func Paginate(page, pageSize int) func(db gen.Dao) gen.Dao {
	return func(db gen.Dao) gen.Dao {
		offset := (page - 1) * pageSize
		return db.Offset(offset).Limit(pageSize)
	}
}
