package service

import (
	"context"
	"fmt"
	"git.7k7k.com/data/abAdmin/httpd/response"
	"github.com/samber/lo"
	"log/slog"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.7k7k.com/data/abAdmin/infra/auth"
	"git.7k7k.com/data/abAdmin/infra/config"

	"git.7k7k.com/data/abAdmin/httpd/base"
	"git.7k7k.com/data/abAdmin/infra/redises"
	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/repository/dao"
	"github.com/cockroachdb/errors"
	"gorm.io/gorm"
)

const maxPollSize = 10

const (
	// 获取数据的锁，减少重复执行
	lockQueryKeyFormat   = "task_lock_query_%s_%d"
	lockQueryValueFormat = "task_lock_query_val:%s_%d"
	defaultLockQueryTTL  = time.Minute * 10

	// 任务执行的锁，避免多个节点之间重复执行
	lockKeyFormat   = "task_lock_%d"
	lockValueFormat = "task_lock_exp_id:%d_%d"
	defaultLockTTL  = time.Second * 90
)

const (
	// 任务类别
	taskType = "taskType"
	// 自动关闭实验
	taskCloseExpAuto = "closeExpAuto"
	// 实验策略触发
	taskStrategyTrigger = "expStrategyTrigger"
)

// 查询函数
type queryFunc func(offset, limit int) ([]any, error)

// 任务执行函数
type taskFunc func(ctx context.Context, data any) error

// @autowire(set=service)
type TaskService struct {
	DS *dao.Query
	Query
	Rdb             *redises.Clients
	MetricService   *MetricService
	ExpStateService *ExpStateService
	ExpService      *ExpService
	GroupService    *GroupService
}

// Execute 执行task
func (s *TaskService) Execute(ctx context.Context) {
	ctx = context.WithValue(ctx, auth.ContextKeyUserID, config.GlobalConfig.Task.AccountId)
	var wg sync.WaitGroup

	if config.GlobalConfig.Task.CloseExpEnabled {
		wg.Add(1)
		go func() {
			defer wg.Done()
			defer func() {
				if r := recover(); r != nil {
					slog.Error(fmt.Sprintf("执行关闭实验任务发生panic: %v", r))
				}
			}()
			// 执行关闭实验任务
			ctx = context.WithValue(ctx, taskType, taskCloseExpAuto)
			interval := time.Duration(config.GlobalConfig.Task.CloseExpIntervalSecond) * time.Second
			s.executor(ctx, interval, s.QueryRunningTask, s.closeExp)
		}()
	}

	if config.GlobalConfig.Task.ExpStrategyTriggerEnabled {
		wg.Add(1)
		go func() {
			defer wg.Done()
			defer func() {
				if r := recover(); r != nil {
					slog.Error(fmt.Sprintf("执行实验策略任务发生panic: %v", r))
				}
			}()
			// 执行实验策略任务
			ctx = context.WithValue(ctx, taskType, taskStrategyTrigger)
			interval := time.Duration(config.GlobalConfig.Task.ExpStrategyTriggerIntervalSecond) * time.Second
			s.executor(ctx, interval, s.ExpService.QueryRunningExp, s.triggerFilter)
		}()
	}

	wg.Wait()
}

// executor 抽象出的任务执行器，用于执行task和异常处理，task的间隔调用时间为interval
func (s *TaskService) executor(ctx context.Context, interval time.Duration, queryFn queryFunc, taskFn taskFunc) {
	// 启动时先运行一次，避免interval时间过长，任务长时间得不到执行
	if err := s.executeTemplate(ctx, queryFn, taskFn); err != nil {
		slog.Error(fmt.Sprintf("%s executeTemplate 出错: %s", ctx.Value(taskType), err.Error()))
	}

	var wg sync.WaitGroup
	var taskRunning bool
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// 如果任务在执行，则等待下一次运行
			if !taskRunning {
				taskRunning = true
				wg.Add(1)
				go func() {
					defer func() {
						if r := recover(); r != nil {
							slog.Error(fmt.Sprintf("%s实验任务协程执行出现异常: %v", ctx.Value(taskType), r))
						}
						wg.Done()
						taskRunning = false
					}()
					if err := s.executeTemplate(ctx, queryFn, taskFn); err != nil {
						slog.Error(fmt.Sprintf("%s executeTemplate 出错: %s", ctx.Value(taskType), err.Error()))
					}
				}()
			}
		case <-ctx.Done():
			// 等待所有的任务执行完毕再退出
			wg.Wait()
			return
		}
	}
}

// executeTemplate 抽象的执行任务模板，分页获取数据和执行
func (s *TaskService) executeTemplate(ctx context.Context, queryFn queryFunc, taskFn taskFunc) error {
	// 分页获取
	pageNo := 1
	for {
		list, locked, err := s.queryWithLock(ctx, queryFn, pageNo)
		if err != nil {
			return fmt.Errorf("%s queryFn error, %s", ctx.Value(taskType), err.Error())
		}
		if locked {
			if len(list) == 0 {
				slog.Debug(fmt.Sprintf("%s queryFn has no data at page: %d", ctx.Value(taskType), pageNo))
				return nil
			}

			if err = executeTask(ctx, list, taskFn); err != nil {
				return err
			}
			if len(list) < maxPollSize {
				// 没有更多的数据了
				slog.Debug(fmt.Sprintf("%s no more data need to execute task", ctx.Value(taskType)))
				return nil
			}
		}
		pageNo++
	}
}

// 抽象worker pool模式执行任务逻辑，传入具体的任务处理逻辑函数
func executeTask(ctx context.Context, list []any, taskFn taskFunc) error {
	size := maxPollSize
	if size > len(list) {
		size = len(list)
	}
	taskChan := make(chan any, len(list))

	var wg sync.WaitGroup
	wg.Add(size)
	// 启动worker pool中的协程
	for i := 0; i < size; i++ {
		go func() {
			defer wg.Done()
			for {
				select {
				case <-ctx.Done():
					slog.Info(fmt.Sprintf("%s executeTask done,err: %s", ctx.Value(taskType), ctx.Err()))
					return

				case task, ok := <-taskChan:
					if !ok {
						// 通道已关闭，说明所有任务已分配完，退出循环
						return
					}
					err := taskFn(ctx, task)
					if err != nil {
						slog.Error(fmt.Sprintf("%s taskFn error: %s", ctx.Value(taskType), err.Error()))
					}
				}
			}
		}()
	}

	// 将任务放入通道，供worker pool中的协程执行
	for _, task := range list {
		taskChan <- task
	}

	select {
	case <-ctx.Done():
		return nil
	default:
		close(taskChan)
		wg.Wait()
		return nil
	}
}

// queryWithLock 查询数据
func (s *TaskService) queryWithLock(ctx context.Context, queryFn queryFunc, pageNo int) (list []any, locked bool, err error) {
	// 分布式锁
	lockKey := fmt.Sprintf(lockQueryKeyFormat, ctx.Value(taskType), pageNo)
	lockVal := fmt.Sprintf(lockQueryValueFormat, ctx.Value(taskType), time.Now().UnixNano())
	locked, err = s.Rdb.TryLock(lockKey, lockVal, defaultLockQueryTTL)
	if err != nil {
		return
	}
	if locked {
		defer func() {
			ok, err := s.Rdb.Unlock(lockKey, lockVal)
			if err != nil {
				slog.Error(fmt.Sprintf("failed to unlock, key: %s, err: %s", lockKey, err.Error()))
			} else if !ok {
				slog.Error(fmt.Sprintf("failed to unlock, key: %s, value: %s", lockKey, lockVal))
			}
		}()
		offset, limit := s.CalcOffsetLimit(pageNo, maxPollSize)
		list, err = queryFn(offset, limit)
	}
	return
}

// closeExp 关闭实验
func (s *TaskService) closeExp(ctx context.Context, data any) error {
	task := data.(*model.ExpTask)
	exp, err := s.ExpService.Detail(task.ExpID)
	if err != nil {
		return err
	}
	return s.changeExpStatus(ctx, exp, task, &ClosedStrategy{}, true)
}

// triggerFilter 触发规则，对实验关闭/停止进量
func (s *TaskService) triggerFilter(ctx context.Context, data any) (err error) {
	exp := data.(*model.Exp)
	// 没有配置规则，则直接返回
	if exp.Strategy.TriggerStopKey == nil && exp.Strategy.TriggerEndKey == nil {
		slog.Debug(fmt.Sprintf("exp id: %d has no strategy", exp.ID))
		return nil
	}

	// 实验进量人数
	expCountMap, err := s.MetricService.GetExpUserCount(ctx, []int64{exp.ID})
	if err != nil {
		if !errors.Is(err, context.Canceled) {
			return fmt.Errorf("GetExpUserCount error, %s", err.Error())
		}
		slog.Info(fmt.Sprintf("exp id: %d GetExpUserCount err, %s", exp.ID, err.Error()))
		return nil
	}
	groupCountMap, _, err := s.GroupService.QueryGroup(ctx, exp.ID)
	if err != nil {
		if !errors.Is(err, context.Canceled) {
			return fmt.Errorf("GetGroupUserCount error, %s", err.Error())
		}
		slog.Info(fmt.Sprintf("exp id: %d GetGroupUserCount err, %s", exp.ID, err.Error()))
		return nil
	}
	groupCount := lo.Map(groupCountMap, func(item *response.Group, index int) int64 {
		return item.UserCount
	})
	maxGroupCount := lo.Max(groupCount)
	value := map[string]any{
		"test_flow":       expCountMap[exp.ID],
		"group_test_flow": maxGroupCount,
		"now_ts":          time.Now().Unix() * 1000, // 目前存的值都是 ms 单位
	}

	expTask, err := s.DS.ExpTask.Where(s.DS.ExpTask.ExpID.Eq(exp.ID)).First()
	if err != nil {
		slog.Error(fmt.Sprintf("failed to query task by exp id: %d,err: %s", exp.ID, err.Error()))
		return err
	}

	// 优先处理关闭
	if exp.Strategy.TriggerEndKey != nil {
		if exp.State == int32(model.ExpStatusClosed) {
			// 不应该存在这种情况
			slog.Error(fmt.Sprintf("exp id: %d has closed", exp.ID))
			return nil
		}
		ok := exp.Strategy.TriggerEndKey.EvaluateFilter(value)
		if ok {
			return s.changeExpStatus(ctx, exp, expTask, &ClosedStrategy{}, false)
		}
	}

	// 再处理停止进量
	if exp.Strategy.TriggerStopKey != nil {
		// 如果实验已经停止进量，则不做任何的处理
		if exp.State == int32(model.ExpStatusRunningWithoutFlow) ||
			exp.State == int32(model.ExpStatusGrayRunningWithoutFlow) {
			slog.Debug(fmt.Sprintf("%d exp has been without flow, the current is: %s", exp.ID, model.ExpStatus(exp.State).Name()))
			return nil
		}
		ok := exp.Strategy.TriggerStopKey.EvaluateFilter(value)
		if ok {
			return s.changeExpStatus(ctx, exp, expTask, &WithoutFlowStrategy{}, false)
		}
	}
	return nil
}

// changeExpStatus 实验状态变更, 自动关闭需要等待wait为true
func (s *TaskService) changeExpStatus(ctx context.Context, exp *model.Exp, task *model.ExpTask, strategy ExpStateChangeStrategy, wait bool) error {
	// 分布式锁
	lockKey := fmt.Sprintf(lockKeyFormat, task.ID)
	lockVal := fmt.Sprintf(lockValueFormat, task.ID, time.Now().UnixNano())
	locked, err := s.Rdb.TryLock(lockKey, lockVal, defaultLockTTL)
	if err != nil {
		return err
	}
	defer func() {
		ok, err := s.Rdb.Unlock(lockKey, lockVal)
		if err != nil {
			slog.Error(fmt.Sprintf("failed to unlock,key: %s, err: %s", lockKey, err.Error()))
		} else if !ok {
			slog.Error(fmt.Sprintf("failed to unlock,key: %s, value: %s", lockKey, lockVal))
		}
	}()
	if !locked {
		slog.Debug(fmt.Sprintf("failed to lock, key: %s, value: %s", lockKey, lockVal))
		return nil
	}

	if wait {
		now := time.Now()
		diffTime := task.PlannedExecutionTime.Sub(now)
		if diffTime > 0 {
			// 更新为执行中，减少不必要的取数
			updateTask := &model.ExpTask{
				ID:         task.ID,
				UpdateTime: task.UpdateTime,
				Desc:       task.Desc,
				Status:     int32(model.TaskStatusRunning),
			}
			if err = doTaskUpdate(s.DS, updateTask, fmt.Sprintf("等待执行%s", strategy.GetOperate())); err != nil {
				return err
			}

			select {
			case <-ctx.Done():
				updateTask = &model.ExpTask{
					ID:         task.ID,
					UpdateTime: task.UpdateTime,
					Desc:       task.Desc,
					Status:     int32(model.TaskStatusPending),
				}
				return doTaskUpdate(s.DS, updateTask, "恢复")
			case <-time.After(diffTime):
				// 等时间到达后再执行
			}
		}
	}

	err = s.ExpStateService.ToggleExpStatus(ctx, exp, strategy)
	if err != nil && !errors.Is(err, context.Canceled) {
		slog.Error(fmt.Sprintf("failed to ToggleExpStatus, exp id: %d, err: %s", task.ExpID, err.Error()))
		failedTask := &model.ExpTask{
			ID:         task.ID,
			UpdateTime: task.UpdateTime,
			Desc:       task.Desc,
			Status:     int32(model.TaskStatusFailed),
		}
		return doTaskUpdate(s.DS, failedTask, fmt.Sprintf("%s失败%s", strategy.GetOperate(), err.Error()))
	}
	return err
}

// CreateTask 通用的创建任务函数
func CreateTask(tx *dao.Query, exp *model.Exp) error {
	task, err := tx.ExpTask.Where(tx.ExpTask.ExpID.Eq(exp.ID)).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return base.NewBizError(err)
	}

	if task != nil {
		return base.NewBizError(fmt.Errorf("实验任务已存在，状态为[%s]，不允许此操作", model.TaskStatus(task.Status).Name()))
	}

	now := time.Now()
	task = &model.ExpTask{
		Name:                 exp.Name,
		ExpID:                exp.ID,
		ExpStatus:            exp.State,
		PlannedExecutionTime: now.AddDate(0, 0, int(exp.Days)),
		CreateTime:           now,
		UpdateTime:           now,
		Status:               int32(model.TaskStatusPending),
		Desc:                 model.ExpStatus(exp.State).Name(),
	}

	return tx.ExpTask.Create(task)
}

// doTaskUpdate 对实验任务部分字段的更新，请将原task的UpdateTime和desc赋值给更新的task，desc为本次更新的描述（会追加）
func doTaskUpdate(query *dao.Query, task *model.ExpTask, desc string) error {
	// 对日志进行压缩，避免定时任务一直报错，desc超出其长度
	task.Desc = compressLogs(task.UpdateTime, task.Desc, desc, 2000)
	task.UpdateTime = time.Now()

	taskDao := query.ExpTask
	result, err := taskDao.Debug().Where(taskDao.ID.Eq(task.ID)).Updates(task)
	if err != nil {
		return base.NewBizError(fmt.Errorf("更新实验任务失败，实验ID: %d,err: %s", task.ID, err.Error()))
	}
	if result.RowsAffected != 1 {
		return base.NewBizError(fmt.Errorf("更新实验任务失败，实验ID：%d", task.ID))
	}
	return nil
}

// UpdateTask 实验状态变更
func UpdateTask(tx *dao.Query, newExp *model.Exp, taskStatus model.TaskStatus) error {
	expInfo, err := tx.Exp.Where(tx.Exp.ID.Eq(newExp.ID)).Take()
	if err != nil {
		return base.NewBizError(err)
	}
	// 待启动状态，直接关闭
	if expInfo.State == int32(model.ExpStatusPending) {
		return nil
	}
	task, err := tx.ExpTask.Where(tx.ExpTask.ExpID.Eq(newExp.ID)).First()
	if err != nil {
		return base.NewBizError(err)
	}

	if task == nil {
		return base.NewBizError(fmt.Errorf("实验%d，不存在任务记录", newExp.ID))
	}

	now := time.Now()
	updateTask := &model.ExpTask{
		ExpStatus:  newExp.State,
		UpdateTime: now,
		Desc: fmt.Sprintf("%s%s,%s", task.Desc, task.UpdateTime.Format(time.DateTime),
			model.ExpStatus(newExp.State).Name()),
		Status: int32(taskStatus),
	}

	result, err := tx.ExpTask.Where(tx.ExpTask.ID.Eq(task.ID)).Updates(updateTask)
	if err != nil {
		return base.NewBizError(fmt.Errorf("更新实验状态失败，exp id: %d,err: %s", newExp.ID, err.Error()))
	}
	if result.RowsAffected != 1 {
		return base.NewBizError(fmt.Errorf("更新实验状态失败，exp id: %d", newExp.ID))
	}
	return nil
}

// UpdateTaskOnExpDayChange 实验天数变更
func UpdateTaskOnExpDayChange(query *dao.Query, expId int64, diffDay int) error {
	task, err := query.ExpTask.Where(query.ExpTask.ExpID.Eq(expId)).First()
	if err != nil {
		return base.NewBizError(err)
	}

	if task == nil {
		return base.NewBizError(fmt.Errorf("实验%d，不存在任务记录", expId))
	}

	if task.Status == int32(model.TaskStatusFinish) {
		return base.NewAlertTip("实验已结束，不允许操作")
	}

	now := time.Now()
	newPlannedExecutionTime := task.PlannedExecutionTime.AddDate(0, 0, diffDay)
	if newPlannedExecutionTime.Before(now) {
		return base.NewAlertTip("实验时长不可以小于已运行时间")
	}

	updateTask := &model.ExpTask{
		PlannedExecutionTime: newPlannedExecutionTime,
		UpdateTime:           task.UpdateTime,
		Desc:                 task.Desc,
		ID:                   task.ID,
		Status:               int32(model.TaskStatusPending),
	}
	return doTaskUpdate(query, updateTask, fmt.Sprintf("增加%d天", diffDay))
}

// QueryRunningTask 查询运行中的实验任务
func (s *TaskService) QueryRunningTask(offset, limit int) ([]any, error) {
	taskDao := s.DS.ExpTask
	now := time.Now()
	startTime := now.AddDate(0, 0, -int(config.GlobalConfig.Task.CloseExpDaysAgo))
	endTime := now.Add(time.Minute)
	list, err := taskDao.Where(taskDao.PlannedExecutionTime.Between(startTime, endTime),
		taskDao.Status.Eq(int32(model.TaskStatusPending))).
		Order(taskDao.PlannedExecutionTime.Asc(), taskDao.CreateTime.Desc()).
		Offset(offset).Limit(limit).Find()
	if err != nil {
		return nil, err
	}
	var data []any
	for _, v := range list {
		data = append(data, v)
	}
	return data, nil
}

// compressLogs 对日志进行压缩&剪切
func compressLogs(lastUpdateTime time.Time, taskDesc, desc string, maxLength int) string {
	result := compressLogsWithoutLimit(lastUpdateTime, taskDesc, desc)
	runeSlice := []rune(result)
	if len(runeSlice) <= maxLength {
		return result
	}

	// 如果超过最大长度，需要进行截取
	return "..." + result[len(runeSlice)-maxLength-3:]
}

// compressLogsWithoutLimit 对日志进行压缩
func compressLogsWithoutLimit(lastUpdateTime time.Time, taskDesc, desc string) string {
	// 如果desc为空，直接返回taskDesc
	if desc == "" {
		return taskDesc
	}

	// 如果taskDesc为空，返回desc
	if taskDesc == "" {
		return desc
	}

	// 如果taskDesc不包含时间（第一次添加时间）
	if !strings.Contains(taskDesc, ",") {
		return fmt.Sprintf("%s%s,%s",
			taskDesc,
			lastUpdateTime.Format(time.DateTime),
			desc)
	}

	parts := strings.Split(taskDesc, ",")
	if len(parts) != 2 {
		return fmt.Sprintf("%s,%s", taskDesc, desc)
	}

	baseWithTime := parts[0] // 第一部分（包含时间）
	currentDesc := parts[1]  // 当前描述

	// 如果新描述与当前描述相同
	if strings.HasPrefix(currentDesc, desc) {
		// 检查是否已有计数
		if strings.Contains(currentDesc, "(") {
			re := regexp.MustCompile(`^(.*?)\((\d+)\).*$`)
			matches := re.FindStringSubmatch(currentDesc)
			if len(matches) == 3 {
				count, _ := strconv.Atoi(matches[2])
				return fmt.Sprintf("%s,%s(%d)%s",
					baseWithTime,
					desc,
					count+1,
					lastUpdateTime.Format(time.DateTime))
			}
		}
		// 首次重复
		return fmt.Sprintf("%s,%s(2)%s",
			baseWithTime,
			desc,
			lastUpdateTime.Format(time.DateTime))
	}

	// 如果是新的描述
	return fmt.Sprintf("%s,%s", taskDesc, desc)
}
