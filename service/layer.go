package service

import (
	"context"
	"git.7k7k.com/data/abAdmin/infra/auth"
	"github.com/dromara/carbon/v2"
	"gorm.io/gen"
	"gorm.io/gorm"
	"time"

	"git.7k7k.com/data/abAdmin/httpd/base"
	"git.7k7k.com/data/abAdmin/httpd/response"
	"github.com/cockroachdb/errors"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"

	"git.7k7k.com/data/abAdmin/httpd/request"
	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/repository/dao"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// Query 查询服务
// @autowire(set=service)
type LayerService struct {
	DS *dao.Query
	Query
}

func (s *LayerService) Add(ctx context.Context, req request.AddLayerReq) (any, error) {
	layerDao := s.DS.Layer
	layer := &model.Layer{}
	layer.ProjectID = auth.GetProjectId64(ctx)
	take, err := layerDao.
		Where(layerDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Where(layerDao.State.Eq(model.StateOnline)).
		Where(layerDao.Name.Eq(req.Name)).
		Take()
	if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
		return nil, err
	}
	if take != nil {
		return nil, base.NewBizError(errors.New("流量层:" + req.Name + ",已经存在"))
	}
	if req.IsExclusion == 1 {
		exits, err := layerDao.
			Where(layerDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
			Where(layerDao.State.Eq(model.StateOnline)).
			Where(layerDao.IsExclusion.Eq(1)).
			Take()
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
		if exits != nil {
			return nil, base.NewBizError(errors.New("每个项目，互斥流量层,只能存在一个"))
		}
	}
	err = copier.Copy(layer, &req)
	if err != nil {
		return nil, err
	}
	layer.ProjectID = auth.GetProjectId64(ctx)
	err = layerDao.Create(layer)
	return layer, err
}

func (s *LayerService) List(ctx context.Context, req request.ListLayerReq) (any, error) {
	layer := s.DS.Layer
	id := cast.ToInt64(req.Search)
	where := make([]gen.Condition, 0)
	where = append(where, layer.State.Eq(req.State))
	where = append(where, layer.ProjectID.Eq(auth.GetProjectId64(ctx)))
	if req.Search != "" {
		subQuery := layer.Where(layer.Name.Like("%" + req.Search + "%")).
			Or(layer.ID.Like(id)).Or(layer.Desc.Like("%" + req.Search + "%"))
		where = append(where, subQuery)
	}
	list, err := layer.
		Order(layer.CreateTime.Desc()).
		Scopes(Paginate(req.Page, req.PageSize)).
		Where(where...).
		Find()
	total, err := layer.
		Where(where...).
		Count()
	res := make(map[string]any, 4)
	res["list"] = list
	res["total"] = total
	res["page"] = req.Page
	res["page_size"] = req.PageSize
	return res, err
}

func (s *LayerService) DownList(req request.ListLayerReq, ctx *gin.Context) (any, error) {
	layer := s.DS.Layer
	id := cast.ToInt64(req.Search)
	res := make(map[string]any, 4)
	var list []*model.Layer
	var err error
	where := make([]gen.Condition, 0)
	where = append(where, layer.State.Eq(req.State))
	where = append(where, layer.ProjectID.Eq(auth.GetProjectId64(ctx)))
	if req.Search != "" {
		subQuery := layer.Where(layer.Name.Like("%" + req.Search + "%")).
			Or(layer.ID.Like(id)).Or(layer.Desc.Like("%" + req.Search + "%"))
		where = append(where, subQuery)
	}
	list, err = layer.
		Order(layer.CreateTime.Desc()).
		Scopes(Paginate(req.Page, req.PageSize)).
		Where(where...).
		Find()
	if err != nil {
		return res, err
	}
	total, err := layer.
		Where(where...).
		Count()
	result := make([]response.LayerList, 0, len(list))
	for _, item := range list {
		var date string
		if item.DownTime != nil {
			date = carbon.CreateFromStdTime(item.UpdateTime).Format("Y-m-d H:i:s")

		}
		result = append(result, response.LayerList{
			Id:       item.ID,
			Name:     item.Name,
			Level:    item.Level,
			Desc:     item.Desc,
			DownTime: date,
			Rate:     item.Rate,
		})

	}
	res["list"] = list
	res["total"] = total
	res["page"] = req.Page
	res["page_size"] = req.PageSize
	return result, err
}

func (s *LayerService) Update(req request.UpdateLayerReq, ctx *gin.Context) (int64, error) {
	layerDao := s.DS.Layer
	layerInfo, err := layerDao.
		Where(layerDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Where(layerDao.ID.Eq(req.Id)).
		Take()
	if err != nil {
		return 0, err
	}
	if layerInfo.IsExclusion != req.IsExclusion {
		return 0, base.NewBizError(errors.New("互斥状态，不支持改动"))
	}
	if layerInfo.IsExclusion == model.LayerTypeNoEx && layerInfo.Level > model.LayerNoLevel && layerInfo.Level != req.Level {
		return 0, base.NewBizError(errors.New("分层等级，不支持修改"))
	}
	layerInfo.UpdateTime = time.Now()
	err = copier.Copy(layerInfo, &req)
	if err != nil {
		return 0, err
	}
	exits, err := layerDao.
		Where(layerDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Where(layerDao.Name.Eq(req.Name)).
		Where(layerDao.ID.Neq(req.Id)).
		Take()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return 0, err
	}
	if exits != nil {
		return 0, base.NewBizError(errors.New("流量层:" + req.Name + ",已经存在"))
	}
	info, err := layerDao.WithContext(ctx).
		Select(layerDao.UpdateTime, layerDao.Name, layerDao.Rate, layerDao.Level).
		Where(layerDao.ID.Eq(req.Id)).
		Updates(layerInfo)
	if err != nil {
		return 0, err
	}
	return info.RowsAffected, err
}
func (s *LayerService) Down(req request.DownLayerReq, ctx *gin.Context) (int64, error) {
	layerDao := s.DS.Layer
	expDao := s.DS.Exp
	count, err := expDao.Where(expDao.State.NotIn(model.ExpStateClose, model.ExpStateDraft)).Where(expDao.LayerID.Eq(req.Id)).Count()
	if err != nil {
		return 0, err
	}
	if count > 0 {
		return 0, base.NewBizError(errors.New("该流量层还有实验占用，请关闭实验后再尝试"))
	}
	layer := &model.Layer{}
	downTime := time.Now()
	layer.DownTime = &downTime
	layer.State = req.State
	info, err := s.DS.WithContext(ctx).Layer.Where(layerDao.ID.Eq(req.Id)).Updates(layer)
	return info.RowsAffected, err
}
func (s *LayerService) All(ctx *gin.Context) (any, error) {
	layerDao := s.DS.Layer
	layerPer := auth.CanName(ctx, "abtest.exp.create-level")
	wheres := make([]gen.Condition, 0, 10)
	if !layerPer {
		wheres = append(wheres, layerDao.Level.Eq(model.LayerLevelMedium))
	}
	wheres = append(wheres,
		layerDao.State.Eq(model.StateOnline),
		layerDao.ProjectID.Eq(auth.GetProjectId64(ctx)),
	)
	expDao := s.DS.Exp
	layerList, err := layerDao.WithContext(ctx).
		Where(wheres...).
		Select(layerDao.ID, layerDao.Name).
		Find()
	if err != nil {
		return nil, err
	}
	expList, err := expDao.WithContext(ctx).
		Where(expDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Select(expDao.ID, expDao.Name, expDao.LayerID, expDao.LayerRate).
		Where(expDao.State.In(model.ExpStateRunning, model.ExpStateGrayRunning, model.ExpStateTodo)).
		Where(expDao.IsDeleted.Eq(model.NotDeleted)).
		Find()
	groupExpList := lo.GroupBy(expList, func(item *model.Exp) int64 {
		return item.LayerID
	})
	list := make([]map[string]any, 0, len(layerList))
	for _, item := range layerList {
		expLayerList, ok := groupExpList[item.ID]
		if !ok {
			expLayerList = make([]*model.Exp, 0)
		}
		arr := make(map[string]any)
		arr["layer_id"] = item.ID
		arr["layer_name"] = item.Name
		arr["exp_layer_list"] = expLayerList
		list = append(list, arr)
	}
	return list, err
}

// QueryLayerByIds 通过ID获取流量层数据
func (s *LayerService) QueryLayerByIds(ids ...int64) ([]*model.Layer, error) {
	if len(ids) == 0 {
		return nil, base.NewAlertTip("流量层id必填")
	}
	layerDao := s.DS.Layer
	data, err := layerDao.Where(layerDao.ID.In(ids...)).Find()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return data, err
}

// QueryExpWithLayer 获取层其他实验流量
func (s *LayerService) QueryExpWithLayer(layerId, expId int64) ([]*response.ExpList, error) {
	if layerId == 0 {
		return nil, base.NewAlertTip("流量层ID是必填的")
	}
	if expId == 0 {
		return nil, base.NewAlertTip("实验ID是必填的")
	}
	expWithLayer := make([]*response.ExpList, 0)
	exp := s.DS.Exp
	err := exp.Select(exp.ID.As("exp_id"), exp.Name.As("exp_name"), exp.LayerRate).
		Where(exp.LayerID.Eq(layerId)).
		Where(exp.State.In(model.ExpStateRunning, model.ExpStateGrayRunning, model.ExpStateTodo)).
		Where(exp.IsDeleted.Eq(model.NotDeleted)).
		Where(exp.ID.Neq(expId)).Scan(&expWithLayer)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return expWithLayer, nil
}

// AvailableRate 流量校验
// layerId: 流量层ID，excludeExpId：排除的实验ID（一般为当前实验）， currentRate：当前流量值
func (s *LayerService) AvailableRate(layerId, excludeExpId int64, currentRate int32) error {
	expDao := s.DS.Exp
	expWithLayers, err := expDao.Where(expDao.LayerID.Eq(layerId)).
		Where(expDao.ID.Neq(excludeExpId)).
		Where(expDao.State.In(model.ExpStateRunning, model.ExpStateGrayRunning, model.ExpStateTodo)).
		Where(expDao.IsDeleted.Eq(model.NotDeleted)).Find()
	if err != nil {
		return err
	}
	var sumLayerRate int32
	for _, expWithLayer := range expWithLayers {
		sumLayerRate += expWithLayer.LayerRate
	}
	// 分层容量校验
	if sumLayerRate+currentRate > 100 {
		return base.NewBizError(errors.New("流量层缺乏足够的流量容量"))
	}
	return nil
}
