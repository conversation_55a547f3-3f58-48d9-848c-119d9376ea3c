package service

import (
	"context"
	"errors"
	"fmt"
	"git.7k7k.com/data/abAdmin/httpd/request"
	"git.7k7k.com/data/abAdmin/httpd/response"
	"git.7k7k.com/data/abAdmin/infra/auth"
	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/repository"
	"git.7k7k.com/data/abAdmin/repository/dao"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	"gorm.io/gen"
	"gorm.io/gorm"
	"time"
)

// @autowire(set=service)
type ApprovalRuleManageService struct {
	DS *dao.Query
	Query
	QueryForBIv1 *repository.QueryForBIv1
}
type ApprovalNodeList struct {
	RuleTypeList  []*response.Item `json:"rule_type_list"`
	UsedList      []*response.Item `json:"used_list"`
	RuleStateList []*response.Item `json:"rule_state_list"`
	RuleMsgList   []*response.Item `json:"rule_msg_list"`
}

func (s *ApprovalRuleManageService) GetAllOnlineApprovalRule(ctx context.Context) ([]*model.ApprovalRule, error) {
	approvalDao := s.DS.ApprovalRule
	return approvalDao.
		WithContext(ctx).
		Where(approvalDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Find()
}

// 获取审批节点列表
func (s *ApprovalRuleManageService) GetApprovalNodeList(ctx context.Context) (*ApprovalNodeList, error) {
	allExpType := make([]*response.Item, 0)
	for k, v := range model.ExpChangeTypeMap {
		allExpType = append(allExpType, &response.Item{
			Label: v,
			Value: k,
		})
	}
	ruleStateList := make([]*response.Item, 0)
	for _, v := range model.ExpChangeTypeList[model.ExpStateChange] {
		ruleStateList = append(ruleStateList, &response.Item{
			Value: v,
			Label: model.ApprovalNodeList[v],
		})
	}
	ruleMsgList := make([]*response.Item, 0)
	for _, v := range model.ExpChangeTypeList[model.ExpMsgChange] {
		ruleMsgList = append(ruleMsgList, &response.Item{
			Value: v,
			Label: model.ApprovalNodeList[v],
		})
	}
	list, err := s.GetAllOnlineApprovalRule(ctx)
	if err != nil {
		return nil, err
	}
	usedList := make([]*response.Item, 0)
	used := make([]model.Stage, 0)
	lo.ForEach(list, func(item *model.ApprovalRule, index int) {
		used = append(used, item.Stages...)
	})
	rule := lo.Uniq(used)
	for _, val := range rule {
		label := model.ApprovalNodeList[string(val)]
		usedList = append(usedList, &response.Item{
			Label: val,
			Value: label,
		})
	}
	approvalNodeList := &ApprovalNodeList{
		RuleTypeList:  allExpType,
		UsedList:      usedList,
		RuleStateList: ruleStateList,
		RuleMsgList:   ruleMsgList,
	}
	return approvalNodeList, nil
}

type ApprovalRuleIterm struct {
	model.ApprovalRule
	Creator      string `json:"creator"`
	StateName    string `json:"state_name"`
	RuleTypeName string `json:"rule_type_name"`
}

// 获取审批规则列表
func (s *ApprovalRuleManageService) GetApprovalRuleList(ctx context.Context, req request.ApprovalListReq) (*response.List, error) {
	wheres := make([]gen.Condition, 0, 10)
	approvalDao := s.DS.ApprovalRule
	var likeSubQuery dao.IApprovalRuleDo
	if req.Search != "" {
		likeClause := fmt.Sprintf("%%%s%%", req.Search)
		userDao := s.QueryForBIv1.User
		users, _ := userDao.WithContext(ctx).Where(userDao.Realname.Like(likeClause)).Debug().Select(userDao.ID, userDao.Realname).Find()
		likeSubQuery = approvalDao.WithContext(ctx).Where(approvalDao.Name.Like(likeClause)).Or(approvalDao.Desc.Like(likeClause))
		if len(users) > 0 {
			for _, user := range users {
				likeSubQuery = likeSubQuery.Or(approvalDao.CreatorID.Eq(user.ID))
			}
		}
		wheres = append(wheres, likeSubQuery)
	}
	if len(req.State) != 0 {
		wheres = append(wheres, approvalDao.State.In(req.State...))
	}
	wheres = append(wheres,
		approvalDao.ProjectID.Eq(auth.GetProjectId64(ctx)),
		approvalDao.State.Neq(model.ApprovalRuleStateOffline),
	)
	ruleList, err := approvalDao.
		WithContext(ctx).
		Where(wheres...).
		Scopes(Paginate(req.Page, req.PageSize)).
		Find()
	if err != nil {
		return nil, err
	}
	uids := lo.Map(ruleList, func(item *model.ApprovalRule, index int) int32 {
		return int32(item.CreatorID)
	})
	userMap, err := s.QueryForBIv1.QueryUserByIds(ctx, uids...)
	if err != nil {
		return nil, err
	}
	ruleItermList := make([]*ApprovalRuleIterm, 0, len(ruleList))
	for _, rule := range ruleList {
		newRule := ApprovalRuleIterm{
			ApprovalRule: *rule,
		}
		if user, ok := userMap[int32(rule.CreatorID)]; ok {
			newRule.Creator = user.Realname
		}
		if name, ok := model.ApprovalRuleStateMap[rule.State]; ok {
			newRule.StateName = name
		}
		if name, ok := model.ExpChangeTypeMap[rule.RuleType]; ok {
			newRule.RuleTypeName = name
		}
		ruleItermList = append(ruleItermList, &newRule)
	}
	total, err := approvalDao.
		WithContext(ctx).
		Where(wheres...).
		Count()
	if err != nil {
		return nil, err
	}
	list := &response.List{
		List:     ruleItermList,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}
	return list, err
}

// 校验审批节点唯一
func (s *ApprovalRuleManageService) CheckApprovalNodeUnique(ctx context.Context, req *model.ApprovalRule) (bool, error) {
	approvalRuleDao := s.DS.ApprovalRule
	approvalTaskDao := s.DS.ApprovalTask
	approvalRuleList, err := approvalRuleDao.
		WithContext(ctx).
		Where(approvalRuleDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Where(approvalRuleDao.State.Neq(model.ApprovalRuleStateOffline)).
		Where(approvalRuleDao.ID.Neq(req.ID)).
		Find()
	if err != nil && !errors.Is(gorm.ErrRecordNotFound, err) {
		return false, err
	}
	// 判断是否有相同名字的审批规则
	approvalNames := lo.Map(approvalRuleList, func(item *model.ApprovalRule, index int) string {
		return item.Name
	})
	if lo.Contains(approvalNames, req.Name) {
		return false, errors.New("流程名字重复")
	}
	// 判断是否有审批类型相同的审批规则
	approvalRuleListMap := lo.SliceToMap(approvalRuleList, func(item *model.ApprovalRule) (int32, *model.ApprovalRule) {
		return item.RuleType, item
	})
	if _, ok := approvalRuleListMap[req.RuleType]; ok {
		typeName := model.ExpChangeTypeMap[req.RuleType]
		errMsg := fmt.Sprintf("已有节点类型为%s的审批流程，同一类型节点仅可创建一个审批流程，保存失败，请修改节点类型", typeName)
		return false, errors.New(errMsg)
	}
	// 判断当前审批规则下，是否有审批中的审批任务
	approvalTask, err := approvalTaskDao.WithContext(ctx).
		Where(approvalTaskDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Where(approvalTaskDao.RuleID.Eq(req.ID)).
		Where(approvalTaskDao.State.Eq(model.ApprovalInit)).
		Take()
	if err != nil && !errors.Is(gorm.ErrRecordNotFound, err) {
		return false, err
	}
	if approvalTask != nil {
		return false, errors.New("该流程有处理中审批，请处理完成后编辑")
	}
	// 判断是否有相同的审批节点
	usedApprovalRuleList := make([]model.Stage, 0, 30)
	lo.ForEach(approvalRuleList, func(item *model.ApprovalRule, index int) {
		usedApprovalRuleList = append(usedApprovalRuleList, item.Stages...)
	})
	usedApprovalRuleList = lo.Uniq(usedApprovalRuleList)
	for _, node := range req.Stages {
		for _, used := range usedApprovalRuleList {
			if node == used {
				return false, nil
			}
		}
	}
	return true, nil
}

// 添加规则
func (s *ApprovalRuleManageService) AddApprovalRule(ctx context.Context, req *model.ApprovalRule) (*model.ApprovalRule, error) {
	rule := &model.ApprovalRule{}
	err := copier.Copy(&rule, req)
	if err != nil {
		return nil, err
	}
	rule.State = model.ApprovalRuleStateClose
	unique, err := s.CheckApprovalNodeUnique(ctx, rule)
	if err != nil {
		return nil, err
	}
	if !unique {
		return nil, errors.New("审批存在，被使用审批节点")
	}
	user, err := s.QueryForBIv1.User.Where(s.QueryForBIv1.User.ID.Eq(int32(auth.GetUserId(ctx)))).Take()
	if err != nil {
		return nil, err
	}
	rule.SearchText = append(rule.SearchText, rule.Name, rule.Desc, user.Realname)
	CreateTime := time.Now()
	rule.CreatedAt = &CreateTime
	rule.UpdatedAt = &CreateTime
	rule.ProjectID = auth.GetProjectId64(ctx)
	rule.CreatorID = int32(auth.GetUserId(ctx))
	err = s.DS.ApprovalRule.Create(rule)
	if err != nil {
		return nil, err
	}
	return rule, nil
}

// 更新规则
func (s *ApprovalRuleManageService) UpdateApprovalRule(ctx context.Context, req *model.ApprovalRule) (*model.ApprovalRule, error) {
	rule := &model.ApprovalRule{}
	err := copier.Copy(&rule, req)
	if err != nil {
		return nil, err
	}
	unique, err := s.CheckApprovalNodeUnique(ctx, rule)
	if err != nil {
		return nil, err
	}
	if !unique {
		return nil, errors.New("审批规则，存在被使用审批节点")
	}
	UpdateTime := time.Now()
	rule.UpdatedAt = &UpdateTime
	_, err = s.DS.ApprovalRule.Updates(rule)
	if err != nil {
		return nil, err
	}
	return rule, err
}

func (s *ApprovalRuleManageService) SwitchApprovalRule(ctx context.Context, ruleId int64, state int32) (*model.ApprovalRule, error) {
	if ruleId == 0 {
		return nil, nil
	}
	_, ok := model.ApprovalRuleStateMap[state]
	if !ok || state == model.ApprovalRuleStateOffline {
		return nil, errors.New("状态错误")
	}
	approvalTaskDao := s.DS.ApprovalTask
	approvalTask, err := approvalTaskDao.WithContext(ctx).
		Where(approvalTaskDao.RuleID.Eq(ruleId)).
		Where(approvalTaskDao.State.Eq(model.ApprovalInit)).
		Take()
	if err != nil && !errors.Is(gorm.ErrRecordNotFound, err) {
		return nil, err
	}
	if approvalTask != nil && state == model.ApprovalRuleStateClose {
		return nil, errors.New("该流程有处理中审批，请处理完成后关闭")
	}
	rule, err := s.DS.ApprovalRule.WithContext(ctx).Where(s.DS.ApprovalRule.ID.Eq(ruleId)).Take()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("审批规则，不合法")
		}
		return nil, err
	}
	rule.State = state
	now := time.Now()
	rule.UpdatedAt = &now
	_, err = s.DS.ApprovalRule.Updates(rule)
	if err != nil {
		return nil, err
	}
	return rule, nil
}

func (s *ApprovalRuleManageService) DeleteApprovalRule(ctx context.Context, ruleId int64) (*model.ApprovalRule, error) {
	if ruleId == 0 {
		return nil, errors.New("审批规则id，不能为空")
	}
	approvalTaskDao := s.DS.ApprovalTask
	approvalTask, err := approvalTaskDao.
		WithContext(ctx).
		Where(approvalTaskDao.State.Eq(model.ApprovalInit)).
		Where(approvalTaskDao.RuleID.Eq(ruleId)).
		Take()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	if approvalTask != nil {
		return nil, errors.New("该流程有处理中审批，请处理完成后删除")
	}
	rule, err := s.DS.ApprovalRule.WithContext(ctx).Where(s.DS.ApprovalRule.ID.Eq(ruleId)).Take()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("审批规则，不合法")
		}
		return nil, err
	}

	rule.State = model.ApprovalRuleStateOffline
	now := time.Now()
	rule.UpdatedAt = &now
	_, err = s.DS.ApprovalRule.Updates(rule)
	if err != nil {
		return nil, err
	}
	return rule, nil
}

// 根据条件获取指定审批规则
func (e *ApprovalRuleManageService) GetOneApprovalRuleByCondition(ctx context.Context, conditions []gen.Condition) (*model.ApprovalRule, error) {
	approvalRuleDao := e.DS.ApprovalRule
	wheres := make([]gen.Condition, 0, 10)
	wheres = append(wheres, approvalRuleDao.ProjectID.Eq(auth.GetProjectId64(ctx)))
	if len(conditions) > 0 {
		wheres = append(wheres, conditions...)
	}
	approvalRule, err := approvalRuleDao.
		WithContext(ctx).
		Where(wheres...).
		Take()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return approvalRule, nil
}

// 判断是否需要审批
func (e *ApprovalRuleManageService) IsNeedApproval(ctx context.Context, approvalRuleType int32, changeNodeList []*model.ChangeNode) (bool, *model.ApprovalRule, error) {
	var isNeed bool
	approvalRuleDao := e.DS.ApprovalRule
	approvalRule, err := e.GetOneApprovalRuleByCondition(ctx, []gen.Condition{
		approvalRuleDao.RuleType.Eq(approvalRuleType),
		approvalRuleDao.State.Eq(model.ApprovalRuleStateOnline),
	})
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return isNeed, approvalRule, err
	}
	if approvalRule == nil {
		return isNeed, approvalRule, nil
	}
	intersectNodes := make([]string, 0, 5)
	changeNodes := lo.Map(changeNodeList, func(item *model.ChangeNode, index int) string {
		return item.NodeType
	})
	if len(changeNodes) > 0 {
		var ruleNodes []string
		for _, stage := range approvalRule.Stages {
			ruleNodes = append(ruleNodes, string(stage))
		}
		intersectNodes = lo.Intersect(ruleNodes, changeNodes)
	}
	if len(intersectNodes) > 0 {
		isNeed = true
	}
	return isNeed, approvalRule, nil
}
