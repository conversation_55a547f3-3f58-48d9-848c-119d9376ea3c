package service

import (
	"errors"
	"fmt"
	"git.7k7k.com/data/abAdmin/httpd/base"
	"git.7k7k.com/data/abAdmin/httpd/request"
	"git.7k7k.com/data/abAdmin/infra/auth"
	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/repository/dao"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
	"time"
)

// @autowire(set=service)
type BatchService struct {
	DS *dao.Query
	Query
}

// Create 创建批次
func (s *BatchService) Create(ctx *gin.Context, req request.BatchReq) error {
	now := time.Now()
	batch := &model.Batch{
		Name:       req.Name,
		ProjectID:  auth.GetProjectId64(ctx),
		Desc:       req.Desc,
		CreateTime: now,
		UpdateTime: now,
		IsDeleted:  model.NotDeleted,
	}

	batchDao := s.DS.Batch
	// 判断批次的名称是否重复
	count, err := batchDao.Where(batchDao.ProjectID.Eq(batch.ProjectID), batchDao.Name.Eq(batch.Name),
		batchDao.IsDeleted.Eq(model.NotDeleted)).Count()
	if err != nil {
		return err
	}
	if count > 0 {
		return base.NewAlertTip(fmt.Sprintf("名称[%s]已经被使用", req.Name))
	}

	// 创建批次
	return batchDao.Create(batch)
}

// Update 更新批次
func (s *BatchService) Update(req request.BatchUpdate) error {
	now := time.Now()
	batch := &model.Batch{
		Name:       req.Name,
		Desc:       req.Desc,
		UpdateTime: now,
	}
	batchDao := s.DS.Batch

	count, err := batchDao.Where(batchDao.ID.Neq(cast.ToInt64(req.Id)), batchDao.Name.Eq(batch.Name),
		batchDao.IsDeleted.Eq(model.NotDeleted)).Count()
	if err != nil {
		return err
	}
	if count > 0 {
		return base.NewAlertTip(fmt.Sprintf("名称[%s]已经被使用", req.Name))
	}

	result, err := batchDao.Where(batchDao.ID.Eq(cast.ToInt64(req.Id)), batchDao.IsDeleted.Eq(model.NotDeleted)).Updates(batch)
	if err != nil {
		return err
	}
	return result.Error
}

// Delete 删除批次，将状态设置为删除
func (s *BatchService) Delete(req request.BatchDelete) error {
	expDao := s.DS.Exp
	count, err := expDao.Where(expDao.BatchID.Eq(req.Id)).Count()
	if err != nil {
		return err
	}
	if count > 0 {
		return base.NewAlertTip("批次被实验引用，不可删除")
	}

	batchDao := s.DS.Batch
	result, err := batchDao.Where(batchDao.ID.Eq(req.Id)).Update(batchDao.IsDeleted, model.Deleted)
	if err != nil {
		return err
	}
	return result.Error
}

// List 批次的列表（含过滤条件）
func (s *BatchService) List(ctx *gin.Context, query *request.BatchQuery) ([]*model.Batch, int64, error) {
	batchDao := s.DS.Batch
	do := batchDao.Where(batchDao.IsDeleted.Eq(model.NotDeleted), batchDao.ProjectID.Eq(auth.GetProjectId64(ctx)))
	if query.Keyword != "" {
		likeClause := fmt.Sprintf("%%%s%%", query.Keyword)
		subQuery := batchDao.Where(batchDao.Name.Like(likeClause)).Or(batchDao.Desc.Like(likeClause))
		do.Where(subQuery)
	}
	return do.Order(batchDao.CreateTime.Desc()).FindByPage(s.CalcOffsetLimit(query.Page, query.Size))
}

// QueryBatchByIds 通过id查询批次
func (s *BatchService) QueryBatchByIds(ids ...int64) ([]*model.Batch, error) {
	if len(ids) == 0 {
		return nil, base.NewAlertTip("批次id必填")
	}
	batchDao := s.DS.Batch
	data, err := batchDao.Where(batchDao.IsDeleted.Eq(model.NotDeleted), batchDao.ID.In(ids...)).Find()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return data, err
}
