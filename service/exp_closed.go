package service

import (
	"context"
	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/repository/dao"
)

// ClosedStrategy 关闭实验
type ClosedStrategy struct {
	Status       model.ExpStatus
	ApprovalNode string
}

// Close 关闭实验
func (s *ExpService) Close(ctx context.Context, exp *model.Exp) error {
	return s.ExpStateService.ToggleExpStatus(ctx, exp, &ClosedStrategy{ApprovalNode: model.ExpClose})
}

// TryTransStatus 关闭实验
func (o *ClosedStrategy) TryTransStatus(status model.ExpStatus) bool {
	o.Status = model.ExpStatusClosed

	return status == model.ExpStatusRunning ||
		status == model.ExpStatusRunningWithoutFlow ||
		status == model.ExpStatusPending ||
		status == model.ExpStatusGrayRunning ||
		status == model.ExpStatusGrayRunningWithoutFlow
}

// GetStatusAfterTrans 已关闭
func (o *ClosedStrategy) GetStatusAfterTrans() model.ExpStatus {
	return o.Status
}

// GetOperate 关闭操作
func (o *ClosedStrategy) GetOperate() ExpOpt {
	return expClosedOpt
}

func (o *ClosedStrategy) GetApprovalNode() string {
	return o.ApprovalNode
}

// CreateOrUpdateTask 关闭实验
func (o *ClosedStrategy) CreateOrUpdateTask(tx *dao.Query, newExp *model.Exp) error {
	return UpdateTask(tx, newExp, model.TaskStatusFinish)
}
func (o *ClosedStrategy) PrepareGroupData(groups []*model.Group) error {
	err := CloseGroup(groups)
	if err != nil {
		return err
	}
	return nil
}

// UpdateGroupStatus 关闭实验组
func (o *ClosedStrategy) UpdateGroupStatus(tx *dao.Query, groups []*model.Group) error {
	err := UpdateGroup(tx, groups)
	if err != nil {
		return err
	}
	return nil
}
