package service

import (
	"context"
	"strings"
	"time"

	"git.7k7k.com/data/abAdmin/httpd/response"
	"git.7k7k.com/data/abAdmin/infra/config"
	"github.com/samber/lo"

	"git.7k7k.com/data/abAdmin/httpd/base"
	"git.7k7k.com/data/abAdmin/infra/auth"

	"github.com/cockroachdb/errors"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"

	"git.7k7k.com/data/abAdmin/httpd/request"
	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/repository/dao"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// Query 查询服务
// @autowire(set=service)
type ProjectService struct {
	DS           *dao.Query
	LayerService *LayerService
	GroupService *GroupService
	AuthService  *AuthService
}

func (s *ProjectService) Add(req request.ProjectReq) error {
	// 检查项目是否存在
	projectDao := s.DS.Project
	first, err := projectDao.Where(projectDao.UniqueID.Eq(req.UniqueId)).Take()
	if errors.Is(err, gorm.ErrRecordNotFound) {
		project := &model.Project{}
		err = copier.Copy(project, req)
		if err != nil {
			return err
		}
		err = isValidAllowUserKeys(req.Data.AllowUserKeys)
		if err != nil {
			return err
		}
		project.CreateTime = time.Now()
		project.UpdateTime = time.Now()
		err = s.DS.Project.Create(project)
		if err != nil {
			return err
		}
		err = s.AuthService.RefreshCache()
		return err
	} else {
		if first != nil && first.ID > 0 {
			return base.NewBizError(errors.New("项目,已经存在"))
		} else {
			return err
		}
	}
}

type ProjectDTO struct {
	model.Project
	CreateTime string `json:"create_time"`
	UpdateTime string `json:"update_time"`
	DownTime   string `json:"down_time"`
}

func ToProjectDTO(project *model.Project) *ProjectDTO {
	return &ProjectDTO{
		Project:    *project,
		CreateTime: project.CreateTime.Format("2006-01-02 15:04:05"),
		UpdateTime: project.UpdateTime.Format("2006-01-02 15:04:05"),
		DownTime:   project.DownTime.Format("2006-01-02 15:04:05"),
	}
}

func (s *ProjectService) List(req request.ListProjectReq, ctx *gin.Context) ([]*ProjectDTO, error) {
	project := s.DS.Project
	retList := make([]*ProjectDTO, 0, 10)
	var err error
	query := project.WithContext(ctx)
	if req.Search != "" {
		query = query.Where(project.Name.Like("%" + req.Search + "%")).
			Or(project.UniqueID.Like("%" + req.Search + "%")).Or(project.Desc.Like("%" + req.Search + "%"))
	}
	if config.GlobalConfig.IsProd() {
		query = query.Order(project.ID.Desc())
	} else {
		query = query.Order(project.UpdateTime.Desc())
	}
	list, err := query.Where(project.State.Eq(req.State)).Find()

	for _, v := range list {
		projectId := cast.ToInt(v.ID)
		ok := auth.InProject(ctx, projectId)
		if ok {
			retList = append(retList, ToProjectDTO(v))
		}
	}
	return retList, err
}

func (s *ProjectService) DownList(req request.ListProjectReq, ctx *gin.Context) ([]*ProjectDTO, error) {
	project := s.DS.Project
	retList := make([]*ProjectDTO, 0, 10)
	var err error
	var list []*model.Project
	if req.Search != "" {
		list, err = project.Where(project.Name.Like("%" + req.Search + "%")).
			Or(project.UniqueID.Like("%" + req.Search + "%")).Or(project.Desc.Like("%" + req.Search + "%")).Debug().
			Where(project.State.Eq(req.State)).Order(project.DownTime.Desc()).Find()
	} else {
		list, err = s.DS.WithContext(ctx).Project.Debug().Where(project.State.Eq(req.State)).Order(project.DownTime.Desc()).Find()
	}
	for _, v := range list {
		retList = append(retList, ToProjectDTO(v))
	}
	return retList, err
}
func isValidAllowUserKeys(allowUserKeys []string) error {
	if len(allowUserKeys) == 0 {
		return nil
	}
	for _, v := range allowUserKeys {
		if _, ok := config.ExpressionKeys[v]; !ok {
			return errors.New("策略，存在不合法的:" + v)
		}
	}
	return nil
}
func (s *ProjectService) Update(req request.ProjectReq, ctx *gin.Context) (int64, error) {
	project := &model.Project{}
	project.UpdateTime = time.Now()
	err := isValidAllowUserKeys(req.Data.AllowUserKeys)
	if err != nil {
		return 0, err
	}
	err = copier.Copy(project, &req)
	if err != nil {
		return 0, err
	}
	projectDao := s.DS.Project
	info, err := projectDao.WithContext(ctx).Where(projectDao.UniqueID.Eq(req.UniqueId)).Updates(project)
	if err != nil {
		return 0, err
	}
	return info.RowsAffected, err
}

func (s *ProjectService) Down(req request.DownProjectReq, ctx *gin.Context) (int64, error) {
	project := &model.Project{}
	project.DownTime = time.Now()
	project.UpdateTime = time.Now()
	project.State = req.State
	projectDao := s.DS.Project
	expDao := s.DS.Exp
	projectFirst, _ := projectDao.WithContext(ctx).Where(projectDao.ID.Eq(req.Id)).First()
	if projectFirst == nil {
		return 0, errors.New("项目不存在")
	}
	// 检查项目下是否有实验
	take, err := s.DS.WithContext(ctx).Exp.
		Where(expDao.State.Neq(model.ExpStateClose)).
		Where(expDao.ProjectID.Eq(projectFirst.ID)).
		Where(expDao.IsDeleted.Eq(model.NotDeleted)).
		Take()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return 0, err
	}
	if take != nil {
		return 0, errors.New("该项目还有未开始和进行的实验，请关闭实验后再尝试")
	}

	info, err := s.DS.WithContext(ctx).Project.Where(projectDao.ID.Eq(req.Id)).Updates(project)
	return info.RowsAffected, err
}

func (s *ProjectService) RemoveLayerExpFeature(ctx context.Context, exp *model.Exp, groups []*model.Group) error {
	project, err := s.Detail(ctx, exp.ProjectID)
	if err != nil {
		return err
	}
	featureStats := project.FeatureStats
	if featureStats == nil {
		return nil
	}
	layerList, err := s.LayerService.QueryLayerByIds(exp.LayerID)
	if err != nil {
		return err
	}
	layer := layerList[0]
	layerFeatureId := featureStats.GetLayerFeatureIdMap(model.LevelID(layer.Level), exp.LayerID)
	if layerFeatureId == nil {
		return nil
	}
	layerFeatureKey := featureStats.GetLayerFeatureKeyMap(model.LevelID(layer.Level), exp.LayerID)
	if layerFeatureKey == nil {
		return nil
	}
	if groups == nil {
		groups, err = s.GroupService.QueryGroupByExpId(ctx, exp.ID)
		if err != nil {
			return err
		}
	}
	//groupFeatureIdMap := make(map[int64][]string, len(groups))
	//groupFeatureKeyMap := make(map[int64][]string, len(groups))
	for _, group := range groups {
		processor := project.Data.GroupParamStyle.Processor()
		featureKeys := processor.GetFeatureKeys(group.ParamsContent)
		featureIds := processor.GetFeatureIDs(group.ParamsContent)
		//groupFeatureIdMap[group.ID] = featureIds
		//groupFeatureKeyMap[group.ID] = featureKeys
		layerFeatureId = removeFeatureFromMap(layerFeatureId, featureIds, group.ID)
		layerFeatureKey = removeFeatureFromMap(layerFeatureKey, featureKeys, group.ID)
	}
	//for groupId, featureIds := range groupFeatureIdMap {
	//	if len(featureIds) == 0 {
	//		continue
	//	}
	//	for _, featureId := range featureIds {
	//		featureGroupIds := layerFeatureId[model.FeatureID(featureId)]
	//		if len(featureGroupIds) > 0 {
	//			featureGroupIds = lo.Without(featureGroupIds, groupId)
	//			if len(featureGroupIds) == 0 {
	//				delete(layerFeatureId, model.FeatureID(featureId))
	//				continue
	//			}
	//			layerFeatureId[model.FeatureID(featureId)] = featureGroupIds
	//		}
	//	}
	//}
	//for groupId, featureKeys := range groupFeatureKeyMap {
	//	if len(featureKeys) == 0 {
	//		continue
	//	}
	//	for _, featureKey := range featureKeys {
	//		featureGroupIds := layerFeatureKey[model.FeatureKey(featureKey)]
	//		if len(featureGroupIds) > 0 {
	//			featureGroupIds = lo.Without(featureGroupIds, groupId)
	//			if len(featureGroupIds) == 0 {
	//				delete(layerFeatureKey, model.FeatureKey(featureKey))
	//				continue
	//			}
	//			layerFeatureKey[model.FeatureKey(featureKey)] = featureGroupIds
	//		}
	//	}
	//}
	projectDao := s.DS.Project
	featureStats.DataFeatureId[model.LevelID(layer.Level)][model.LayerID(exp.LayerID)] = layerFeatureId
	featureStats.DataFeatureKey[model.LevelID(layer.Level)][model.LayerID(exp.LayerID)] = layerFeatureKey
	project.FeatureStats = featureStats
	project.UpdateTime = time.Now()
	_, err = projectDao.WithContext(ctx).
		Select(projectDao.FeatureConflict, projectDao.FeatureStats, projectDao.UpdateTime).
		Where(projectDao.ID.Eq(auth.GetProjectId64(ctx))).Updates(project)
	return nil
}

func removeFeatureFromMap[K ~string](featureMap map[K][]int64, features []string, groupId int64) map[K][]int64 {
	for _, feature := range features {
		if groupIds, ok := featureMap[K(feature)]; ok {
			groupIds = lo.Without(groupIds, groupId)
			if len(groupIds) == 0 {
				delete(featureMap, K(feature))
			} else {
				featureMap[K(feature)] = groupIds
			}
		}
	}
	return featureMap
}

func (s *ProjectService) GetConflictGroups(ctx context.Context, exp *model.Exp, groups []*model.Group) ([]*response.ConflictGroup, error) {
	project, err := s.Detail(ctx, exp.ProjectID)
	if err != nil {
		return nil, err
	}
	layerList, err := s.LayerService.QueryLayerByIds(exp.LayerID)
	if err != nil {
		return nil, err
	}

	layer := layerList[0]
	featureStats := project.FeatureStats
	if featureStats == nil {
		return nil, nil
	}
	// 处理 Key 冲突
	processor := project.Data.GroupParamStyle.Processor()
	groupFeatureKeyMap := lo.SliceToMap(groups, func(item *model.Group) (string, []string) {
		return item.Name, processor.GetFeatureKeys(item.ParamsContent)
	})
	// 处理 ID 冲突
	groupFeatureIdMap := lo.SliceToMap(groups, func(item *model.Group) (string, []string) {
		return item.Name, processor.GetFeatureIDs(item.ParamsContent)
	})

	groupsMap := lo.KeyBy(groups, func(g *model.Group) string { return g.Name })

	// 冲突检测公共逻辑抽象
	type conflictSource func(string) ([]int64, map[string]map[int64]bool)

	processConflictGroups := func(source conflictSource, groupFeatureMap map[string][]string, getLabel func(string, string) string) ([]*response.ConflictGroup, error) {
		var conflictGroups []*response.ConflictGroup
		for groupName, features := range groupFeatureMap { // 根据实际类型切换 Map
			if len(features) == 0 {
				continue
			}

			cg := &response.ConflictGroup{
				GroupName: groupName,
				Conflicts: make([]*response.Conflict, 0, len(features)),
			}

			for _, feature := range features {
				conflictGroupIds, conflictKeys := source(feature)
				if len(conflictGroupIds) == 0 {
					continue
				}

				conflictGroupList, err := s.GroupService.GetGroups(ctx, conflictGroupIds)
				if err != nil || len(conflictGroupList) == 0 {
					continue
				}

				// 分组处理冲突实验
				conflictGroupsByExp := lo.GroupBy(conflictGroupList, func(g *model.Group) int64 { return g.ExpID })
				expList, _ := s.DS.Exp.WithContext(ctx).Where(s.DS.Exp.ID.In(lo.Keys(conflictGroupsByExp)...)).Find()
				expMap := lo.KeyBy(expList, func(e *model.Exp) int64 { return e.ID })

				for expID, groupsInExp := range conflictGroupsByExp {
					exp, exists := expMap[expID]
					if !exists {
						continue
					}

					conflictParams := lo.FlatMap(groupsInExp, func(g *model.Group, _ int) []string {
						return lo.FilterMap(lo.Keys(conflictKeys), func(key string, _ int) (string, bool) {
							return processor.ConflictLabel(g.ParamsContent, key), conflictKeys[key][g.ID]
						})
					})

					if len(conflictParams) == 0 {
						continue
					}

					cg.Conflicts = append(cg.Conflicts, &response.Conflict{
						ParamContent:   getLabel(groupName, feature),
						ConflictParams: conflictParams,
						ExpId:          expID,
						ExpName:        exp.Name,
						GroupsName:     lo.Map(groupsInExp, func(g *model.Group, _ int) string { return g.Name }),
						GroupIds:       lo.Map(groupsInExp, func(g *model.Group, _ int) int64 { return g.ID }),
					})
				}
			}

			if len(cg.Conflicts) > 0 {
				conflictGroups = append(conflictGroups, cg)
			}
		}
		return conflictGroups, nil
	}

	// 处理两种冲突类型
	idConflictGroups, _ := processConflictGroups(
		func(featureId string) ([]int64, map[string]map[int64]bool) {
			conflictKeys := make(map[string]map[int64]bool)
			conflictGroupIds := make([]int64, 0)
			for _, fid := range project.FeatureConflict[featureId] {
				if ids := featureStats.GetDiffLayerFeatureIdMap(model.LevelID(layer.Level), exp.LayerID)[fid]; len(ids) > 0 {
					conflictKeys[fid] = lo.SliceToMap(ids, func(id int64) (int64, bool) { return id, true })
					conflictGroupIds = append(conflictGroupIds, ids...)
				}
			}
			ids := featureStats.GetDiffLayerFeatureIdMap(model.LevelID(layer.Level), exp.LayerID)[featureId]
			if len(ids) > 0 {
				conflictKeys[featureId] = lo.SliceToMap(ids, func(id int64) (int64, bool) { return id, true })
				conflictGroupIds = append(conflictGroupIds, ids...)
			}
			conflictGroupIds = lo.Uniq(conflictGroupIds)
			return conflictGroupIds, conflictKeys
		},
		groupFeatureIdMap,
		func(groupName, featureId string) string {
			return processor.ConflictLabel(groupsMap[groupName].ParamsContent, featureId)
		},
	)

	keyConflictGroups, _ := processConflictGroups(
		func(featureKey string) ([]int64, map[string]map[int64]bool) {
			conflictKeys := make(map[string]map[int64]bool)
			if ids := featureStats.GetDiffLayerFeatureKeyMap(model.LevelID(layer.Level), exp.LayerID)[featureKey]; len(ids) > 0 {
				conflictKeys[featureKey] = lo.SliceToMap(ids, func(id int64) (int64, bool) { return id, true })
				return ids, conflictKeys
			}
			return nil, nil
		},
		groupFeatureKeyMap,
		func(groupName, featureKey string) string {
			return processor.ConflictLabel(groupsMap[groupName].ParamsContent, featureKey)
		},
	)

	// 合并结果
	finalConflictGroups := append(idConflictGroups, keyConflictGroups...)
	uniqueGroups := make(map[string]bool)
	uniqueConflictGroups := make(map[string][]*response.Conflict, 0)
	for _, cg := range finalConflictGroups {
		if !uniqueGroups[cg.GroupName] {
			uniqueGroups[cg.GroupName] = true
			uniqueConflictGroups[cg.GroupName] = cg.Conflicts
		} else {
			uniqueConflictGroups[cg.GroupName] = append(uniqueConflictGroups[cg.GroupName], cg.Conflicts...)
		}
	}
	finalConflictGroups = make([]*response.ConflictGroup, 0, len(uniqueConflictGroups))
	for groupName, conflicts := range uniqueConflictGroups {
		finalConflictGroups = append(finalConflictGroups, &response.ConflictGroup{
			GroupName: groupName,
			Conflicts: lo.UniqBy(conflicts, func(e *response.Conflict) string {
				return e.ExpName + strings.Join(e.ConflictParams, ",")
			}),
		})
	}
	return finalConflictGroups, nil
}

func (s *ProjectService) SaveProjectFeatureStats(ctx context.Context, exp *model.Exp) error {
	layerList, err := s.LayerService.QueryLayerByIds(exp.LayerID)
	if err != nil {
		return err
	}
	layer := layerList[0]
	layerDao := s.DS.Layer
	expDao := s.DS.Exp
	layerList, err = layerDao.
		Where(layerDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Where(layerDao.State.Eq(model.StateOnline)).
		Where(layerDao.Level.Eq(layer.Level)).
		Find()
	if err != nil {
		return err
	}
	layerListMap := lo.KeyBy(layerList, func(item *model.Layer) int64 {
		return item.ID
	})
	project, err := s.Detail(ctx, exp.ProjectID)
	if err != nil {
		return err
	}
	featureStats := project.FeatureStats
	if featureStats == nil || featureStats.DataFeatureKey == nil || featureStats.DataFeatureId == nil {
		featureStats = &model.FeatureStats{
			DataFeatureKey: make(map[model.LevelID]map[model.LayerID]map[model.FeatureKey][]int64, 0),
			DataFeatureId:  make(map[model.LevelID]map[model.LayerID]map[model.FeatureID][]int64, 0),
		}
		for i := 1; i < 4; i++ {
			levelFeatureKey := make(map[model.LayerID]map[model.FeatureKey][]int64, 0)
			featureStats.DataFeatureKey[model.LevelID(i)] = levelFeatureKey
			levelFeatureId := make(map[model.LayerID]map[model.FeatureID][]int64, 0)
			featureStats.DataFeatureId[model.LevelID(i)] = levelFeatureId
		}
	}
	expList, err := expDao.WithContext(ctx).
		Where(expDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Where(expDao.State.NotIn(model.ExpStateClose, model.ExpStateDraft)).
		Find()
	if err != nil {
		return err
	}
	if len(expList) == 0 {
		return nil
	}
	expListMap := lo.KeyBy(expList, func(item *model.Exp) int64 {
		return item.ID
	})
	expIds := lo.Map(expList, func(item *model.Exp, index int) int64 {
		return item.ID
	})

	groupList, err := s.GroupService.QueryGroupByExpIds(ctx, expIds)
	if err != nil {
		return err
	}
	if len(groupList) == 0 {
		return nil
	}
	groupListMap := lo.GroupBy(groupList, func(item *model.Group) int64 {
		return item.ExpID
	})
	processor := project.Data.GroupParamStyle.Processor()
	for expId, groups := range groupListMap {
		expInfo := expListMap[expId]
		if expInfo == nil {
			continue
		}
		layerInfo := layerListMap[expInfo.LayerID]
		if layerInfo == nil || layerInfo.Level == model.LayerNoLevel {
			continue
		}
		for _, group := range groups {
			featureKeys := processor.GetFeatureKeys(group.ParamsContent)
			featureIds := processor.GetFeatureIDs(group.ParamsContent)
			if len(featureKeys) == 0 && len(featureIds) == 0 {
				continue
			}
			for _, featureKey := range featureKeys {
				layerFeatureKeyMap := featureStats.GetLayerFeatureKeyMap(layerInfo.Level, layerInfo.ID)
				if layerFeatureKeyMap == nil {
					layerFeatureKeyMap = make(map[model.FeatureKey][]int64, 0)
				}
				layerFeatureKeyMap[model.FeatureKey(featureKey)] = append(layerFeatureKeyMap[model.FeatureKey(featureKey)], group.ID)
				layerFeatureKeyMap[model.FeatureKey(featureKey)] = lo.Uniq(layerFeatureKeyMap[model.FeatureKey(featureKey)])
				featureStats.SetLayerFeatureKeyMap(layerInfo.Level, layerInfo.ID, layerFeatureKeyMap)
			}
			for _, featureId := range featureIds {
				layerFeatureIdMap := featureStats.GetLayerFeatureIdMap(layerInfo.Level, layerInfo.ID)
				if layerFeatureIdMap == nil {
					layerFeatureIdMap = make(map[model.FeatureID][]int64, 0)
				}
				layerFeatureIdMap[model.FeatureID(featureId)] = append(layerFeatureIdMap[model.FeatureID(featureId)], group.ID)
				layerFeatureIdMap[model.FeatureID(featureId)] = lo.Uniq(layerFeatureIdMap[model.FeatureID(featureId)])
				featureStats.SetLayerFeatureIdMap(layerInfo.Level, layerInfo.ID, layerFeatureIdMap)
			}
		}
	}
	project.FeatureStats = featureStats
	project.UpdateTime = time.Now()
	projectDao := s.DS.Project
	_, err = projectDao.WithContext(ctx).
		Select(projectDao.FeatureConflict, projectDao.FeatureStats, projectDao.UpdateTime).
		Where(projectDao.ID.Eq(auth.GetProjectId64(ctx))).
		Updates(project)
	return err
}
func (s *ProjectService) DetailCtx(ctx context.Context) (*model.Project, error) {
	return s.Detail(ctx, auth.GetProjectId64(ctx))
}

func (s *ProjectService) Detail(ctx context.Context, projectId int64) (*model.Project, error) {
	projectDao := s.DS.Project
	return projectDao.WithContext(ctx).Debug().
		Where(projectDao.ID.Eq(projectId)).
		Where(projectDao.State.Eq(model.StateOnline)).
		Take()
}

func (s *ProjectService) IsParamsValid(ctx context.Context, projectId int64, params string) error {
	project, err := s.Detail(ctx, projectId)
	if err != nil {
		return err
	}
	processor := project.Data.GroupParamStyle.Processor()
	err = processor.Valid(params)
	return err
}
