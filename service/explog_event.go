package service

import (
	"fmt"
	"github.com/samber/lo"
	"strings"

	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/model/dto"
	"github.com/spf13/cast"
)

const (
	createExp                  = "创建实验"
	expOwnerChangeFormat       = "实验负责人：由%s变更为%s\n"
	expFlowChangeFormat        = "实验流量：从%d%%变更为%d%%\n"
	expUserRulesFormat         = "触发用户规则变更：%s 变更为 %s\n"
	expWithoutFlowFormat       = "停止进量触发规则变更：%s 变更为 %s\n"
	expCloseFormat             = "关闭触发规则变更：%s 变更为 %s\n"
	expCreatePlanFormat        = "新建方案：%s\n"
	ExpGroupNoNewFlowFormat    = "停止进量方案：%s\n"
	ExpGroupCloseFormat        = "关闭方案：%s\n %s"
	ExpGroupSplitDetailFormat  = "%s定向到%s %s %d%%"
	ExpGroupRedirectFormat     = "重定向方案: %s\n"
	expStatusChangeFormat      = "实验状态：由 %s 变为 %s"
	expDaysChangeFormat        = "实验时间：由%d天变为%d天\n"
	groupLimitHourChangeFormat = "单方案小时限量：由%v变为%v\n"
	triggerOnceChangeFormat    = "实验进组不出组：由%s变为%s\n"
)

// ChangeEvent 变更事件的结构体
type ChangeEvent struct {
	ExpId        int64
	OriginalData *dto.ExpLog
	NewData      *dto.ExpLog
}

// EventPublisher 事件发布者结构体
type EventPublisher struct {
	subscribers []ChangeHandlerFunc
}

// Subscribe 用于添加订阅者（变更处理函数）到事件发布者
func (p *EventPublisher) Subscribe(handler ...ChangeHandlerFunc) {
	p.subscribers = append(p.subscribers, handler...)
}

type ChangeListenr struct {
	ChangeNode string
	GroupType  string                    // 1 字段修改 2 生效策略修改 3 新增方案 4 关闭方案 5 方案重定向
	LogText    func(*ChangeEvent) string // 生成操作记录的文本
	NotifyText func(*ChangeEvent) string // 生成通知的文本，变量为 nil 时默认使用 LogText
}

// ChangeHandlerFunc 变更处理函数
/**
createExpChanger 新建变更
expOwnerChanger 实验负责人变更，必须填充UserMap
expFlowChanger 实验流量变更
expWithoutFlowChanger 停止进量触发规则
expCloseChanger 关闭触发规则
expCreateGroupChanger 实验方案新建
expGroupWithoutFlowChanger 实验方案停止进量
expGroupCloseChanger 实验方案的关闭
expGroupRedirectChanger 实验方案的重定向
expStateChanger 实验状态变更
*/
type ChangeHandlerFunc func(*ChangeEvent) string

var DoNothing ChangeHandlerFunc = func(*ChangeEvent) string { return "" }

// createExpChanger 新建变更
func createExpChanger(*ChangeEvent) string {
	return createExp
}

// expOwnerChanger 实验负责人变更，负责人变更必须填充UserMap
func (s *ExpLogService) expOwnerChanger(event *ChangeEvent) string {
	if event.OriginalData.Exp.Owner == event.NewData.Exp.Owner {
		return ""
	}

	if len(event.OriginalData.Exp.Owner) == len(event.NewData.Exp.Owner) {
		hasChanged := false

		//	如果只是顺序由变更，则不记录，否则记录
		originalExpMap := make(map[int32]string)
		for _, id := range strings.Split(event.OriginalData.Exp.Owner, ",") {
			if user, ok := event.OriginalData.UserMap[cast.ToInt32(id)]; ok {
				originalExpMap[user.ID] = user.Realname
			}
		}
		for _, id := range event.NewData.Exp.Owner {
			if _, ok := originalExpMap[cast.ToInt32(id)]; !ok {
				hasChanged = true
				break
			}
		}
		// 没有变化，则返回空
		if !hasChanged {
			return ""
		}
	}

	// 有变化
	var originalOwners, newExpOwners string
	var originalOwner strings.Builder
	for i, id := range strings.Split(event.OriginalData.Exp.Owner, ",") {
		if i > 0 {
			originalOwner.WriteString(", ")
		}
		if user, ok := event.OriginalData.UserMap[cast.ToInt32(id)]; ok {
			originalOwner.WriteString(user.Realname)
		} else {
			originalOwner.WriteString(id)
		}
	}
	originalOwners = originalOwner.String()

	var newExpOwner strings.Builder
	for i, id := range strings.Split(event.NewData.Exp.Owner, ",") {
		if i > 0 {
			newExpOwner.WriteString(", ")
		}
		if user, ok := event.NewData.UserMap[cast.ToInt32(id)]; ok {
			newExpOwner.WriteString(user.Realname)
		} else {
			newExpOwner.WriteString(id)
		}
	}
	newExpOwners = newExpOwner.String()

	return fmt.Sprintf(expOwnerChangeFormat, originalOwners, newExpOwners)
}

// expFlowChanger 实验流量变更
func (s *ExpLogService) expFlowChanger(event *ChangeEvent) string {
	if event.OriginalData.Exp.LayerRate == event.NewData.Exp.LayerRate {
		return ""
	}
	return fmt.Sprintf(expFlowChangeFormat,
		event.OriginalData.Exp.LayerRate,
		event.NewData.Exp.LayerRate)
}

// expWithoutFlowChanger 触发用户规则
func (s *ExpLogService) expUserRulesChanger(event *ChangeEvent) string {
	// 比较表达式的变更
	originalExpr := s.StrategyService.GenerateExpression(event.OriginalData.Exp.Strategy.TriggerUserKey)
	newExpr := s.StrategyService.GenerateExpression(event.NewData.Exp.Strategy.TriggerUserKey)

	if originalExpr == newExpr {
		return ""
	}
	return fmt.Sprintf(expUserRulesFormat, originalExpr, newExpr)
}

// expWithoutFlowChanger 停止进量触发规则
func (s *ExpLogService) expWithoutFlowChanger(event *ChangeEvent) string {
	// 比较表达式的变更
	originalExpr := s.StrategyService.GenerateExpression(event.OriginalData.Exp.Strategy.TriggerStopKey)
	newExpr := s.StrategyService.GenerateExpression(event.NewData.Exp.Strategy.TriggerStopKey)
	// slog.Info("expWithoutFlowChanger",
	// 	"originalExpr", originalExpr, "newExpr", newExpr,
	// 	"origin_stop", event.OriginalData.Exp.Strategy.TriggerStopKey.Value,
	// 	"new_stop", event.NewData.Exp.Strategy.TriggerStopKey.Value,
	// 	"origin_type", reflect.TypeOf(event.OriginalData.Exp.Strategy.TriggerStopKey.Value).String(),
	// 	"new_type", reflect.TypeOf(event.NewData.Exp.Strategy.TriggerStopKey.Value).String(),
	// )
	if originalExpr == newExpr {
		return ""
	}
	return fmt.Sprintf(expWithoutFlowFormat, originalExpr, newExpr)
}

// expCloseChanger 关闭触发规则
func (s *ExpLogService) expCloseChanger(event *ChangeEvent) string {
	originalExpr := s.StrategyService.GenerateExpression(event.OriginalData.Exp.Strategy.TriggerEndKey)
	newExpr := s.StrategyService.GenerateExpression(event.NewData.Exp.Strategy.TriggerEndKey)

	// 比较关闭触发规则的内容是否有变更
	if originalExpr == newExpr {
		return ""
	}
	return fmt.Sprintf(expCloseFormat, originalExpr, newExpr)
}

// expCreateGroupChanger 实验方案新建
func (s *ExpLogService) expCreateGroupChanger(event *ChangeEvent) string {
	// 没有新增的方案
	if len(event.NewData.AddGroups) == 0 {
		return ""
	}

	var addPlans strings.Builder
	for i, plan := range event.NewData.AddGroups {
		if i > 0 {
			addPlans.WriteString(", ")
		}
		addPlans.WriteString(plan.Name)
	}
	return fmt.Sprintf(expCreatePlanFormat, addPlans.String())
}

// expGroupWithoutFlowChanger 实验方案停止进量
func (s *ExpLogService) expGroupWithoutFlowChanger(event *ChangeEvent) string {
	if len(event.NewData.UpdateGroups) == 0 {
		return ""
	}
	groupsMap := lo.SliceToMap(event.OriginalData.Exp.Groups, func(group *model.Group) (int64, *model.Group) {
		return group.ID, group
	})

	newExpGroup := make([]*model.Group, 0, len(event.NewData.UpdateGroups))
	for _, group := range event.NewData.UpdateGroups {
		if _, ok := groupsMap[group.ID]; !ok {
			continue
		}
		// 实验存在&状态有变更&变更后的状态是停止进量
		if groupsMap[group.ID].State != group.State && group.State == model.GroupStateRunningNotProgress {
			newExpGroup = append(newExpGroup, group)
		}
	}

	// 没有停止进量的方案
	if len(newExpGroup) == 0 {
		return ""
	}

	var noNewFlow strings.Builder
	for i, plan := range newExpGroup {
		if i > 0 {
			noNewFlow.WriteString(", ")
		}
		noNewFlow.WriteString(plan.Name)
	}

	return fmt.Sprintf(ExpGroupNoNewFlowFormat, noNewFlow.String())
}

// expGroupCloseChanger 实验方案的关闭
func (s *ExpLogService) expGroupCloseChanger(event *ChangeEvent) string {
	if len(event.NewData.SplitGroup) == 0 {
		return ""
	}

	var summary strings.Builder
	var details strings.Builder
	for _, group := range event.NewData.SplitGroup {
		// 实验存在&状态有变更&变更后的状态是关闭
		if group.SplitType != model.SplitGroupTypeClose {
			continue
		}
		if summary.Len() > 0 {
			summary.WriteString(", ")
		}
		summary.WriteString(group.FromGroupName)

		if details.Len() > 0 {
			details.WriteString(",")
		}
		if group.ToGroupName != "" {
			details.WriteString(fmt.Sprintf(ExpGroupSplitDetailFormat, group.FromGroupName, group.ToExpName, group.ToGroupName, group.Rate/100))
		} else {
			//details.WriteString(group.FromGroupName)
			//details.WriteString("释放流量")
		}
	}

	// 没有关闭的方案
	if summary.Len() == 0 {
		return ""
	}

	return fmt.Sprintf(ExpGroupCloseFormat, summary.String(), details.String())
}

// expGroupRedirectChanger 实验方案的重定向
func (s *ExpLogService) expGroupRedirectChanger(event *ChangeEvent) string {
	var details strings.Builder
	for _, group := range event.NewData.SplitGroup {
		if details.Len() > 0 {
			details.WriteString(",")
		}
		if group.SplitType == model.SplitGroupTypeRedirect || group.SplitType == model.SplitGroupTypeRunningRedirect {
			details.WriteString(fmt.Sprintf(ExpGroupSplitDetailFormat, group.FromGroupName, group.ToExpName, group.ToGroupName, group.Rate/100))
		}
	}

	if details.Len() == 0 {
		return ""
	}
	return fmt.Sprintf(ExpGroupRedirectFormat, details.String())
}

// expStateChanger 实验状态变更
func (s *ExpLogService) expStateChanger(event *ChangeEvent) string {
	if event.OriginalData.Exp.State == event.NewData.Exp.State {
		return ""
	}
	return fmt.Sprintf(expStatusChangeFormat,
		model.ExpStatus(event.OriginalData.Exp.State).Name(),
		model.ExpStatus(event.NewData.Exp.State).Name())
}

// expStateChanger 实验状态变更
func (s *ExpLogService) expStateStartChanger(event *ChangeEvent) string {
	if event.OriginalData.Exp.State == event.NewData.Exp.State && event.NewData.Exp.State == model.ExpStateRunning {
		return ""
	}
	return fmt.Sprintf(expStatusChangeFormat,
		model.ExpStatus(event.OriginalData.Exp.State).Name(),
		model.ExpStatus(event.NewData.Exp.State).Name())
}

// expStateChanger 实验状态变更
func (s *ExpLogService) expStateStopChanger(event *ChangeEvent) string {
	if event.OriginalData.Exp.State == event.NewData.Exp.State && event.NewData.Exp.State == model.ExpStateRunningNotProgress {
		return ""
	}
	return fmt.Sprintf(expStatusChangeFormat,
		model.ExpStatus(event.OriginalData.Exp.State).Name(),
		model.ExpStatus(event.NewData.Exp.State).Name())
}

// expStateChanger 实验状态变更
func (s *ExpLogService) expStateCloseChanger(event *ChangeEvent) string {
	if event.OriginalData.Exp.State == event.NewData.Exp.State && event.NewData.Exp.State == model.ExpStateClose {
		return ""
	}
	return fmt.Sprintf(expStatusChangeFormat,
		model.ExpStatus(event.OriginalData.Exp.State).Name(),
		model.ExpStatus(event.NewData.Exp.State).Name())
}

// expStateChanger 实验状态变更
func (s *ExpLogService) expStateRestartChanger(event *ChangeEvent) string {
	if event.OriginalData.Exp.State == event.NewData.Exp.State && event.NewData.Exp.State == model.ExpStateClose {
		return ""
	}
	return fmt.Sprintf(expStatusChangeFormat,
		model.ExpStatus(event.OriginalData.Exp.State).Name(),
		model.ExpStatus(event.NewData.Exp.State).Name())
}

// expDaysChanger 实验天数变更
func (s *ExpLogService) expDaysChanger(event *ChangeEvent) string {
	if event.OriginalData.Exp.Days == event.NewData.Exp.Days {
		return ""
	}

	return fmt.Sprintf(expDaysChangeFormat,
		event.OriginalData.Exp.Days,
		event.NewData.Exp.Days)
}

func (s *ExpLogService) groupLimitHourChanger(event *ChangeEvent) string {
	if event.OriginalData.Exp.GroupLimitHour == event.NewData.Exp.GroupLimitHour {
		return ""
	}
	changeGroupLimitHourFunc := func(limitHour int32) string {
		if limitHour == 0 {
			return "不限制"
		}
		return cast.ToString(limitHour)
	}
	return fmt.Sprintf(groupLimitHourChangeFormat,
		changeGroupLimitHourFunc(event.OriginalData.Exp.GroupLimitHour),
		changeGroupLimitHourFunc(event.NewData.Exp.GroupLimitHour))
}

func (s *ExpLogService) triggerOnceChanger(event *ChangeEvent) string {
	if event.OriginalData.Exp.Strategy.TriggerOnce == event.NewData.Exp.Strategy.TriggerOnce {
		return ""
	}
	convert := func(b bool) string {
		if b {
			return "是"
		}
		return "否"
	}
	return fmt.Sprintf(triggerOnceChangeFormat,
		convert(event.OriginalData.Exp.Strategy.TriggerOnce),
		convert(event.NewData.Exp.Strategy.TriggerOnce))
}
