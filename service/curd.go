package service

import (
	"context"

	"git.7k7k.com/data/abAdmin/model"
	"gorm.io/gorm"
)

type CurdBase[M model.ModelInt] struct {
	db *gorm.DB
}

type ListIn[T model.ModelInt] struct {
	Page
	SearchText  string   `json:"search_text,omitempty" form:"search_text"`
	SearchTexts []string `json:"search_texts,omitempty" form:"search_texts"`

	filter *gorm.DB // 暂时代替 filter
}

func (in *ListIn[T]) FromForm(query *gorm.DB) {
	in.WithQuery(query)
}

func (in *ListIn[T]) WithQuery(query *gorm.DB) {
	if in.filter == nil {
		in.filter = query
	} else {
		in.filter = in.filter.Where(query)
	}
}

func (in *ListIn[T]) applyGlobalFilter(db *gorm.DB) *gorm.DB {
	if in.SearchText != "" {
		db = db.Where("search_text LIKE ? ", "%"+in.SearchText+"%")
	}
	for _, text := range in.SearchTexts {
		db = db.Where("search_text LIKE ? ", "%"+text+"%")
	}

	return applyPage(db, &in.Page)
}

type ListOut[T model.ModelInt] struct {
	Rows []*T `json:"rows"`
	Page
}

type Page struct {
	PageIndex int `json:"page_index,omitempty" form:"page_index"`
	PageTotal int `json:"page_total,omitempty"`

	SortKey string `json:"sort_key,omitempty" form:"sort_key"`
	KeyGt   any    `json:"key_gt,omitempty" form:"key_gt"`
	KeyLt   any    `json:"key_lt,omitempty" form:"key_lt"`

	Limit int `json:"limit,omitempty" form:"limit"`

	Total   int  `json:"total,omitempty"`
	HasMore bool `json:"has_more,omitempty"`
}

type SaveIn[T model.ModelInt] struct {
	Model *T `json:"model"`
}

type SaveOut[T model.ModelInt] struct {
	Model *T `json:"model"`
}

type GetOneIn[T model.ModelInt] struct {
	ID int `json:"id,string" form:"id"`
}

type GetOneOut[T model.ModelInt] struct {
	Model *T `json:"model"`
}

func (c *CurdBase[M]) List(ctx context.Context, in ListIn[M]) (out ListOut[M], err error) {
	query := c.db.WithContext(ctx).Model(c.newModel())
	if in.filter != nil {
		query = query.Where(in.filter)
	}
	query = in.applyGlobalFilter(query)
	err = query.Find(&out.Rows).Error
	if err != nil {
		return
	}

	cnt := int64(0)
	err = query.Count(&cnt).Error
	if err != nil {
		return
	}

	out.Total = int(cnt)
	out.Limit = in.Limit
	out.PageTotal = int(cnt) / in.Limit
	if int(cnt)%in.Limit > 0 {
		out.PageTotal++
	}
	return
}

// Save 保存或更新一个模型实例
func (c *CurdBase[M]) Save(ctx context.Context, in SaveIn[M]) (out SaveOut[M], err error) {
	err = c.db.WithContext(ctx).Save(in.Model).Error
	if err == nil {
		out.Model = in.Model
	}
	return
}

// GetOne 获取单个模型实例
func (c *CurdBase[M]) GetOne(ctx context.Context, in GetOneIn[M]) (out GetOneOut[M], err error) {
	out.Model = c.newModel()
	err = c.db.WithContext(ctx).First(out.Model, in.ID).Error
	return
}

func (c *CurdBase[T]) newModel() *T {
	return new(T)
}

func applyPage(db *gorm.DB, page *Page) *gorm.DB {
	if page.Limit <= 0 {
		page.Limit = 20
	}
	db = db.Limit(page.Limit)

	if page.PageIndex > 0 {
		db = db.Offset((page.PageIndex - 1) * page.Limit)
	}

	return db
}
