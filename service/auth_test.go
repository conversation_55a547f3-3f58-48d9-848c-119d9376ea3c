package service

import (
	"context"
	"testing"

	"git.7k7k.com/data/abAdmin/infra/auth"
	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/repository"
	"git.7k7k.com/data/abAdmin/repository/dao"
	"github.com/glebarez/sqlite"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestRefreshCacheCtx(t *testing.T) {
	// 设置内存数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	assert.NoError(t, err)

	// 迁移表结构
	err = db.AutoMigrate(&model.Permission{}, &model.PermissionRole{}, &model.Role{}, &model.Project{}, &model.UserInfo{})
	assert.NoError(t, err)

	// 插入测试数据
	permissions := []model.Permission{
		{ID: 1, Name: "create", DisplayName: "创建"},
		{ID: 2, Name: "read", DisplayName: "读取"},
		{ID: 3, Name: "update", DisplayName: "更新"},
	}
	err = db.Create(&permissions).Error
	assert.NoError(t, err)

	// 创建角色
	roles := []model.Role{
		{ID: 1, Name: "abtest-admin"},
		{ID: 2, Name: "abtest-user"},
	}
	err = db.Create(&roles).Error
	assert.NoError(t, err)

	// 创建角色权限关系
	permissionRoles := []model.PermissionRole{
		{RoleID: 1, PermissionID: 1},
		{RoleID: 1, PermissionID: 2},
		{RoleID: 1, PermissionID: 3},
		{RoleID: 2, PermissionID: 2},
	}
	err = db.Create(&permissionRoles).Error
	assert.NoError(t, err)

	// 创建项目
	projects := []model.Project{
		{ID: 1, Name: "项目A"},
		{ID: 2, Name: "项目B"},
	}
	err = db.Create(&projects).Error
	assert.NoError(t, err)

	// 创建用户信息
	userInfos := []model.UserInfo{
		{
			UserID: 101,
			Data: model.UserInfoData{
				PrjRoles: []*model.PrjRole{
					{PrjId: 1, RoleIds: []int32{1}}, // 项目A的管理员
					{PrjId: 2, RoleIds: []int32{2}}, // 项目B的普通用户
				},
			},
		},
		{
			UserID: 102,
			Data: model.UserInfoData{
				PrjRoles: []*model.PrjRole{
					{PrjId: 1, RoleIds: []int32{2}}, // 项目A的普通用户
				},
			},
		},
	}
	err = db.Create(&userInfos).Error
	assert.NoError(t, err)

	// 创建测试实例
	q := dao.Use(db)
	authService := &AuthService{Query: q, QueryForBIv1: (*repository.QueryForBIv1)(q)}

	// 执行测试
	err = authService.RefreshCacheCtx(context.Background())
	assert.NoError(t, err)

	// 验证项目A的权限
	storeA := auth.GetProjectStore(1)
	assert.NotNil(t, storeA)

	// 验证用户角色映射
	expectedUserRolesA := map[int][]int{
		101: {1},
		102: {2},
	}
	assert.Equal(t, expectedUserRolesA, storeA.UserRoles)

	// 验证权限角色映射
	expectedPermRolesA := map[int][]int{
		1: {1},
		2: {1, 2},
		3: {1},
	}
	assert.Equal(t, expectedPermRolesA, storeA.PermRoles)

	// 验证权限名称索引
	expectedPermNameIdxA := map[string]int{
		"create": 1,
		"read":   2,
		"update": 3,
	}
	assert.Equal(t, expectedPermNameIdxA, storeA.PermNameIdx)

	// 验证用户权限
	assert.True(t, storeA.CanName(101, "create"))  // 管理员可以创建
	assert.True(t, storeA.CanName(101, "read"))    // 管理员可以读取
	assert.True(t, storeA.CanName(101, "update"))  // 管理员可以更新
	assert.False(t, storeA.CanName(102, "create")) // 普通用户不能创建
	assert.True(t, storeA.CanName(102, "read"))    // 普通用户可以读取
	assert.False(t, storeA.CanName(102, "update")) // 普通用户不能更新

	// 验证项目B的权限
	storeB := auth.GetProjectStore(2)
	assert.NotNil(t, storeB)

	// 验证用户角色映射
	expectedUserRolesB := map[int][]int{
		101: {2},
	}
	assert.Equal(t, expectedUserRolesB, storeB.UserRoles)

	// 验证权限角色映射
	expectedPermRolesB := map[int][]int{
		1: {1},
		2: {1, 2},
		3: {1},
	}
	assert.Equal(t, expectedPermRolesB, storeB.PermRoles)

	// 验证用户权限
	assert.False(t, storeB.CanName(101, "create")) // 普通用户不能创建
	assert.True(t, storeB.CanName(101, "read"))    // 普通用户可以读取
	assert.False(t, storeB.CanName(101, "update")) // 普通用户不能更新
}
