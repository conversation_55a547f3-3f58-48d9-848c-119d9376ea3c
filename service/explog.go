package service

import (
	"context"
	"encoding/json"
	"fmt"
	"gorm.io/gen"
	"strings"
	"time"

	"git.7k7k.com/data/abAdmin/httpd/base"
	"git.7k7k.com/data/abAdmin/httpd/request"
	"git.7k7k.com/data/abAdmin/httpd/response"
	"git.7k7k.com/data/abAdmin/infra/auth"
	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/model/dto"
	"git.7k7k.com/data/abAdmin/repository"
	"git.7k7k.com/data/abAdmin/repository/dao"
	"github.com/cockroachdb/errors"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// @autowire(set=service)
type ExpLogService struct {
	DS *dao.Query
	Query
	MetricService             *MetricService
	UserQuery                 *repository.QueryForBIv1
	LayerService              *LayerService
	GroupService              *GroupService
	BatchService              *BatchService
	TagService                *TagService
	StrategyService           *StrategyService
	ApprovalRuleManageService *ApprovalRuleManageService
	ApprovalTaskManageService *ApprovalTaskManageService
}

// List 实验操作的日志列表（含过滤条件）
func (s *ExpLogService) List(query *request.ExpLogQuery) ([]*model.ExpLog, int64, error) {
	expLogDao := s.DS.ExpLog
	do := expLogDao.Where(expLogDao.ExpID.Eq(query.ExpId)).Where(expLogDao.Show.Eq(1))
	if query.Keyword != "" {
		likeClause := fmt.Sprintf("%%%s%%", query.Keyword)
		subQuery := expLogDao.Where(expLogDao.Changes.Like(likeClause)).Or(expLogDao.Uname.Like(likeClause))
		do.Where(subQuery)
	}
	if query.BeginDate != nil {
		do.Where(expLogDao.CreateTime.Gte(*query.BeginDate))
	}
	if query.EndDate != nil {
		do.Where(expLogDao.CreateTime.Lte(query.EndDate.AddDate(0, 0, 1)))
	}
	return do.Order(expLogDao.CreateTime.Desc()).FindByPage(s.CalcOffsetLimit(query.Page, query.Size))
}

// Detail 实验的快照详情，只返回快照字段
func (s *ExpLogService) Detail(query *request.ExpSnapshotQuery) (any, error) {
	expLogDao := s.DS.ExpLog
	snapshot, err := expLogDao.Select(expLogDao.ExpSnapshot).Where(expLogDao.ID.Eq(query.ExpLogId)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	detail := &dto.Snapshot{}
	err = json.Unmarshal([]byte(*snapshot.ExpSnapshot), detail)
	if err != nil {
		return nil, base.NewBizError(errors.Errorf("failed to unmarshal experiment snapshot，%s", err.Error()))
	}
	return detail, nil
}

// All 返回该实验的所有日志（除快照字段），用于快照的筛选器
func (s *ExpLogService) All(query *request.ExpAllLogQuery) ([]*model.ExpLog, error) {
	expLogDao := s.DS.ExpLog
	all, err := expLogDao.Select(expLogDao.ID, expLogDao.Desc, expLogDao.CreateTime).Where(expLogDao.ExpID.Eq(query.ExpId)).Find()
	if err != nil {
		return nil, err
	}
	return all, nil
}

// CreateExpLogByStatus 通过实验状态的变更创建实验日志
func (s *ExpLogService) CreateExpLogByStatus(ctx context.Context, oldExp, newExp *model.Exp, updateGroups []*model.Group, strategy ExpStateChangeStrategy) (expLog *model.ExpLog, isNeedApproval bool, err error) {
	var isRealNeedApproval bool
	if newExp == nil {
		return nil, isRealNeedApproval, errors.New("new exp should not be nil")
	}
	if newExp.ID == 0 {
		return nil, isRealNeedApproval, errors.New("new exp id is required")
	}

	newExpDto := &dto.ExpLog{
		Exp:               newExp,
		UpdateGroups:      updateGroups,
		ApprovalStateNode: strategy.GetApprovalNode(),
		ExpOperate:        string(strategy.GetOperate()),
		OriginExp:         oldExp,
	}
	snapshot, err := s.buildSnapshot(ctx, newExpDto)
	if err != nil {
		return nil, isRealNeedApproval, err
	}

	// 实验状态的变更，除了实验数据，不需要其他
	event := &ChangeEvent{
		ExpId:        newExp.ID,
		OriginalData: &dto.ExpLog{Exp: oldExp},
		NewData:      newExpDto,
	}
	changeListenrs := []ChangeListenr{
		{LogText: s.expStateChanger, ChangeNode: model.ExpOpen, GroupType: model.ChangeNodeExpStateType},
	}
	changes, changeNodeList, notifyLines, err := handleChangeEvent(event, changeListenrs)
	if err != nil {
		return nil, isRealNeedApproval, err
	}

	newExp.Desc = ""
	expLog, err = s.buildExpLog(ctx, newExp, snapshot, changes, notifyLines)
	if err != nil {
		return nil, isRealNeedApproval, err
	}
	expLog.State = 1
	err = s.DS.ExpLog.Create(expLog)
	if err != nil {
		return nil, isRealNeedApproval, err
	}
	// 获取审批规则
	approvalRuleDao := s.DS.ApprovalRule
	approvalRule, err := s.ApprovalRuleManageService.GetOneApprovalRuleByCondition(ctx, []gen.Condition{
		approvalRuleDao.RuleType.Eq(model.ExpStateChange),
		approvalRuleDao.State.Eq(model.ApprovalRuleStateOnline),
	})
	if err != nil {
		return nil, isRealNeedApproval, err
	}
	// 创建审批
	if approvalRule != nil && lo.Contains(approvalRule.Stages, model.Stage(strategy.GetApprovalNode())) {
		baseChanges := GetBaseChanges(changeNodeList)
		diff := &model.Changes{
			BaseChanges:  baseChanges,
			BatchChanges: nil,
		}
		descArr := lo.Map(baseChanges, func(item *model.Iterm, index int) string {
			return item.Value
		})
		isRealNeedApproval = true
		_, err = s.ApprovalTaskManageService.CreateApprovalTask(ctx, model.ExpStateChange, &model.ApprovalData{
			ExpId:   newExp.ID,
			AfterId: expLog.ID,
			Desc:    strings.Join(descArr, ""),
			Changes: diff,
		})
		if err != nil {
			return nil, isRealNeedApproval, err
		}
		return expLog, isRealNeedApproval, err
	} else {
		expLog.State = 2
	}

	return expLog, isRealNeedApproval, nil
}

// BuildExpLog 通过实验的变更创建实验日志
func (s *ExpLogService) BuildExpLog(ctx context.Context, original, current *dto.ExpLog) (log *model.ExpLog, changeNodes []*model.ChangeNode, err error) {
	if original == nil {
		return log, nil, errors.New("original exp should not be nil")
	}
	if current == nil {
		return log, nil, errors.New("current exp should not be nil")
	}
	if current.Exp.ID == 0 {
		return log, nil, errors.New("current exp id is required")
	}

	changeFn := []ChangeListenr{
		// 实验天数的变更
		{LogText: s.expDaysChanger, ChangeNode: model.ExpDaysChange, GroupType: model.ChangeNodeGroupParamType},
		// 实验流量变更
		{LogText: s.expFlowChanger, ChangeNode: model.ExpLayerChange, GroupType: model.ChangeNodeGroupParamType},
		// 触发用户规则
		{LogText: s.expUserRulesChanger, ChangeNode: model.ExpTriggerUserChange, GroupType: model.ChangeNodeGroupStrategyType},
		// 停止进量触发规则
		{LogText: s.expWithoutFlowChanger, ChangeNode: model.ExpTriggerStopChange, GroupType: model.ChangeNodeGroupStrategyType},
		// 关闭触发规则
		{LogText: s.expCloseChanger, ChangeNode: model.ExpTriggerEndChange, GroupType: model.ChangeNodeGroupStrategyType},
		// 实验方案新建
		{LogText: s.expCreateGroupChanger, ChangeNode: model.ExpNewGroup, GroupType: model.ChangeNodeGroupNewGroupType},
		// 实验方案停止进量
		{LogText: s.expGroupWithoutFlowChanger, ChangeNode: model.ExpStopGroup, GroupType: model.ChangeNodeGroupStopGroupType},
		// 实验方案的关闭
		{LogText: s.expGroupCloseChanger, ChangeNode: model.ExpCloseGroup, GroupType: model.ChangeNodeGroupCloseGroupType},
		// 实验方案的重定向
		{LogText: s.expGroupRedirectChanger, ChangeNode: model.ExpRedirectGroup, GroupType: model.ChangeNodeGroupRedirectType},
		// 实验进组不出组设置
		{LogText: s.triggerOnceChanger, ChangeNode: model.ExpTriggerOnce, GroupType: model.ChangeNodeGroupParamType},
		// 实验小时流量设置
		{LogText: s.groupLimitHourChanger, ChangeNode: model.ExpGroupLimitHoursChange, GroupType: model.ChangeNodeGroupParamType},
	}

	if original.Exp.Owner != current.Exp.Owner {
		// 实验负责人变更，必须填充UserMap
		changeFn = append(changeFn, ChangeListenr{LogText: s.expOwnerChanger, ChangeNode: model.ExpOwnerChange, GroupType: model.ChangeNodeGroupParamType, NotifyText: DoNothing})

		originalOwners := castOwners(original.Exp.Owner)
		currentOwners := castOwners(current.Exp.Owner)

		allUser := append(originalOwners, currentOwners...)
		allUserMap, err := s.UserQuery.QueryUserByIds(ctx, allUser...)
		if err != nil {
			return log, nil, err
		}

		original.UserMap = make(map[int32]*model.User)
		for _, owner := range originalOwners {
			original.UserMap[owner] = allUserMap[owner]
		}
		current.UserMap = make(map[int32]*model.User)
		for _, owner := range currentOwners {
			current.UserMap[owner] = allUserMap[owner]
		}
	}

	snapshot, err := s.buildSnapshot(ctx, current)
	if err != nil {
		return log, nil, err
	}

	event := &ChangeEvent{
		ExpId:        current.Exp.ID,
		OriginalData: original,
		NewData:      current,
	}
	changes, changesNodes, notifyLines, err := handleChangeEvent(event, changeFn)
	if err != nil {
		return log, nil, err
	}
	log, err = s.buildExpLog(ctx, current.Exp, snapshot, changes, notifyLines)
	if err != nil {
		return log, nil, err
	}
	return log, changesNodes, err
}

// buildSnapshot 构建实验快照
func (s *ExpLogService) buildSnapshot(ctx context.Context, expDto *dto.ExpLog) ([]byte, error) {
	// 同一层的实验
	current := expDto.Exp
	expWithLayer, err := s.LayerService.QueryExpWithLayer(current.LayerID, current.ID)
	if err != nil {
		return nil, err
	}

	// 实验组（实验方案）
	groups, groupCount, err := s.castGroup(ctx, append(append(expDto.AddGroups, expDto.UpdateGroups...), expDto.UnchangedGroups...))
	if err != nil {
		return nil, err
	}

	batchName, err := s.getBatchName(current.BatchID)
	if err != nil {
		return nil, err
	}

	ownerName, err := s.getOwnerName(ctx, current.Owner)
	if err != nil {
		return nil, err
	}

	tagName, err := s.getTagName(current.TagID)
	if err != nil {
		return nil, err
	}

	layerName, err := s.getLayerName(current.LayerID)
	if err != nil {
		return nil, err
	}

	// 获取实验的总人数
	totalUser := int64(0)
	expUserMap, err := s.MetricService.GetExpUserCount(ctx, []int64{current.ID})
	if err != nil {
		if _, ok := expUserMap[current.ID]; ok {
			totalUser = expUserMap[current.ID]
		}
	}

	snapshotLog := dto.Snapshot{
		ExpInfo: dto.ExpInfo{
			Id:             current.ID,
			Name:           current.Name,
			Desc:           current.Desc,
			ExpType:        current.ExpType,
			ExpTypeName:    model.ExpTypeMap[int(current.ExpType)],
			Days:           current.Days,
			ExpRunTime:     calcRunTime(current.StartTime),
			BatchName:      batchName,
			BatchId:        current.BatchID,
			Owner:          current.Owner,
			OwnerName:      ownerName,
			TagName:        tagName,
			TagId:          current.TagID,
			LayerId:        current.LayerID,
			LayerName:      layerName,
			LayerRate:      current.LayerRate,
			StartTime:      current.StartTime,
			EndTime:        current.EndTime,
			State:          current.State,
			StateName:      model.ExpStatus(current.State).Name(),
			Strategy:       current.Strategy,
			Csr:            current.Csr,
			Version:        current.Version,
			UpdateTime:     current.UpdateTime,
			GroupLimitHour: current.GroupLimitHour,
		},
		ExpWithLayer:      expWithLayer,
		Groups:            groups,
		GroupCount:        groupCount,
		TotalUser:         totalUser,
		AddGroups:         expDto.AddGroups,
		UpdateGroups:      expDto.UpdateGroups,
		UnchangedGroups:   expDto.UnchangedGroups,
		ExpOperate:        expDto.ExpOperate,
		ApprovalStateNode: expDto.ApprovalStateNode,
		OriginExp:         expDto.OriginExp,
	}

	return json.Marshal(snapshotLog)
}

// calcRunTime 计算运行时长
func calcRunTime(startTime *time.Time) string {
	runTime := fmt.Sprintf("%d天%d小时", 0, 0)
	if startTime != nil {
		d := time.Since(*startTime)
		days := int(d / (24 * time.Hour))
		hours := int((d % (24 * time.Hour)) / time.Hour)
		runTime = fmt.Sprintf("%d天%d小时", days, hours)
	}
	return runTime
}

// getBatchName 获取批次名称
func (s *ExpLogService) getBatchName(batchID int64) (string, error) {
	if batchID == 0 {
		return "", nil
	}
	batchList, err := s.BatchService.QueryBatchByIds(batchID)
	if err != nil {
		return "", base.NewBizError(err)
	}
	if len(batchList) == 0 {
		return "", base.NewBizError(fmt.Errorf("%d批次不存在", batchID))
	}
	return batchList[0].Name, nil
}

// getOwnerName 获取实验拥有者的名称
func (s *ExpLogService) getOwnerName(ctx context.Context, owner string) (string, error) {
	if owner == "" {
		return "", nil
	}
	owners := castOwners(owner)
	var ownerName strings.Builder
	users, err := s.UserQuery.QueryUserByIds(ctx, owners...)
	if err != nil {
		return "", base.NewBizError(err)
	}
	for _, uId := range strings.Split(owner, ",") {
		if uId == "" {
			continue
		}
		if ownerName.Len() > 0 {
			ownerName.WriteString(",")
		}
		ownerName.WriteString(users[cast.ToInt32(uId)].Realname)
	}
	return ownerName.String(), nil
}

// getTagName 获取标签名称
func (s *ExpLogService) getTagName(tagID string) (string, error) {
	if tagID == "" {
		return "", nil
	}

	var tagName strings.Builder
	tagIds := strings.Split(tagID, ",")
	ids := make([]int64, 0, len(tagIds))
	for _, tagId := range tagIds {
		ids = append(ids, cast.ToInt64(tagId))
	}
	tags, err := s.TagService.QueryTagByIds(ids...)
	if err != nil {
		return "", base.NewBizError(err)
	}
	for i, tag := range tags {
		if i > 0 {
			tagName.WriteString(",")
		}
		tagName.WriteString(tag.Name)
	}
	return tagName.String(), nil
}

// getLayerName 获取流量层名称
func (s *ExpLogService) getLayerName(layerID int64) (string, error) {
	if layerID == 0 {
		return "", nil
	}
	layers, err := s.LayerService.QueryLayerByIds(layerID)
	if err != nil {
		return "", base.NewBizError(err)
	}
	if len(layers) == 0 {
		return "", base.NewBizError(fmt.Errorf("%d流量层不存在", layerID))
	}
	return layers[0].Name, nil
}

// handleChangeEvent 处理变更的时间，获取变更内容
func handleChangeEvent(event *ChangeEvent, listeners []ChangeListenr) (logtexts string, changes []*model.ChangeNode, notifyLines []Line, err error) {
	var changesBuf strings.Builder
	// 审批node修改
	changesNodes := make([]*model.ChangeNode, 0, len(listeners))
	// 调用所有订阅者的处理函数来处理事件
	for _, listener := range listeners {
		logtext := listener.LogText(event)
		if logtext == "" {
			continue
		}
		// 变更节点
		if listener.ChangeNode != "" {
			changesNodes = append(changesNodes, &model.ChangeNode{
				NodeType:  listener.ChangeNode,
				Change:    logtext,
				GroupType: listener.GroupType,
			})
		}
		// 操作日志
		changesBuf.WriteString(logtext)

		// 通知
		notifyLine := logtext
		if listener.NotifyText != nil {
			notifyLine = listener.NotifyText(event)
		}
		if notifyLine != "" {
			notifyLines = append(notifyLines, Line{Text: notifyLine})
		}
	}
	return changesBuf.String(), changesNodes, notifyLines, nil
}

// buildExpLog 构建实验日志
func (s *ExpLogService) buildExpLog(ctx context.Context, current *model.Exp, snapshot []byte, changes string, notifyLines []Line) (*model.ExpLog, error) {
	uid := int32(auth.GetUserId(ctx))
	var uName string
	if uid != 0 {
		users, err := s.UserQuery.QueryUserByIds(ctx, int32(auth.GetUserId(ctx)))
		if err != nil {
			return nil, err
		}
		if len(users) > 0 {
			if user, ok := users[uid]; ok && user != nil {
				uName = user.Realname
			}
		}
	}
	expSnapshot := string(snapshot)
	return &model.ExpLog{
		ExpID:       current.ID,
		ExpSnapshot: &expSnapshot,
		Changes:     changes,
		UID:         int64(uid),
		Uname:       uName,
		CreateTime:  time.Now(),
		Desc:        current.Desc,
		Notify:      model.NotifyMessage{Lines: notifyLines},
	}, nil
}

// castOwners owner字符串转用户id列表
func castOwners(owners string) []int32 {
	userIdList := strings.Split(owners, ",")
	ids := make([]int32, 0, len(userIdList))
	for _, uid := range userIdList {
		uid = strings.TrimSpace(uid)
		if uid == "" {
			continue
		}
		ids = append(ids, cast.ToInt32(uid))
	}
	return ids
}

// castGroup 实验组转换
func (s *ExpLogService) castGroup(ctx context.Context, groups []*model.Group) ([]*response.Group, int, error) {
	if len(groups) == 0 {
		return nil, 0, nil
	}
	// model.Group与response.Group的数据类型得统一下
	groupIds := make([]int64, 0, len(groups))
	batchIds := make([]int64, 0, len(groups))
	groupCount := 0
	groupResults := make([]*response.Group, 0, len(groups))
	for _, group := range groups {
		if model.ExpGroupStatus(group.State) == model.ExpGroupStatusRunning ||
			model.ExpGroupStatus(group.State) == model.ExpGroupStatusRunningWithoutFlow {
			groupCount++
		}

		groupIds = append(groupIds, group.ID)
		batchIds = append(batchIds, group.BatchID)

		_group := &response.Group{}
		if err := copier.Copy(_group, group); err != nil {
			return nil, 0, err
		}
		_group.GroupId = group.ID
		_group.GroupTypeName = model.GroupTypeMap[_group.GroupType]

		// 获取参数类型，数据结构设计如此，暂时单个获取，以后再优化为批量
		if group.ParamsType != 0 {
			paramsTypeDao := s.DS.ParamsType
			paramsType, err := paramsTypeDao.Where(paramsTypeDao.ID.Eq(int64(group.ParamsType))).First()
			if err != nil {
				return nil, 0, err
			}
			_group.ParamsName = paramsType.Name
		}

		groupResults = append(groupResults, _group)
	}

	// 获取进组数量
	groupsCountMap, err := s.MetricService.GetGrpUserCount(ctx, groupIds)
	if err != nil {
		return nil, 0, err
	}

	// 获取批次
	batchMap := make(map[int64]*model.Batch)
	if len(batchIds) > 0 {
		batchList, err := s.BatchService.QueryBatchByIds(batchIds...)
		if err != nil {
			return nil, 0, err
		}
		batchMap = lo.KeyBy(batchList, func(batch *model.Batch) int64 { return batch.ID })
	}

	for _, group := range groupResults {
		group.UserCount = groupsCountMap[group.GroupId]
		if batch := batchMap[int64(group.BatchId)]; batch != nil {
			group.BatchName = batch.Name
		}
	}
	return groupResults, groupCount, nil
}
