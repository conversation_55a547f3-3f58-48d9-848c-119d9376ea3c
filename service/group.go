package service

import (
	"context"
	"errors"
	"fmt"
	"git.7k7k.com/data/abAdmin/httpd/base"
	"git.7k7k.com/data/abAdmin/httpd/response"
	"gorm.io/gorm"
	"strings"
	"time"

	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/repository/dao"
)

// @autowire(set=service)
type GroupService struct {
	DS            *dao.Query
	MetricService *MetricService
}

func (s *GroupService) GetGroups(ctx context.Context, groupIds []int64) ([]*model.Group, error) {
	groupDao := s.DS.Group
	groups, err := groupDao.Debug().WithContext(ctx).Where(groupDao.ID.In(groupIds...)).Find()
	if err != nil {
		return nil, err
	}
	return groups, nil
}

// GroupStateTransition 定义状态转换规则
type GroupStateTransition struct {
	ValidSourceStates []model.ExpGroupStatus                           // 有效的源状态
	SkipStates        []model.ExpGroupStatus                           // 需要跳过的状态
	DetermineTarget   func(*model.Group) (model.ExpGroupStatus, error) // 确定目标状态的函数
}

// OpenGroup 开启实验组
func OpenGroup(groups []*model.Group) error {
	return updateGroupStates(groups, GroupStateTransition{
		ValidSourceStates: []model.ExpGroupStatus{
			model.ExpGroupStatusPending,
			model.ExpGroupStatusRunningWithoutFlow,
		},
		SkipStates: []model.ExpGroupStatus{
			model.ExpGroupStatusRunning,
			model.ExpGroupStatusClosed,
		},
		DetermineTarget: func(group *model.Group) (model.ExpGroupStatus, error) {
			switch group.IsProgress {
			case model.AllowFlow:
				return model.ExpGroupStatusRunning, nil
			case model.NotAllowFlow:
				return model.ExpGroupStatusRunningWithoutFlow, nil
			default:
				return 0, base.NewAlertTip(fmt.Sprintf("未知的是否进量值 %d", group.IsProgress))
			}
		},
	})
}

// OpenGroupWithoutFlow 开启不进量
func OpenGroupWithoutFlow(groups []*model.Group) error {
	return updateGroupStates(groups, GroupStateTransition{
		ValidSourceStates: []model.ExpGroupStatus{
			model.ExpGroupStatusPending,
		},
		SkipStates: nil,
		DetermineTarget: func(group *model.Group) (model.ExpGroupStatus, error) {
			return model.ExpGroupStatusRunningWithoutFlow, nil
		},
	})
}

// GroupRunningWithoutFlow 实验组停止进量
func GroupRunningWithoutFlow(groups []*model.Group) error {
	return updateGroupStates(groups, GroupStateTransition{
		ValidSourceStates: []model.ExpGroupStatus{
			model.ExpGroupStatusRunning,
		},
		SkipStates: []model.ExpGroupStatus{
			model.ExpGroupStatusRunningWithoutFlow,
			model.ExpGroupStatusClosed,
		},
		DetermineTarget: func(group *model.Group) (model.ExpGroupStatus, error) {
			return model.ExpGroupStatusRunningWithoutFlow, nil
		},
	})
}

// RestartGroup 重启实验组
func RestartGroup(groups []*model.Group) error {
	return updateGroupStates(groups, GroupStateTransition{
		ValidSourceStates: []model.ExpGroupStatus{
			model.ExpGroupStatusRunningWithoutFlow,
		},
		SkipStates: []model.ExpGroupStatus{
			model.ExpGroupStatusClosed,
		},
		DetermineTarget: func(group *model.Group) (model.ExpGroupStatus, error) {
			switch group.IsProgress {
			case model.AllowFlow:
				return model.ExpGroupStatusRunning, nil
			case model.NotAllowFlow:
				return model.ExpGroupStatusRunningWithoutFlow, nil
			default:
				return 0, base.NewAlertTip(fmt.Sprintf("未知的是否进量值 %d", group.IsProgress))
			}
		},
	})
}

// CloseGroup 关闭实验组
func CloseGroup(groups []*model.Group) error {
	return updateGroupStates(groups, GroupStateTransition{
		ValidSourceStates: []model.ExpGroupStatus{
			model.ExpGroupStatusRunningWithoutFlow,
			model.ExpGroupStatusRunning,
			model.ExpGroupStatusPending,
		},
		SkipStates: []model.ExpGroupStatus{
			model.ExpGroupStatusClosed,
		},
		DetermineTarget: func(group *model.Group) (model.ExpGroupStatus, error) {
			return model.ExpGroupStatusClosed, nil
		},
	})
}

// updateGroupStates 通用的状态更新处理函数
func updateGroupStates(groups []*model.Group, transition GroupStateTransition) error {
	if len(groups) == 0 {
		return nil
	}

	now := time.Now()
	for index, group := range groups {
		currentStatus := model.ExpGroupStatus(group.State)

		// 检查是否需要跳过
		if containsStatus(transition.SkipStates, currentStatus) {
			continue
		}

		// 验证源状态是否合法
		if !containsStatus(transition.ValidSourceStates, currentStatus) {
			return base.NewAlertTip(formatStateError(transition.ValidSourceStates, currentStatus))
		}

		// 确定目标状态
		targetStatus, err := transition.DetermineTarget(group)
		if err != nil {
			return err
		}

		if group.ID == 0 {
			return base.NewAlertTip("方案号为0不正确")
		}
		// 只做方案状态变更，不执行数据库操作
		groups[index].State = int32(targetStatus)
		groups[index].UpdateTime = now

		//updateGroup := &model.Group{
		//	ID:         group.ID,
		//	State:      int32(targetStatus),
		//	UpdateTime: now,
		//}
		//info, err := tx.Group.Where(tx.Group.ID.Eq(group.ID)).Updates(updateGroup)
		//if err != nil {
		//	return err
		//}
		//if int(info.RowsAffected) != 1 {
		//	return base.NewAlertTip(fmt.Sprintf("更新的数据行应该为1, 但是更新了%d行", info.RowsAffected))
		//}
	}

	return nil
}

func UpdateGroup(tx *dao.Query, groups []*model.Group) error {
	for _, group := range groups {
		_, err := tx.Group.Where(tx.Group.ID.Eq(group.ID)).Updates(group)
		if err != nil {
			return err
		}
	}
	return nil
}

// containsStatus 辅助函数，状态是否可扭转
func containsStatus(statuses []model.ExpGroupStatus, target model.ExpGroupStatus) bool {
	for _, s := range statuses {
		if s == target {
			return true
		}
	}
	return false
}

// formatStateError 格式化状态码错误
func formatStateError(validStates []model.ExpGroupStatus, current model.ExpGroupStatus) string {
	var validNames []string
	for _, s := range validStates {
		validNames = append(validNames, s.Name())
	}
	return fmt.Sprintf("方案的状态不正确，应该为[%s]，而不是[%s]",
		strings.Join(validNames, "/"),
		current.Name())
}

// QueryGroup 查询实验下的实验组（方案）
func (s *GroupService) QueryGroup(ctx context.Context, expId int64) ([]*response.Group, int, error) {
	if expId == 0 {
		return nil, 0, base.NewAlertTip("实验ID是必填的")
	}
	groupResult := make([]*response.Group, 0)
	groupDao := s.DS.Group
	batchDao := s.DS.Batch
	paramsTypeDao := s.DS.ParamsType
	err := groupDao.Select(groupDao.ID.As("group_id"), groupDao.Name, batchDao.Name.As("batch_name"),
		groupDao.BatchID, groupDao.GroupType, groupDao.Desc, paramsTypeDao.Name.As("params_name"),
		groupDao.ParamsContent, groupDao.IsProgress, groupDao.State, groupDao.IsDeleted).
		LeftJoin(batchDao, groupDao.BatchID.EqCol(batchDao.ID)).
		Where(groupDao.ExpID.Eq(expId), groupDao.IsDeleted.Eq(model.NotDeleted)).
		LeftJoin(paramsTypeDao, groupDao.ParamsType.EqCol(paramsTypeDao.ID)).Scan(&groupResult)
	if err != nil {
		return nil, 0, err
	}

	groupCount := 0
	groupIds := make([]int64, 0, len(groupResult))
	for _, group := range groupResult {
		if model.ExpGroupStatus(group.State) == model.ExpGroupStatusRunning ||
			model.ExpGroupStatus(group.State) == model.ExpGroupStatusRunningWithoutFlow {
			groupCount++
		}
		group.GroupTypeName = model.GroupTypeMap[group.GroupType]
		groupIds = append(groupIds, group.GroupId)
	}
	groupsCountMap, err := s.MetricService.GetGrpUserCount(ctx, groupIds)
	for _, group := range groupResult {
		group.UserCount = groupsCountMap[group.GroupId]
	}

	return groupResult, groupCount, nil
}

// QueryGroupByExpId 通过实验ID获取实验组详情
func (s *GroupService) QueryGroupByExpId(ctx context.Context, expId int64) ([]*model.Group, error) {
	if expId == 0 {
		return nil, base.NewAlertTip("实验ID是必填的")
	}
	groupDao := s.DS.Group
	list, err := groupDao.WithContext(ctx).Where(groupDao.ExpID.Eq(expId), groupDao.IsDeleted.Eq(model.NotDeleted)).Find()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

// QueryGroupByExpIds 通过实验IDs获取实验组详情
func (s *GroupService) QueryGroupByExpIds(ctx context.Context, expIds []int64) ([]*model.Group, error) {
	if len(expIds) == 0 {
		return nil, base.NewAlertTip("实验ID是必填的")
	}
	groupDao := s.DS.Group
	list, err := groupDao.WithContext(ctx).Where(groupDao.ExpID.In(expIds...), groupDao.IsDeleted.Eq(model.NotDeleted)).Find()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}
