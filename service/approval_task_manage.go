package service

import (
	"context"
	"encoding/json"
	"fmt"
	"git.7k7k.com/data/abAdmin/httpd/request"
	"git.7k7k.com/data/abAdmin/httpd/response"
	"git.7k7k.com/data/abAdmin/infra/auth"
	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/model/dto"
	"git.7k7k.com/data/abAdmin/repository"
	"git.7k7k.com/data/abAdmin/repository/dao"
	"github.com/cockroachdb/errors"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"gorm.io/datatypes"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
	"math"
	"strings"
	"time"
)

// @autowire(set=service)
type ApprovalTaskManageService struct {
	DS *dao.Query
	Query
	QueryForBIv1 *repository.QueryForBIv1
	Events       *Events
	LayerService *LayerService
}
type ApprovalTaskIterm struct {
	model.ApprovalTask
	ExpName          string `json:"exp_name"`
	StateName        string `json:"state_name"`
	Creator          string `json:"creator"`
	ApproversName    string `json:"approvers_name"`
	ApprovalRuleName string `json:"approval_rule_name"`
	StepCount        int    `json:"step_count"`
	IsEffected       bool   `json:"is_effected"`
}

func (e *ApprovalTaskManageService) IsExpApprovalRunning(ctx context.Context, expId int64) (bool, error) {
	// 获取实验状态
	approvalTaskDao := e.DS.ApprovalTask
	approvalTaskList, err := approvalTaskDao.WithContext(ctx).
		Where(approvalTaskDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Where(approvalTaskDao.ExpID.Eq(expId)).
		Where(approvalTaskDao.State.Eq(model.ApprovalInit)).
		Find()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return false, err
	}

	if len(approvalTaskList) == 0 {
		return false, nil
	} else {
		return true, nil
	}
}

// 待审批、已审批、全部审批
func (a *ApprovalTaskManageService) List(ctx context.Context, req request.ApprovalTaskListReq) (ret *response.List, err error) {
	approvalTaskDao := a.DS.ApprovalTask
	expDao := a.DS.Exp
	userDao := a.QueryForBIv1.User
	wheres := make([]gen.Condition, 0, 10)
	var order field.Expr
	var subQuery dao.IApprovalTaskDo
	if req.Search != "" {
		// 审批创建人搜索
		likeClause := fmt.Sprintf("%%%s%%", req.Search)
		users, err := userDao.WithContext(ctx).Where(userDao.Realname.Like(likeClause)).Debug().Select(userDao.ID, userDao.Realname).Find()
		if err != nil && !errors.Is(gorm.ErrRecordNotFound, err) {
			return nil, err
		}
		if len(users) > 0 {
			userIds := lo.Map(users, func(item *model.User, index int) int32 {
				return item.ID
			})
			subQuery = approvalTaskDao.Where(approvalTaskDao.CreatorID.In(userIds...))
		}
		// 实验名字搜索
		expList, err := expDao.WithContext(ctx).
			Where(expDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
			Where(expDao.Name.Like(likeClause)).Find()
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
		expIds := lo.Map(expList, func(item *model.Exp, index int) int64 {
			return item.ID
		})
		if subQuery == nil {
			subQuery = approvalTaskDao.Where(approvalTaskDao.ExpID.In(expIds...))
		} else {
			subQuery = subQuery.Or(approvalTaskDao.Where(approvalTaskDao.ExpID.In(expIds...)))
		}
		wheres = append(wheres, subQuery)
	}
	if len(req.State) > 0 {
		wheres = append(wheres, approvalTaskDao.State.In(req.State...))
	}
	preload := make([]field.RelationField, 0, 10)
	preload = append(preload, approvalTaskDao.Exp, approvalTaskDao.ExpLog)
	// 是否有效
	if len(req.IsEffected) > 0 {
		wheres = append(wheres, approvalTaskDao.IsEffected.In(req.IsEffected...))
	}
	// 我的申请
	if req.ListType == 1 {
		preload = append(preload, approvalTaskDao.ApprovalRule)
		wheres = append(wheres, approvalTaskDao.CreatorID.Eq(int32(auth.GetUserId(ctx))))
		order = approvalTaskDao.ID.Desc()
	}
	// 待审批
	if req.ListType == 2 {
		// 超管 可以查看，当前项目下，所有审批
		if auth.IsAdmin(ctx) {
			preload = append(preload, approvalTaskDao.ApprovalRule)
		} else {
			subWhere := datatypes.JSONArrayQuery("approvers")
			subWhere.Contains(auth.GetUserId(ctx))
			wheres = append(wheres, gen.Cond(subWhere)...)
			preload = append(preload, approvalTaskDao.ApprovalRule.Clauses(subWhere))
		}
		wheres = append(wheres, approvalTaskDao.State.Eq(model.ApprovalInit))
		order = approvalTaskDao.ID.Asc()
	}

	// 已审批
	if req.ListType == 3 {
		subWhere := datatypes.JSONArrayQuery("flow_approvers")
		subWhere.Contains(auth.GetUserId(ctx))
		wheres = append(wheres, gen.Cond(subWhere)...)
		preload = append(preload, approvalTaskDao.ApprovalRule)
		order = approvalTaskDao.ID.Desc()
	}
	// 全部审批
	if req.ListType == 4 {
		preload = append(preload, approvalTaskDao.ApprovalRule)
		order = approvalTaskDao.ID.Desc()
	}
	wheres = append(wheres, approvalTaskDao.ProjectID.Eq(auth.GetProjectId64(ctx)))
	result, err := approvalTaskDao.
		WithContext(ctx).
		Preload(preload...).
		Where(wheres...).
		Order(order).
		Scopes(Paginate(req.Page, req.PageSize)).
		Find()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	// 申请人
	uids := lo.Map(result, func(item *model.ApprovalTask, index int) int32 {
		return item.CreatorID
	})
	// 审批人
	for _, item := range result {
		for _, uid := range item.Approvers {
			uids = append(uids, uid)
		}
	}
	uids = lo.Uniq(uids)
	userMap, err := a.QueryForBIv1.QueryUserByIds(ctx, uids...)
	if err != nil {
		return nil, err
	}

	list := make([]*ApprovalTaskIterm, 0, len(result))
	for _, task := range result {
		newTask := ApprovalTaskIterm{
			ApprovalTask: *task,
		}
		if user, ok := userMap[int32(task.CreatorID)]; ok {
			newTask.Creator = user.Realname
		}
		if len(task.Approvers) > 0 && task.State == model.ApprovalInit {
			usernames := make([]string, 0, len(task.Approvers))
			for _, uid := range task.Approvers {
				if user, ok := userMap[uid]; ok {
					usernames = append(usernames, user.Realname)
				}
			}
			newTask.ApproversName = strings.Join(usernames, ",")
		}
		newTask.ExpName = task.Exp.Name
		newTask.StateName = model.ApprovalStateMap[task.State]
		newTask.ApprovalRuleName = task.ApprovalRule.Name
		newTask.StepCount = len(task.ApprovalRule.Approvers)
		newTask.IsEffected = cast.ToBool(task.ExpLog.Show)
		list = append(list, &newTask)
	}
	total, err := approvalTaskDao.
		WithContext(ctx).
		Where(wheres...).
		Count()
	if err != nil {
		return nil, err
	}
	res := &response.List{
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    total,
		List:     list,
	}
	return res, nil
}
func (e *ApprovalTaskManageService) Changes(ctx context.Context, taskId int64) (*model.Changes, error) {
	// 获取审批任务
	approvalTaskDao := e.DS.ApprovalTask
	approvalTask, err := approvalTaskDao.WithContext(ctx).Where(approvalTaskDao.ID.Eq(taskId)).Take()
	if err != nil {
		return nil, err
	}
	// 获取审批改变
	return approvalTask.Changes, nil
}

type ApprovalTaskRes struct {
	*model.ApprovalTask
	Tips string `json:"tips"`
}

// 审批
func (a *ApprovalTaskManageService) Approval(ctx context.Context, req request.ApprovalReq) (approvalTaskRes *ApprovalTaskRes, err error) {
	// 获取审批任务
	approvalTaskDao := a.DS.ApprovalTask
	approvalTask, err := approvalTaskDao.
		WithContext(ctx).
		Preload(approvalTaskDao.ApprovalRule, approvalTaskDao.Exp, approvalTaskDao.ExpLog).
		Where(approvalTaskDao.ID.Eq(req.Id)).Take()
	if err != nil {
		return nil, errors.Wrap(err, "获取审批任务失败")
	}
	// 获取实验快照
	explogDao := a.DS.ExpLog
	expLog, err := explogDao.WithContext(ctx).Where(explogDao.ID.Eq(approvalTask.AfterSnapshotID)).Take()
	if err != nil {
		return nil, errors.Wrap(err, "获取实验快照失败")
	}
	// 解析快照数据
	snapshot := &dto.Snapshot{}
	if err = json.Unmarshal([]byte(*expLog.ExpSnapshot), snapshot); err != nil {
		return nil, errors.Wrap(err, "解析快照数据失败")
	}
	// 处理实验数据
	exp := &model.Exp{}
	err = copier.Copy(exp, snapshot.ExpInfo)
	if err != nil {
		return nil, err
	}
	// 更新审批任务状态
	err, isUpdateExpAction, tips := a.updateApprovalTaskState(ctx, approvalTask, req)
	if err != nil {
		return nil, err
	}
	// 执行事务操作
	if err = a.executeApprovalTransaction(ctx, approvalTask, snapshot, isUpdateExpAction); err != nil {
		return nil, err
	}
	res := &ApprovalTaskRes{
		ApprovalTask: approvalTask,
		Tips:         tips,
	}
	return res, nil
}
func (a *ApprovalTaskManageService) updateApprovalTaskState(ctx context.Context, task *model.ApprovalTask, req request.ApprovalReq) (error, bool, string) {
	// 审批操作转化审批状态
	var err error
	var isUpdateExpAction bool
	var tips string
	task.State, err = ApprovalActionToState(task, req.State)
	if err != nil {
		return err, isUpdateExpAction, tips
	}
	// 判断当前审批步数是否有效
	if req.Step < task.Step {
		return errors.New("当前审批阶段，已被其他审批人审批"), isUpdateExpAction, tips
	}
	var flowApprovers []int32
	flowApprovers = task.Approvers
	if auth.IsAdmin(ctx) {
		flowApprovers = append(flowApprovers, auth.GetUserIdInt32(ctx))
	}
	// 记录历史审批人
	task.FlowApprovers = lo.Uniq(append(task.FlowApprovers, flowApprovers...))
	// 计算下一个审批人
	task.Step += 1
	if task.State == model.ApprovalCompleted {
		task.Approvers = []int32{}
	} else if task.State == model.ApprovalInit {
		if task.Step <= int32(len(task.ApprovalRule.Approvers)-1) {
			task.Approvers = task.ApprovalRule.Approvers[task.Step]
		}
	}
	//流量校验 仅在审批通过最后一步
	var comment string
	if task.State == model.ApprovalCompleted {
		expLog := &dto.Snapshot{}
		err = json.Unmarshal([]byte(*task.ExpLog.ExpSnapshot), expLog)
		if err != nil {
			return err, isUpdateExpAction, tips
		}
		err = a.LayerService.AvailableRate(expLog.ExpInfo.LayerId, task.ExpID, expLog.ExpInfo.LayerRate)
		if err != nil {
			comment = req.Comment + "：" + err.Error()
			tips = err.Error()
			task.ExpLog.Show = 0
			task.ExpLog.State = 3
		} else {
			comment = req.Comment
			task.ExpLog.Show = 1
			task.ExpLog.State = 2
			isUpdateExpAction = true
			task.IsEffected = 1
		}
	} else {
		task.ExpLog.Show = 0
		comment = req.Comment
	}

	// 记录审批日志
	now := time.Now()
	task.UpdatedAt = &now
	task.Log = append(task.Log, &model.ApprovalLog{
		UserId:    int32(auth.GetUserId(ctx)),
		State:     req.State,
		ApproveAt: &now,
		Comment:   comment,
	})
	return nil, isUpdateExpAction, tips
}

func (a *ApprovalTaskManageService) executeApprovalTransaction(ctx context.Context, task *model.ApprovalTask, snapshot *dto.Snapshot, isUpdateExpAction bool) error {
	return a.DS.Transaction(func(tx *dao.Query) error {
		// 更新审批任务
		if _, err := tx.WithContext(ctx).ApprovalTask.Where(a.DS.ApprovalTask.ID.Eq(task.ID)).Updates(task); err != nil {
			return errors.Wrap(err, "更新审批任务失败")
		}

		// 审批中，不更新实验日志状态，和实验信息，继续下一层审批
		if task.State == model.ApprovalInit {
			return nil
		} else if task.State == model.ApprovalClose {
			// 审批关闭，更新实验日志状态，日志快照失效，1: 未生效 2: 已生效（发布） 3: 已失效
			if _, err := tx.WithContext(ctx).ExpLog.Where(tx.ExpLog.ID.Eq(task.AfterSnapshotID)).Updates(map[string]any{"state": 3, "show": task.ExpLog.Show}); err != nil {
				return errors.Wrap(err, "更新实验日志状态失败")
			}
			return nil
		} else if task.State == model.ApprovalCompleted {
			// 审批完成，继续往下执行，更新实验日志状态，日志快照生效，1: 未生效 2: 已生效（发布） 3: 已失效
			if _, err := tx.WithContext(ctx).ExpLog.Where(tx.ExpLog.ID.Eq(task.AfterSnapshotID)).Updates(map[string]any{"state": task.ExpLog.State, "show": task.ExpLog.Show, "create_time": time.Now()}); err != nil {
				return errors.Wrap(err, "更新实验日志状态失败")
			}
		} else {
			// 目前审批状态，只有审批中，审批完成，审批关闭
			return errors.New("审批状态,不合法")
		}
		// 执行实验变更
		if !isUpdateExpAction {
			return nil
		}
		// 获取当前实验信息
		currentExp, err := tx.Exp.WithContext(ctx).Where(a.DS.Exp.ID.Eq(task.ExpID)).Take()
		if err != nil {
			return err
		}

		// 获取快照实验信息，更新实验信息
		addGroups := snapshot.AddGroups
		updateGroups := snapshot.UpdateGroups
		splitGroup := snapshot.SplitGroup
		originExp := snapshot.OriginExp
		newExp := &model.Exp{}
		err = copier.Copy(newExp, snapshot.ExpInfo)
		if err != nil {
			return err
		}
		// 查看当前实验状态是否相同
		if originExp.State != currentExp.State {
			return fmt.Errorf("审批前实验状态和当前实验状态不一样，已被自动处理，请从新发起审批")
		}
		// 审批完成，更新实验信息
		if task.ApprovalRule.RuleType == model.ExpMsgChange {
			if err = UpdateRunningExp(ctx, tx, originExp, newExp, addGroups, updateGroups, splitGroup); err != nil {
				return err
			}
		} else if task.ApprovalRule.RuleType == model.ExpStateChange {
			var groups []*model.Group
			for _, group := range snapshot.Groups {
				newGroup := &model.Group{}
				err = copier.Copy(newGroup, group)
				if err != nil {
					return err
				}
				newGroup.ID = group.GroupId
				newGroup.ExpID = task.ExpID
				groups = append(groups, newGroup)
			}

			// 根据审批节点，执行策略
			var expStateStrategy ExpStateChangeStrategy
			switch snapshot.ApprovalStateNode {
			case model.ExpClose:
				expStateStrategy = &ClosedStrategy{}
			case model.ExpOpen:
				if snapshot.ExpOperate == string(expOpenOpt) {
					expStateStrategy = &OpenStrategy{
						BeforeStatus: model.ExpStatus(originExp.State),
					}
				} else if snapshot.ExpOperate == string(expOpenWithoutFlowOpt) {
					expStateStrategy = &OpenWithoutFlowStrategy{}
				} else if snapshot.ExpOperate == string(expOpenGrayOpt) {
					expStateStrategy = &OpenGrayStrategy{}
				}
			case model.ExpRestart:
				expStateStrategy = &RestartStrategy{}
			case model.ExpStop:
				expStateStrategy = &WithoutFlowStrategy{}
			}
			if expStateStrategy == nil {
				return fmt.Errorf("不支持的实验状态节点")
			}

			// 更新实验状态&方案状态
			err = StrategyToChangeExpState(ctx, tx, newExp, model.ExpState(originExp.State), groups, expStateStrategy)
			if err != nil {
				return err
			}
		}

		// 触发事件
		_ = a.Events.ExpUpdated.Push(ctx, ExpUpdatedEvent{ExpID: task.ExpID})
		return nil
	})
}
func UpdateRunningExp(ctx context.Context, tx *dao.Query, originExp *model.Exp, newExp *model.Exp, addGroups []*model.Group, updateGroups []*model.Group, splitGroup []*model.SplitGroup) error {
	// 更新实验基本信息
	expDao := tx.Exp
	if _, err := tx.WithContext(ctx).Exp.
		Select(expDao.Days, expDao.LayerRate, expDao.State, expDao.GroupLimitHour, expDao.Strategy, expDao.Owner, expDao.Version, expDao.EndTime, expDao.UpdateTime).
		Where(expDao.ID.Eq(newExp.ID)).Updates(newExp); err != nil {
		return errors.Wrap(err, "更新实验信息失败")
	}

	// 处理新增组
	if len(addGroups) > 0 {
		if err := tx.WithContext(ctx).Group.Debug().Create(addGroups...); err != nil {
			return errors.Wrap(err, "创建新增组失败")
		}
	}
	groupDao := tx.Group
	// 处理更新组
	if len(updateGroups) > 0 {
		for _, uGroup := range updateGroups {
			if _, err := tx.WithContext(ctx).Group.Debug().Where(groupDao.ID.Eq(uGroup.ID)).Where(groupDao.ExpID.Eq(uGroup.ExpID)).Updates(uGroup); err != nil {
				return errors.Wrap(err, "更新组信息失败")
			}
		}
	}
	// 处理已有分流方案
	splitGroupTypes := lo.Map(splitGroup, func(item *model.SplitGroup, index int) int32 {
		return item.SplitType
	})
	if splitGroup != nil && lo.Contains(splitGroupTypes, model.SplitGroupTypeRunningRedirect) {
		splitGroupDao := tx.SplitGroup
		_, err := tx.WithContext(ctx).SplitGroup.
			Where(splitGroupDao.FromExpID.Eq(newExp.ID), splitGroupDao.SplitType.Eq(model.SplitGroupTypeRunningRedirect)).
			Updates(map[string]any{"state": 2})
		if err != nil {
			return err
		}
	}
	// 处理拆分组
	if len(splitGroup) > 0 {
		if err := tx.WithContext(ctx).SplitGroup.Debug().Create(splitGroup...); err != nil {
			return errors.Wrap(err, "创建分流方案失败")
		}
	}
	// 实验天数有变更，需要同步变更实验任务
	if originExp.Days != newExp.Days {
		runningStateMap := map[model.ExpStatus]bool{
			model.ExpStatusRunning:     true,
			model.ExpStatusGrayRunning: true,
		}
		// 运行中编辑模式，需要同步变更任务的时间
		if runningStateMap[model.ExpStatus(originExp.State)] {
			diffDays := cast.ToInt(math.Abs(float64(newExp.Days - originExp.Days)))
			err := UpdateTaskOnExpDayChange(tx, originExp.ID, diffDays)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

// 撤销
func (a *ApprovalTaskManageService) Revoke(ctx context.Context, id int64) (*model.ApprovalTask, error) {
	approvalTaskDao := a.DS.ApprovalTask
	approvalTask, err := approvalTaskDao.
		WithContext(ctx).
		Where(approvalTaskDao.ID.Eq(id)).
		Take()
	if err != nil {
		return nil, err
	}
	// 申请人，撤销校验
	if approvalTask.CreatorID != auth.GetUserIdInt32(ctx) {
		return nil, errors.New("审批申请，不能本人，不能撤销")
	}
	// 审批状态校验
	state, err := ApprovalActionToState(approvalTask, model.LogStateRevoke)
	if approvalTask.State != model.ApprovalInit {
		return nil, errors.New("审批已经完成，不能撤销")
	}
	now := time.Now()
	approvalTask.State = state
	approvalTask.UpdatedAt = &now
	approvalTask.Log = append(approvalTask.Log, &model.ApprovalLog{
		UserId:    int32(auth.GetUserId(ctx)),
		State:     model.LogStateRevoke,
		ApproveAt: &now,
		Comment:   "",
	})
	_, err = approvalTaskDao.WithContext(ctx).Where(approvalTaskDao.ID.Eq(id)).Updates(approvalTask)
	if err != nil {
		return nil, err
	}
	expLogDao := a.DS.ExpLog
	_, err = expLogDao.WithContext(ctx).Where(expLogDao.ID.Eq(approvalTask.AfterSnapshotID)).Update(expLogDao.State, 3)
	if err != nil {
		return nil, err
	}
	return approvalTask, nil
}
func ApprovalActionToState(approvalTask *model.ApprovalTask, action int32) (state int32, err error) {
	if lo.Contains([]int32{model.ApprovalClose, model.ApprovalCompleted}, approvalTask.State) {
		return approvalTask.State, errors.New("审批任务已被其他人审批，请从新刷新")
	}
	state = model.ApprovalInit
	switch action {
	case model.LogStateApproval:
		if approvalTask.Step < int32(len(approvalTask.ApprovalRule.Approvers)-1) {
			state = model.ApprovalInit
		} else {
			state = model.ApprovalCompleted
		}
	case model.LogStateReject:
		state = model.ApprovalClose
	case model.LogStateRevoke:
		state = model.ApprovalClose
	}

	return state, nil
}

// 创建审批任务
func (a *ApprovalTaskManageService) CreateApprovalTask(ctx context.Context, approvalRuleType int32, taskData *model.ApprovalData) (task *model.ApprovalTask, err error) {
	// 获取审批规则
	approvalRuleDao := a.DS.ApprovalRule
	approvalRule, err := approvalRuleDao.
		WithContext(ctx).
		Where(approvalRuleDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Where(approvalRuleDao.RuleType.Eq(approvalRuleType)).
		Where(approvalRuleDao.State.Eq(model.ApprovalRuleStateOnline)).
		Take()
	if err != nil {
		return nil, err
	}
	now := time.Now()
	task = &model.ApprovalTask{
		ProjectID:        auth.GetProjectId64(ctx),
		RuleID:           approvalRule.ID,
		ExpID:            taskData.ExpId,
		BeforeSnapshotID: taskData.BeforeId,
		AfterSnapshotID:  taskData.AfterId,
		Step:             0,
		CreatorID:        int32(auth.GetUserId(ctx)),
		State:            model.ApprovalInit,
		Changes:          taskData.Changes,
		Desc:             taskData.Desc,
		CreatedAt:        &now,
		UpdatedAt:        &now,
	}
	log := &model.ApprovalLog{
		UserId:    int32(auth.GetUserId(ctx)),
		State:     model.LogStateInit,
		ApproveAt: &now,
	}
	task.Log = append(task.Log, log)
	// 审批人
	approvers := approvalRule.Approvers[int(task.Step)]
	if len(approvers) == 0 {
		return nil, errors.New("没有审批人")
	}
	task.Approvers = approvers
	// 创建审批任务
	err = a.DS.ApprovalTask.WithContext(ctx).Create(task)
	if err != nil {
		return nil, err
	}
	return task, nil
}

type Log struct {
	ApprovalLog   *model.ApprovalLog `json:"approval_log"`
	ApproversName string             `json:"approvers_name"`
	StateName     string             `json:"state_name"`
}

func (a *ApprovalTaskManageService) ApprovalLog(ctx context.Context, id int64) (res []*Log, err error) {
	approvalTaskDao := a.DS.ApprovalTask
	approvalTask, err := approvalTaskDao.
		WithContext(ctx).
		Where(approvalTaskDao.ID.Eq(id)).
		Take()
	if err != nil {
		return nil, err
	}
	// 审批人
	uids := lo.Map(approvalTask.Log, func(item *model.ApprovalLog, index int) int32 {
		return item.UserId
	})
	userMap, err := a.QueryForBIv1.QueryUserByIds(ctx, uids...)
	if err != nil {
		return nil, err
	}
	res = make([]*Log, 0, len(approvalTask.Log))
	for _, item := range approvalTask.Log {
		if user, ok := userMap[item.UserId]; ok {
			LogStateName := model.LogStateMap[item.State]
			res = append(res, &Log{
				ApprovalLog:   item,
				ApproversName: user.Realname,
				StateName:     LogStateName,
			})
		}
	}
	return res, nil
}
