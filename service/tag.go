package service

import (
	"git.7k7k.com/data/abAdmin/infra/auth"
	"gorm.io/gorm"
	"time"

	"git.7k7k.com/data/abAdmin/httpd/base"
	"github.com/cockroachdb/errors"
	"github.com/spf13/cast"

	"git.7k7k.com/data/abAdmin/httpd/request"
	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/repository/dao"
	"github.com/gin-gonic/gin"
)

// Query 查询服务
// @autowire(set=service)
type TagService struct {
	DS *dao.Query
	Query
}

func (s *TagService) Add(req request.AddTagReq, ctx *gin.Context) error {
	tagDao := s.DS.Tag
	take, err := tagDao.
		Where(tagDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Where(tagDao.IsDeleted.Eq(model.NotDeleted)).
		Where(tagDao.Name.Eq(req.Name)).
		Take()
	if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
		return err
	}
	if take != nil {
		return base.NewBizError(errors.New("标签,已经存在"))
	}
	tag := &model.Tag{}
	tag.Name = req.Name
	tag.ProjectID = auth.GetProjectId64(ctx)
	tag.CreateTime = time.Now()
	tag.UpdateTime = time.Now()
	err = s.DS.Tag.Create(tag)
	return err
}

func (s *TagService) List(req request.ListTagReq, ctx *gin.Context) (any, int64, error) {
	tag := s.DS.Tag
	list, count, err := tag.WithContext(ctx).
		Where(tag.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Where(tag.IsDeleted.Eq(model.NotDeleted)).
		Where(tag.Name.Like("%" + req.Search + "%")).
		Order(tag.CreateTime.Desc()).
		FindByPage(s.CalcOffsetLimit(req.Page, req.Size))
	return list, count, err
}

func (s *TagService) Update(req request.UpdateTagReq, ctx *gin.Context) (int64, error) {
	tagDao := s.DS.Tag
	_, err := s.DS.Tag.
		Where(tagDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Where(tagDao.IsDeleted.Eq(model.NotDeleted)).
		Where(tagDao.ID.Eq(req.Id)).
		Take()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, base.NewBizError(errors.New("标签信息,不合法"))
		}
		return 0, err
	}
	take, err := tagDao.
		Where(tagDao.IsDeleted.Eq(model.NotDeleted)).
		Where(tagDao.ID.Neq(req.Id)).
		Where(tagDao.Name.Eq(req.Name)).
		Take()
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return 0, err
	}
	if take != nil {
		return 0, errors.New("标签,已经存在")
	}
	tag := &model.Tag{}
	tag.UpdateTime = time.Now()
	tag.Name = req.Name
	info, err := s.DS.WithContext(ctx).Tag.Where(tagDao.ID.Eq(req.Id)).Updates(tag)
	return info.RowsAffected, err
}
func (s *TagService) Delete(id int64, ctx *gin.Context) (int64, error) {
	expDao := s.DS.Exp
	tagDao := s.DS.Tag
	tagIdStr := cast.ToString(id)
	count, err := expDao.
		Where(expDao.State.Neq(model.ExpStateClose)).
		Where(expDao.IsDeleted.Eq(model.NotDeleted)).
		Where(expDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Where(expDao.TagID.FindInSetWith(tagIdStr)).
		Count()
	if count > 0 || err != nil {
		return 0, base.NewBizError(errors.New("该标签下存在实验，不允许删除"))
	}
	tag := &model.Tag{}
	tag.UpdateTime = time.Now()
	tag.IsDeleted = 1
	info, err := tagDao.WithContext(ctx).
		Where(tagDao.ID.Eq(id)).
		Where(tagDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Updates(tag)
	return info.RowsAffected, err
}

// QueryTagByIds 通过ID获取标签数据
func (s *TagService) QueryTagByIds(ids ...int64) ([]*model.Tag, error) {
	if len(ids) == 0 {
		return nil, base.NewAlertTip("ids是必填的")
	}
	tag := s.DS.Tag
	data, err := tag.Where(tag.IsDeleted.Eq(model.NotDeleted), tag.ID.In(ids...)).Find()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return data, err
}
