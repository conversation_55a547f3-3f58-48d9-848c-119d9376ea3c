package oapi

import (
	"context"

	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/service"
)

// @autowire(set=service)
type ExpService struct {
	ExpService *service.ExpService
}

type ExpListIn struct {
	ProjectId int `json:"project_id" form:"project_id"`
	LayerId   int `json:"layer_id" form:"layer_id"`
}
type ExpListOut struct {
	ExpList []*Exp `json:"exp_list"`
}

func (s *ExpService) List(ctx context.Context, req *ExpListIn) (resp *ExpListOut, err error) {
	resp = &ExpListOut{
		ExpList: make([]*Exp, 0),
	}

	expDao := s.ExpService.DS.Exp.UnderlyingDB().WithContext(ctx)
	query := expDao.Preload("Groups").Limit(10)
	query = query.Where("state not in (?)", []int{model.ExpStateDraft, model.ExpStateClose})
	if req.ProjectId > 0 {
		query = query.Where("project_id = ?", req.ProjectId)
	}
	if req.LayerId > 0 {
		query = query.Where("layer_id = ?", req.LayerId)
	}
	exps := make([]*model.Exp, 0)
	err = query.Find(&exps).Error
	if err != nil {
		return
	}

	for _, exp := range exps {
		expItem := &Exp{
			Id:      int(exp.ID),
			Name:    exp.Name,
			LayerId: int(exp.LayerID),
			State:   exp.State,
		}
		resp.ExpList = append(resp.ExpList, expItem)

		for _, group := range exp.Groups {
			if model.IsClosed(group.State) {
				continue
			}

			groupItem := &Group{
				Id:    int(group.ID),
				Name:  group.Name,
				State: group.State,
			}
			expItem.Groups = append(expItem.Groups, groupItem)
		}
	}

	return resp, nil
}
