package service

import (
	"context"
	"fmt"
	"strings"

	"git.7k7k.com/data/abAdmin/repository"
	"git.7k7k.com/data/abAdmin/repository/dao"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// @autowire(set=service)
type AutoCompleteService struct {
	Query        *dao.Query
	QueryForBIv1 *repository.QueryForBIv1

	modelConfig map[string]*gorm.DB `wire:"-"`
}

type AutoCompleteIn struct {
	Model     string `json:"model,omitempty" form:"model"`           // 模型，枚举值在 modelConfig 里
	Key       string `json:"key,omitempty" form:"key"`               // 唯一键，一般为 id
	Label     string `json:"label,omitempty" form:"label"`           // 要显示的字段
	FilterKey string `json:"filter_key,omitempty" form:"filter_key"` // 过滤的字段，默认为 label
	Keyword   string `json:"keyword,omitempty" form:"keyword"`       // 关键词
	ExtraKeys string `json:"extra_keys,omitempty" form:"extra_keys"` // 要返回的额外字段
	Limit     int    `json:"limit,omitempty" form:"limit"`           // 限制返回的数量
	PK        string `json:"pk,omitempty" form:"pk"`                 // 主键精确查询，用来回显
}

type AutoCompleteOut struct {
	List      []Item            `json:"list,omitempty"`
	ListGroup map[string][]Item `json:"list_group,omitempty"`
}

type Item struct {
	Key    int            `json:"key"`
	KeyStr string         `json:"key_str"`
	Label  string         `json:"label,omitempty"`
	Group  string         `json:"group,omitempty"`
	Extra  map[string]any `json:"extra,omitempty"`
}

func (s *AutoCompleteService) List(ctx context.Context, in AutoCompleteIn) (out AutoCompleteOut, err error) {
	query0, ok := s.config()[in.Model]
	if !ok {
		err = fmt.Errorf("不支持指定的key(%s)", in.Model)
		return
	}

	if in.Key == "" {
		in.Key = "id"
	}

	if in.Limit == 0 {
		in.Limit = 100
	}

	list := []map[string]any{}
	query := query0.WithContext(ctx).Limit(in.Limit)
	if in.PK != "" {
		query = query.Where(fmt.Sprintf("%s = ?", in.Key), in.PK)
	}
	if in.Keyword != "" {
		fields := []string{in.Label}
		for _, s := range strings.Split(in.FilterKey, ",") {
			if s != "" {
				fields = append(fields, s)
			}
		}
		fields = lo.Filter(fields, func(s string, _ int) bool { return s != "" })
		if len(fields) > 0 {
			subQuery := repository.NewQuery(query)
			for _, field := range fields {
				subQuery = subQuery.Or(fmt.Sprintf("%s like ?", field), "%"+in.Keyword+"%")
			}
			query = query.Where(subQuery)
		}
	}
	err = query.Find(&list).Error
	if err != nil {
		return
	}

	for _, row := range list {
		item := Item{
			Key:    cast.ToInt(row[in.Key]),
			KeyStr: cast.ToString(row[in.Key]),
			Label:  cast.ToString(row[in.Label]),
			Extra:  map[string]any{},
		}
		if in.ExtraKeys != "" {
			for _, key := range strings.Split(in.ExtraKeys, ",") {
				item.Extra[key] = row[key]
			}
		}

		out.List = append(out.List, item)
	}

	return
}

func (s *AutoCompleteService) config() map[string]*gorm.DB {
	if s.modelConfig == nil {
		s.modelConfig = map[string]*gorm.DB{
			"user":    s.QueryForBIv1.User.WithContext(context.Background()).UnderlyingDB().Where("status = 1"),
			"tag":     s.Query.Tag.WithContext(context.Background()).UnderlyingDB().Where("state >= 1"),
			"project": s.Query.Project.WithContext(context.Background()).UnderlyingDB().Where("state = 1"),
			"role":    s.QueryForBIv1.Role.WithContext(context.Background()).UnderlyingDB().Where("name like 'abtest%'"),
		}
	}
	return s.modelConfig
}
