package service

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"reflect"
	"regexp"
	"strings"
	"sync"
	"time"

	"git.7k7k.com/data/abAdmin/infra/auth"
	"git.7k7k.com/data/abAdmin/model/dto"
	"github.com/samber/lo"

	"git.7k7k.com/data/abAdmin/gopkg/utils"

	"git.7k7k.com/data/abAdmin/gopkg/condition"
	"git.7k7k.com/data/abAdmin/httpd/base"
	"git.7k7k.com/data/abAdmin/httpd/request"
	"git.7k7k.com/data/abAdmin/infra/config"
	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/repository/dao"
	"github.com/cockroachdb/errors"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

const (
	// 校验版本号的正则表达式, eg:"1.2.3", "v1.2", "V1.2.3-beta+build456"
	// (?:[Vv]?可以有大V或小v开头，也可以没有
	// \d+(?:\.\d+){0,2} 匹配主版本号以及可选的次版本号和修订版本号部分
	// (?:-[a-zA-Z]+(?:\.\d+)?)? 匹配可选的预发布版本号部分，如 -alpha 或 -alpha.1 等形式
	// (?:\+[a-zA-Z0-9]+)? 匹配可选的构建版本号部分 如 +build123
	versionPattern = `^[vV]?\d+(?:\.\d+)*(?:-[\w-]+(?:\.[\w-]+)*)?(?:\+[\w-]+(?:\.[\w-]+)*)?$`

	// 对range值进行校验（兼容空格），eg: 1,2  1,3,5  102.3,108
	rangeFloatPattern = `^[0-9.]+\s*(,\s*[0-9.]+\s*)*$`

	rangeIntPattern = `^[0-9]+\s*(,\s*[0-9]+\s*)*$`

	// 整数
	intPattern = `(-?\d+)`

	// 浮点数
	floatPattern = `^[+\-]?(?:(?:0|[1-9]\d*)(?:\.\d*)?|\.\d+)(?:\d[eE][+\-]?\d+)?$`

	// booleanPattern，不区分大小写
	booleanPattern = "(?i)^(true|false)$"

	// 用于匹配数字类型的数组格式
	listIntPattern = `^\[\s*(\d+(?:,\s*\d+)*)\s*\]$`

	// 用于匹配字符串数组的格式
	listStringPattern = `^\[\s*("(?:\\.|[^"])*"(?:,\s*"(?:\\.|[^"])*")*)?\s*\]$`
)

const (
	triggerUser = "trigger_user_key"
	triggerStop = "trigger_stop_key"
	triggerEnd  = "trigger_end_key"
)

// Query 查询服务
// @autowire(set=service)
type StrategyService struct {
	DS *dao.Query
}

// Create 新增策略模板，只有“触发用户规则”和“分流用户分群划分”有模板
func (s *StrategyService) Create(ctx context.Context, req *dto.AddStrategyReq) error {
	var rules interface{}

	switch req.TplType {
	case model.StrategyUserRule:
		if req.Rules == nil {
			return base.NewAlertTip("模板数据不能为空")
		}
		// 对规则的合法性进行校验
		ok, err := IsValid(req.Rules)
		if err != nil {
			return base.NewAlertTip(err.Error())
		}
		if !ok {
			return base.NewAlertTip("触发用户规则验证失败")
		}
		rules = req.Rules

	case model.StrategyDivertedRule:
		if req.CSRData == nil || len(req.CSRData) == 0 {
			return base.NewAlertTip("模板数据不能为空")
		}
		ok, err := IsValidCSR(req.CSRData)
		if err != nil {
			return base.NewAlertTip(err.Error())
		}
		if !ok {
			return base.NewAlertTip("分流用户群划分验证失败")
		}
		rules = req.CSRData

	default:
		return base.NewAlertTip("模板类型不支持")
	}

	data, err := json.Marshal(rules)
	if err != nil {
		return base.NewBizError(err)
	}

	// 新增之前要进行名称唯一性的校验
	projectId := auth.GetProjectId64(ctx)
	strategyDao := s.DS.StrategyTpl
	count, err := strategyDao.Where(strategyDao.ProjectID.Eq(projectId), strategyDao.TplType.Eq(req.TplType),
		strategyDao.Name.Eq(req.Name), strategyDao.State.Neq(cast.ToInt32(model.StrategyStateDeleted))).
		Count()
	if err != nil {
		return base.NewBizError(err)
	}
	if count > 0 {
		return base.NewAlertTip(fmt.Sprintf("模板名字[%s]已存在", req.Name))
	}

	now := time.Now()
	strategyTpl := &model.StrategyTpl{
		Name:       req.Name,
		ProjectID:  auth.GetProjectId64(ctx),
		TplType:    req.TplType,
		TplData:    string(data),
		State:      model.StrategyStateEnabled,
		CreateTime: now,
		UpdateTime: now,
	}
	return strategyDao.Create(strategyTpl)
}

// List 根据模板的类型查询模板列表，按照创建时间逆排序
func (s *StrategyService) List(ctx context.Context, query *request.StrategyQuery) ([]*dto.StrategyTemplate, error) {
	strategyDao := s.DS.StrategyTpl
	dataList, err := strategyDao.Where(strategyDao.ProjectID.Eq(auth.GetProjectId64(ctx)),
		strategyDao.TplType.Eq(query.TplType), strategyDao.State.Neq(model.StrategyStateDeleted)).
		Order(strategyDao.CreateTime.Desc()).Find()
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
		return nil, nil
	}
	responseList := make([]*dto.StrategyTemplate, len(dataList))
	for i, data := range dataList {
		var tplData interface{}
		if data.TplType == model.StrategyUserRule {
			tplData = condition.Filter{}
		} else {
			tplData = request.CSRData{}
		}
		err = json.Unmarshal([]byte(data.TplData), &tplData)
		if err != nil {
			return nil, err
		}
		responseList[i] = &dto.StrategyTemplate{
			StrategyTpl: *data,
			Data:        &tplData,
		}
	}
	return responseList, nil
}

// Detail 根据模板的ID获取模板详情
func (s *StrategyService) Detail(query *request.StrategyDetailQuery) (*dto.StrategyTemplate, error) {
	strategyDao := s.DS.StrategyTpl
	detail, err := strategyDao.Where(strategyDao.ID.Eq(query.Id)).First()
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
		return nil, nil
	}

	var data interface{}
	if detail.TplType == model.StrategyUserRule {
		data = condition.Filter{}
	} else {
		data = request.CSRData{}
	}
	err = json.Unmarshal([]byte(detail.TplData), &data)
	if err != nil {
		return nil, err
	}
	resp := &dto.StrategyTemplate{
		StrategyTpl: *detail,
		Data:        data,
	}
	return resp, nil
}

// FilterList 策略过滤器，可以根据类型过滤
func (s *StrategyService) FilterList(ctx context.Context, query *request.StrategyFilterQuery) (data []*dto.Condition, err error) {
	// 策略map，第一层的key为策略的类型，第二层的key为表达式的key（resource/assets/expression_keys.json）
	var strategyMap map[string]map[string]interface{}
	// 前端回显
	if query.ExpId != 0 {
		expDao := s.DS.Exp
		exp, err := expDao.Where(expDao.ID.Eq(query.ExpId)).First()
		if err != nil {
			return nil, fmt.Errorf("查询实验失败%d, err: %s", query.ExpId, err.Error())
		}
		// 解析获取策略中配置的所有实验和方案
		strategyMap = covertStrategiesToMap(exp.Strategy)
	}

	prjDao := s.DS.Project
	prj, err := prjDao.Where(prjDao.ID.Eq(auth.GetProjectId64(ctx))).First()
	if err != nil {
		return nil, errors.Wrapf(err, "查询项目失败")
	}

	for _, cond := range config.Strategies {
		if cond.StrategyType != query.Type {
			continue
		}

		// 如果是用户规则，则需要按项目过滤
		if cond.StrategyType == triggerUser && len(prj.Data.AllowUserKeys) > 0 {
			if !lo.Contains(prj.Data.AllowUserKeys, cond.Value) {
				continue
			}
		}

		for i, op := range cond.AllowOpt {
			cond.AllowOpt[i] = config.Operators[op.Value]
		}
		if err = s.fillOptionsData(cond, query, strategyMap); err != nil {
			return nil, err
		}
		data = append(data, cond)
	}
	return
}

// QueryExpression 查询表达式
func (s *StrategyService) QueryExpression(ctx context.Context, req *dto.StrategyQueryReq) (*dto.StrategyResp, error) {
	expDao := s.DS.Exp
	exp, err := expDao.WithContext(ctx).Where(expDao.ID.Eq(req.ExpId)).First()
	if err != nil {
		return nil, err
	}

	resp := &dto.StrategyResp{
		PreExpression: make(map[string]string, 3),
		CurExpression: make(map[string]string, 3),
	}

	// 定义需要处理的字段
	triggers := []struct {
		key string
		pre *condition.Filter
		cur *condition.Filter
	}{
		{triggerUser, exp.Strategy.TriggerUserKey, req.Strategy.TriggerUserKey},
		{triggerStop, exp.Strategy.TriggerStopKey, req.Strategy.TriggerStopKey},
		{triggerEnd, exp.Strategy.TriggerEndKey, req.Strategy.TriggerEndKey},
	}

	lock := sync.Mutex{}
	var wg sync.WaitGroup
	for _, t := range triggers {
		wg.Add(2)
		go func(key string, pre *condition.Filter) {
			defer wg.Done()
			defer func() {
				if err := recover(); err != nil {
					slog.Error("GenerateExpression panic: %v", err)
				}
			}()
			lock.Lock()
			resp.PreExpression[key] = s.GenerateExpression(pre)
			lock.Unlock()
		}(t.key, t.pre)

		go func(key string, cur *condition.Filter) {
			defer wg.Done()
			defer func() {
				if err := recover(); err != nil {
					slog.Error("GenerateExpression panic: %v", err)
				}
			}()
			lock.Lock()
			resp.CurExpression[key] = s.GenerateExpression(cur)
			lock.Unlock()
		}(t.key, t.cur)
	}

	wg.Wait()
	return resp, nil
}

// queryExperimentsByIds 根据实验ID列表获取实验ID和名称
func (s *StrategyService) queryExperimentsByIds(ids []int64) ([]*model.Exp, error) {
	expDao := s.DS.Exp
	exps, err := expDao.Select(expDao.ID, expDao.Name).Where(expDao.ID.In(ids...)).Find()
	if err != nil {
		return nil, fmt.Errorf("查询实验失败%v, err: %w", ids, err)
	}
	return exps, nil
}

// queryGroupsByIds 根据id列表查询方案数据
func (s *StrategyService) queryGroupsByIds(ids []int64) ([]*model.Group, error) {
	groupDao := s.DS.Group
	groups, err := groupDao.Select(groupDao.ID, groupDao.Name, groupDao.ExpID).Where(groupDao.ID.In(ids...)).Find()
	if err != nil {
		return nil, fmt.Errorf("查询实验方案失败%v, err: %w", ids, err)
	}
	return groups, nil
}

// IsValid 条件的校验
func IsValid(f *condition.Filter) (bool, error) {
	if f.Logic != "" {
		// 对逻辑运算符的合法性校验
		if !condition.IsLogicOp(f.Logic) {
			return false, fmt.Errorf("无效的逻辑运算符: %s", f.Logic)
		}
		// 如果有逻辑运算符，则必须有条件
		if len(f.Conditions) == 0 {
			return false, fmt.Errorf("存在逻辑运算，则条件不能为空")
		}
	}
	// 对关系运算符的合法性校验
	if f.Op != "" && !condition.IsRelationOp(f.Op) {
		return false, fmt.Errorf("无效的关系运算符: %s", f.Op)
	}
	// 如果有条件表达式，则逻辑操作符必须有
	if len(f.Conditions) > 0 && f.Logic == "" {
		return false, fmt.Errorf("逻辑运算符不能为空")
	}
	// 如果没有条件表达式，则运算操作符及key、value、type都必须有
	if len(f.Conditions) == 0 {
		if _, ok := config.ExpressionKeys[f.Key]; !ok {
			return false, fmt.Errorf("无关联字段:%s", f.Key)
		}
		if f.Key == "" {
			return false, fmt.Errorf("key不能为空")
		}
		if f.Op == "" {
			return false, fmt.Errorf("%s关系运算符不能为空", config.ExpressionKeys[f.Key].Label)
		}
		if f.Value == nil {
			return false, fmt.Errorf("%s的值不能为空", config.ExpressionKeys[f.Key].Label)
		}
		if f.Type == "" {
			return false, fmt.Errorf("%s的值对应的类型不能为空", config.ExpressionKeys[f.Key].Label)
		}
		// 详见配置文件 resource/assets/expression_keys.json
		if _, ok := config.ExpressionKeys[f.Key]; !ok {
			return false, fmt.Errorf("不合法的key: %s", f.Key)
		}

		if err := validValue(f); err != nil {
			return false, err
		}
	}

	// 递归对condition进行校验
	if len(f.Conditions) > 0 {
		for _, cond := range f.Conditions {
			ok, err := IsValid(cond)
			if err != nil || !ok {
				return false, err
			}
		}
	}
	return true, nil
}

// validValue 对值进行校验
func validValue(f *condition.Filter) error {
	if ok := isEmptyValue(f.Value); ok {
		return fmt.Errorf("%s的值不能为空", config.ExpressionKeys[f.Key].Label)
	}
	switch f.Type {
	case condition.STRING:
		return nil

	// 对版本号进行规则校验
	case condition.StringVersion:
		return validReg(f, versionPattern)

	case condition.ListInt:
		return validListReg(f, listIntPattern)

	case condition.ListString:
		return validListReg(f, listStringPattern)

	case condition.INT, condition.Timestamp:
		return validReg(f, intPattern)

	case condition.FLOAT:
		return validReg(f, floatPattern)

	case condition.BOOL:
		return validReg(f, booleanPattern)

		// range类型，格式要求是"数字,数字"
	case condition.RangeFloat, condition.RangeInt:
		result, err := rangeValid(f.Key, f.Value, f.Type)
		if err != nil {
			return err
		}
		f.Value = result
		return nil

	case condition.ListStringDefault, condition.RangeIntDefault:
		return validDefault(f)

	default:
		return fmt.Errorf("未知的数据类型: %s", f.Type)
	}
}

// isEmptyValue 判断是否有值，无值返回true，空字符串，空map是无值的，0是有值的
func isEmptyValue(i interface{}) bool {
	switch v := i.(type) {
	case nil:
		return true
	case string:
		return v == ""
	case []interface{}:
		return len(v) == 0
	case map[string]interface{}:
		return len(v) == 0
	case chan interface{}:
		return v == nil
	case func() interface{}:
		return v == nil
	case *interface{}:
		return v == nil
	default:
		return false
	}
}

// validReg 正则匹配
func validListReg(f *condition.Filter, pattern string) error {
	// 兼容字符串
	value, ok := f.Value.([]interface{})
	if !ok {
		return validReg(f, pattern)
	}
	adjustValues := make([]interface{}, 0, len(value))
	for _, val := range value {
		// 有且只有2个
		typeVal := strings.Split(string(f.Type), ".")
		if len(typeVal) != 2 {
			return fmt.Errorf("%s的值类型不正确", config.ExpressionKeys[f.Key].Label)
		}

		newPattern := getPatternByValueType(condition.ValueType(typeVal[1]))
		if newPattern == "" {
			// string 类型 不必再处理，以下是为了兼容前端的数据格式，将其解析为系统正确的格式
			continue
		}

		if ok := regMatch(newPattern, val); !ok {
			return fmt.Errorf("%s的值类型不正确", config.ExpressionKeys[f.Key].Label)
		}

		adjustValue, err := adjustValueType(val, condition.ValueType(typeVal[1]))
		if err != nil {
			return fmt.Errorf("%s的值转换报错，%s", config.ExpressionKeys[f.Key].Label, err.Error())
		}
		adjustValues = append(adjustValues, adjustValue)
	}

	return nil
}

// validReg 正则匹配,true表示匹配
func validReg(f *condition.Filter, pattern string) error {
	if !regMatch(pattern, f.Value) {
		return fmt.Errorf("%s的值格式不正确", config.ExpressionKeys[f.Key].Label)
	}
	// 将值的类型调整为后台确定的数据类型
	adjustValue, err := adjustValueType(f.Value, f.Type)
	if err != nil {
		return fmt.Errorf("%s的值转换报错，%s", config.ExpressionKeys[f.Key].Label, err.Error())
	}
	f.Value = adjustValue
	return nil
}

// regMatch 正则表达是校验
func regMatch(pattern string, value interface{}) bool {
	val := strings.TrimSpace(cast.ToString(value))
	reg := regexp.MustCompile(pattern)
	return reg.MatchString(val)
}

// getPatternByValueType 根据数据的类型获取正则表达式
func getPatternByValueType(valueType condition.ValueType) string {
	switch valueType {
	case condition.INT:
		return intPattern
	case condition.FLOAT:
		return floatPattern
	case condition.BOOL:
		return booleanPattern
	default:
		return ""
	}
}

// adjustValueType 对值进行转换
func adjustValueType(val interface{}, valueType condition.ValueType) (interface{}, error) {
	switch valueType {
	case condition.INT:
		return cast.ToInt64E(val)
	case condition.FLOAT:
		return cast.ToFloat64E(val)
	case condition.BOOL:
		return cast.ToBoolE(val)
	case condition.Timestamp:
		return cast.ToInt64E(val)
	default:
		return val, nil
	}
}

// IsValidCSR csr参数校验
func IsValidCSR(csr []*model.CSRData) (bool, error) {
	for _, data := range csr {
		if data.Key == "" || utils.IsEmpty(data.Value) {
			return false, base.NewAlertTip("csr验证失败，key不能为空")
		}

		var expression *dto.Condition
		keyValidate := false
		for _, cond := range config.Strategies {
			if cond.StrategyType != "trigger_diversion_key" {
				continue
			}
			if cond.Value == data.Key {
				expression = cond
				keyValidate = true
				break
			}
		}
		if !keyValidate {
			return false, base.NewAlertTip(data.Key + "是不合法的，验证失败")
		}

		switch data.Type {
		// range类型，格式要求是["数字,数字"]
		case condition.RangeFloat, condition.RangeInt:
			result, err := rangeValid(data.Key, data.Value, data.Type)
			if err != nil {
				return false, err
			}
			data.Value = result

		//	list类型，前端取默认值用于展示，并将其传给后端
		case condition.ListStringDefault, condition.RangeIntDefault:
			if !reflect.DeepEqual(data.Value, expression.DefaultValue) {
				return false, fmt.Errorf("%s的值与系统配置不符", config.ExpressionKeys[data.Key].Label)
			}
		}
	}
	return true, nil
}

// rangeFloatValid 类型为range的值校验
func rangeValid(k string, v interface{}, valueType condition.ValueType) ([]interface{}, error) {
	list, ok := v.([]interface{})
	if !ok {
		var pattern string
		if valueType == condition.RangeFloat {
			pattern = rangeFloatPattern
		} else if valueType == condition.RangeInt {
			pattern = rangeIntPattern
		}
		// 兼容前端传字符串
		val := cast.ToString(v)
		reg := regexp.MustCompile(pattern)
		if !reg.MatchString(val) {
			return nil, fmt.Errorf("%s的值格式不正确", config.ExpressionKeys[k].Label)
		}
		values := strings.Split(val, ",")

		list = make([]interface{}, len(values))
		for i, value := range values {
			_value := strings.TrimSpace(value)
			if len(_value) == 0 {
				// 忽略空字符串
				continue
			}
			list[i] = _value
		}
	}

	// 值必须大于等于0以及严格递增验证逻辑
	var (
		prevValue  float64
		currentVal float64
		err        error
	)
	for i := range list {
		if valueType == condition.RangeFloat {
			currentVal, err = cast.ToFloat64E(list[i])
		} else if valueType == condition.RangeInt {
			var intValue int64
			intValue, err = cast.ToInt64E(list[i])
			currentVal = float64(intValue)
		}

		if err != nil {
			return nil, fmt.Errorf("%s的值解析报错: %v", config.ExpressionKeys[k].Label, err.Error())
		}
		if currentVal < float64(0) {
			return nil, fmt.Errorf("%s的值必须大于等于0", config.ExpressionKeys[k].Label)
		}
		if i > 0 && currentVal <= prevValue {
			return nil, fmt.Errorf("%s的值必须是严格递增", config.ExpressionKeys[k].Label)
		}
		prevValue = currentVal
		list[i] = currentVal
	}
	return list, nil
}

// validDefault 对默认值进行验证
func validDefault(f *condition.Filter) error {
	for _, cond := range config.Strategies {
		if cond.Value != f.Key {
			continue
		}
		if !reflect.DeepEqual(f.Value, cond.DefaultValue) {
			return fmt.Errorf("%s与默认值不一致", config.ExpressionKeys[f.Key].Label)
		}
	}
	return nil
}

// covertStrategiesToMap 解析实验的策略为map，key为策略的类型
func covertStrategiesToMap(strategy *model.Strategy) map[string]map[string]interface{} {
	strategyMap := make(map[string]map[string]interface{})
	// 触发用户规则
	if strategy.TriggerUserKey != nil {
		strategyMap[triggerUser] = covertFilterToMap(strategy.TriggerUserKey)
	}

	// 停止进量触发规则
	if strategy.TriggerStopKey != nil {
		strategyMap[triggerStop] = covertFilterToMap(strategy.TriggerStopKey)
	}

	//	 关闭触发规则
	if strategy.TriggerEndKey != nil {
		strategyMap[triggerEnd] = covertFilterToMap(strategy.TriggerEndKey)
	}
	return strategyMap
}

// covertFilterToMap key为表达式的key（resource/assets/expression_keys.json）
func covertFilterToMap(f *condition.Filter) map[string]interface{} {
	conditionMap := make(map[string]interface{})
	if f == nil {
		return conditionMap
	}
	if len(f.Conditions) == 0 {
		return map[string]interface{}{
			f.Key: f.Value,
		}
	}

	// 根据逻辑运算符将初始结果与子过滤器结果合并得到最终结果
	subIdMaps := make([]map[string]interface{}, len(f.Conditions))
	for i, subFilter := range f.Conditions {
		subIdMaps[i] = covertFilterToMap(subFilter)
	}

	// 进行merge，可能会出现一个key出现多次的情况
	result := make(map[string][]interface{})
	for _, subIdMap := range subIdMaps {
		for k, v := range subIdMap {
			result[k] = append(result[k], v)
		}
	}
	for k, v := range result {
		conditionMap[k] = v
	}

	return conditionMap
}

// fillOptionsData 填充options的值（前端反显），目前有国家，方案和实验
func (s *StrategyService) fillOptionsData(cond *dto.Condition, query *request.StrategyFilterQuery,
	strategyMap map[string]map[string]interface{}) error {
	// 与默认值区分开
	if IsCountry(cond.Value) && cond.Type == condition.ListString {
		// countries
		cond.Options = &config.Region
		return nil
	}

	if query.ExpId == 0 {
		slog.Debug("query is invalid", "exp id:", cast.ToString(query.ExpId))
		return nil
	}
	strategy, ok := strategyMap[cond.StrategyType]
	if !ok {
		// 该实验没有策略该类型的
		slog.Debug(fmt.Sprintf("exp id: %d strategy not found for type: %s", query.ExpId, cond.StrategyType))
		return nil
	}

	if !IsExp(cond.Value) && !IsExpGroup(cond.Value) {
		slog.Debug("need to fill options", "cond value:", cond.Value)
		return nil
	}

	ids, err := extractIdsFromValues(strategy[cond.Value])
	if err != nil {
		return fmt.Errorf("extract ids from values failed: %w", err)
	}
	// 如果没有直接返回
	if len(ids) == 0 {
		return nil
	}

	if IsExp(cond.Value) { //	实验
		exps, err := s.queryExperimentsByIds(ids)
		if err != nil {
			return err
		}
		nodes := make([]*dto.StrategyNode, len(exps))
		for i, exp := range exps {
			nodes[i] = &dto.StrategyNode{
				Label: exp.Name,
				Value: fmt.Sprintf("%d", exp.ID),
			}
		}
		cond.Options = nodes
	} else if IsExpGroup(cond.Value) { //	方案
		// 获取选择的所有方案
		groups, err := s.queryGroupsByIds(ids)
		if err != nil {
			return err
		}

		// 从方案分组映射中获取实验id列表
		expIds := make([]int64, 0, len(groups))
		for _, group := range groups {
			expIds = append(expIds, group.ExpID)
		}

		exps, err := s.queryExperimentsByIds(expIds)
		if err != nil {
			return err
		}

		cond.Options = buildGroupListToStrategyNodes(exps, groups)
	}
	return nil
}

// extractIdsFromValues 从给定的值中提取int64类型的id列表
func extractIdsFromValues(values interface{}) ([]int64, error) {
	var ids []int64
	if values == nil {
		return ids, nil
	}

	switch values := values.(type) {
	case int64:
		ids = append(ids, values)
	case []interface{}:
		ids = make([]int64, 0, len(values))
		for _, value := range values {
			id, err := cast.ToInt64E(value)
			if err == nil {
				ids = append(ids, id)
			} else {
				val, ok := value.([]interface{})
				if ok {
					for _, v := range val {
						id, err := cast.ToInt64E(v)
						if err != nil {
							return nil, err
						}
						ids = append(ids, id)
					}
				}
			}
		}
	default:
		return nil, fmt.Errorf("unsupported values type")
	}
	return ids, nil
}

// buildGroupListToStrategyNodes 构建包含子节点（方案）的实验列表
func buildGroupListToStrategyNodes(exps []*model.Exp, groups []*model.Group) []*dto.StrategyNode {
	// 按照实验进行分组
	groupMap := make(map[int64][]*dto.StrategyNode)
	for _, group := range groups {
		groupMap[group.ID] = append(groupMap[group.ID], &dto.StrategyNode{
			Label: group.Name,
			Value: fmt.Sprintf("%d", group.ID),
		})
	}

	groupList := make([]*dto.StrategyNode, len(exps))
	for i, exp := range exps {
		groupList[i] = &dto.StrategyNode{
			Label:    exp.Name,
			Value:    fmt.Sprintf("%d", exp.ID),
			Children: groupMap[exp.ID],
		}
	}
	return groupList
}

// GenerateCsrExpression 获取csr
func (s StrategyService) GenerateCsrExpression(csrs []*model.CSRData) {
	//for _, csr := range csrs {
	//
	//}
}

// GenerateExpression 解析Filter结构体并生成表达式
func (s *StrategyService) GenerateExpression(f *condition.Filter) string {
	if f == nil {
		return ""
	}
	if len(f.Conditions) == 0 {
		key, op := f.Key, f.Op
		value := f.Value
		if config.ExpressionKeys != nil && config.ExpressionKeys[f.Key] != nil {
			key = config.ExpressionKeys[f.Key].Label
		}
		if config.Operators != nil && config.Operators[f.Op] != nil {
			op = config.Operators[f.Op].Label
		}
		if IsCountry(f.Key) {
			value = fmt.Sprintf("[%s]", strings.Join(convertCountry(f), ";"))
		} else if IsExp(f.Key) {
			expNames, err := s.convertExp(f)
			if err != nil {
				return ""
			}
			value = fmt.Sprintf("[%s]", strings.Join(expNames, ";"))
		} else if IsExpGroup(f.Key) {
			groupNames, err := s.convertExpGroup(f)
			if err != nil {
				return ""
			}
			value = fmt.Sprintf("[%s]", strings.Join(groupNames, ";"))
		} else if f.Type == condition.Timestamp {
			return time.Unix(cast.ToInt64(f.Value)/1000, 0).Format(time.DateTime)
		}
		if f.Type == condition.FLOAT {
			return fmt.Sprintf("%s%s%v", key, op, cast.ToString(value))
		} else {
			return fmt.Sprintf("%s%s'%v'", key, op, value)
		}
	}

	// 根据逻辑运算符将初始结果与子过滤器结果合并得到最终结果
	subExpressions := make([]string, len(f.Conditions))
	for i, subFilter := range f.Conditions {
		subExpressions[i] = s.GenerateExpression(subFilter)
	}

	return fmt.Sprintf("(%s)", strings.Join(subExpressions, fmt.Sprintf(" %s ", convertLogic(f.Logic))))
}

// 逻辑关系符转换为中文
func convertLogic(logic condition.LogicOperatorType) string {
	switch logic {
	case condition.AND, condition.AndSymbol:
		return "且"
	case condition.OR, condition.OrSymbol:
		return "或"
	default:
		return string(logic)
	}
}

// convertCountry 国家编码转换为中文
func convertCountry(f *condition.Filter) []string {
	if filterValue, ok := f.Value.([]interface{}); ok {
		finalCountry := make([]string, 0, len(filterValue))
		for _, country := range filterValue {
			finalCountry = append(finalCountry, config.Country[country.(string)].Label)
		}
		return finalCountry
	}
	return nil
}

// convertExp 实验ID转换为中文
func (s *StrategyService) convertExp(f *condition.Filter) ([]string, error) {
	ids, err := extractIdsFromValues(f.Value)
	if err != nil {
		return nil, fmt.Errorf("exp ids not found in filter, %v", f.Value)
	}
	exps, err := s.queryExperimentsByIds(ids)
	if err != nil {
		return nil, err
	}
	names := make([]string, 0, len(exps))
	for _, exp := range exps {
		names = append(names, exp.Name)
	}
	return names, nil
}

// convertExpGroup 实验方案ID转换为中文
func (s *StrategyService) convertExpGroup(f *condition.Filter) ([]string, error) {
	ids, err := extractIdsFromValues(f.Value)
	if err != nil {
		return nil, fmt.Errorf("group ids not found in filter, %v", f.Value)
	}
	groups, err := s.queryGroupsByIds(ids)
	if err != nil {
		return nil, err
	}

	// 从方案分组映射中获取实验id列表
	expIds := make([]int64, 0, len(groups))
	for _, group := range groups {
		expIds = append(expIds, group.ExpID)
	}
	exps, err := s.queryExperimentsByIds(expIds)
	if err != nil {
		return nil, err
	}
	expMap := lo.KeyBy(exps, func(exp *model.Exp) int64 { return exp.ID })

	names := make([]string, 0, len(groups))
	for _, group := range groups {
		exp := expMap[group.ExpID]
		names = append(names, fmt.Sprintf("%s/%s", exp.Name, group.Name))
	}
	return names, nil
}

// IsCountry 是否是国家
func IsCountry(key string) bool {
	return key == "country"
}

// IsExp 是否是实验
func IsExp(key string) bool {
	return key == "history_exp_ids" || key == "running_exp_ids"
}

// IsExpGroup 是否是方案
func IsExpGroup(key string) bool {
	return key == "history_grp_ids" || key == "running_grp_ids"
}
