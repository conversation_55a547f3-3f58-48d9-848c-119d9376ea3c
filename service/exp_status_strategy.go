package service

import (
	"context"
	"fmt"
	"git.7k7k.com/data/abAdmin/repository"

	"git.7k7k.com/data/abAdmin/httpd/base"
	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/repository/dao"
	"github.com/jinzhu/copier"
)

type ExpOpt string

// 对实验的操作
const (
	expOpenOpt            ExpOpt = "开启全量"
	expOpenGrayOpt        ExpOpt = "开启灰度"
	expOpenWithoutFlowOpt ExpOpt = "开启不进量"
	expWithoutFlowOpt     ExpOpt = "停止进量"
	expRestartOpt         ExpOpt = "重启"
	expClosedOpt          ExpOpt = "关闭"
)

// @autowire(set=service)
type ExpStateService struct {
	DS                        *dao.Query
	UserQuery                 *repository.QueryForBIv1
	MetricService             *MetricService
	ExpLogService             *ExpLogService
	LayerService              *LayerService
	GroupService              *GroupService
	ProjectService            *ProjectService
	Events                    *Events
	ApprovalRuleManageService *ApprovalRuleManageService
	ApprovalTaskManageService *ApprovalTaskManageService
}

// ExpStateChangeStrategy 实验状态变更策略接口
type ExpStateChangeStrategy interface {
	// TryTransStatus 判断是否可以变更状态， 并设置实验变更后的状态
	TryTransStatus(status model.ExpStatus) bool

	// GetStatusAfterTrans 获取转换后的状态值
	GetStatusAfterTrans() model.ExpStatus

	// GetOperate 获取操作名
	GetOperate() ExpOpt

	// 获取审批节点
	GetApprovalNode() string

	// CreateOrUpdateTask 创建或更新任务相关逻辑
	CreateOrUpdateTask(tx *dao.Query, newExp *model.Exp) error

	// 整理实验组变更的数据
	PrepareGroupData(groups []*model.Group) error

	// UpdateGroupStatus 实验组的状态变更
	UpdateGroupStatus(tx *dao.Query, groups []*model.Group) error
}

// ToggleExpStatus 通用的变更实验状态函数
func (s *ExpStateService) ToggleExpStatus(ctx context.Context, exp *model.Exp, strategy ExpStateChangeStrategy) (err error) {
	// 判断实验是否在审批中
	isApprovalRunning, err := s.ApprovalTaskManageService.IsExpApprovalRunning(ctx, exp.ID)
	if err != nil {
		return err
	}
	if isApprovalRunning {
		return base.NewAlertTip("实验正在审批中，不支持状态修改")
	}
	oldExpStatus := model.ExpStatus(exp.State)
	if !strategy.TryTransStatus(oldExpStatus) {
		return base.NewAlertTip(fmt.Sprintf("实验状态为[%s]，不允许执行[%s]操作",
			oldExpStatus.Name(), strategy.GetOperate()))
	}
	// 变更后的实验
	newExp := &model.Exp{}
	err = copier.Copy(newExp, exp)
	if err != nil {
		return base.NewBizError(err)
	}
	newExp.State = int32(strategy.GetStatusAfterTrans())
	// 获取实验的所有分组
	groups, err := s.GroupService.QueryGroupByExpId(ctx, exp.ID)
	// 变更方案数据
	if len(groups) > 0 {
		err = strategy.PrepareGroupData(groups)
		if err != nil {
			return err
		}
	}
	// 创建实验日志
	expLog, isNeedApproval, err := s.ExpLogService.CreateExpLogByStatus(ctx, exp, newExp, groups, strategy)
	if err != nil {
		return err
	}
	// 不需要审批 继续执行
	if isNeedApproval {
		return model.ExpStateChangeNeedApprovalError
	}
	// 不需要审批，更新快照
	expLogDao := s.DS.ExpLog
	_, err = s.DS.ExpLog.WithContext(ctx).Where(expLogDao.ID.Eq(expLog.ID)).Updates(map[string]interface{}{
		"state": 2,
		"show":  1,
	})
	if err != nil {
		return base.NewBizError(err)
	}
	// 开启事务
	//tx := s.DS.Begin()
	//defer func() {
	//	if p := recover(); p != nil {
	//		// 在事务执行过程中发生了panic
	//		err = fmt.Errorf("发生panic, %v", p)
	//	}
	//	if err != nil {
	//		tx.Rollback()
	//	}
	//}()
	//
	//// 更新实验任务表
	//err = strategy.CreateOrUpdateTask(tx, newExp)
	//if err != nil {
	//	return err
	//}
	//
	//// 更新实验表
	//err = UpdateExpState(tx, newExp, status)
	//if err != nil {
	//	return err
	//}
	//// 更新分组表
	//if len(groups) > 0 {
	//	err = strategy.UpdateGroupStatus(tx, groups)
	//	if err != nil {
	//		return err
	//	}
	//}
	//if newExp.State == int32(model.ExpStatusClosed) {
	//	err = s.ProjectService.RemoveLayerExpFeature(ctx, exp, nil)
	//	if err != nil {
	//		return err
	//	}
	//}
	//
	//if err = tx.Commit(); err != nil {
	//	return err
	//}
	err = StrategyToChangeExpState(ctx, s.DS, newExp, oldExpStatus, groups, strategy)
	if err != nil {
		return err
	}
	_ = s.Events.ExpUpdated.Push(ctx, ExpUpdatedEvent{ExpID: exp.ID, Log: expLog})

	return nil
}

// 实验操作策略转化实验状态操作
func StrategyToChangeExpState(ctx context.Context, tx *dao.Query, newExp *model.Exp, oldExpState model.ExpState, groups []*model.Group, strategy ExpStateChangeStrategy) (err error) {
	err = tx.Transaction(func(tx *dao.Query) error {
		// 查看之前实验的状态，是否合法变化
		//if !strategy.TryTransStatus(oldExpState) {
		//	return base.NewAlertTip(fmt.Sprintf("实验状态为[%s]，不允许执行[%s]操作",
		//		oldExpState.Name(), strategy.GetOperate()))
		//}
		// 更新实验任务表
		err = strategy.CreateOrUpdateTask(tx, newExp)
		if err != nil {
			return err
		}

		// 更新实验表
		err = UpdateExpState(ctx, tx, newExp, oldExpState)
		if err != nil {
			return err
		}
		// 更新分组表
		if len(groups) > 0 {
			err = strategy.UpdateGroupStatus(tx, groups)
			if err != nil {
				return err
			}
		}
		return nil
	})

	return err
}
