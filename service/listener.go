package service

import (
	"context"
	"log/slog"

	"git.7k7k.com/pkg/common/queue"
)

// @autowire(set=service)
type Listener struct {
	Events *Events

	ExpSyncService *ExpSyncService

	NotifyService *NotifyService
}

func (l *Listener) Listen(ctx context.Context) {
	go l.listenExpUpdated(ctx)
}

func (l *Listener) listenExpUpdated(ctx context.Context) {
	l.Events.ExpUpdated.ListenBatch(ctx, queue.ConsumConfig[ExpUpdatedEvent]{
		WorkerSize: 1,
		FlushSize:  1,
		HandleFunc: func(events []ExpUpdatedEvent) error {
			for _, event := range events {
				ctx := context.Background()
				if err := l.ExpSyncService.SyncExp(ctx, event.ExpID); err != nil {
					slog.ErrorContext(ctx, "sync_exp_failed", "exp_id", event.ExpID, "err", err.Error())
				}

				if event.Log != nil {
					err := l.NotifyService.NotifyExpChange(ctx, event.Log)
					if err != nil {
						slog.ErrorContext(ctx, "notify_exp_change_failed", "explog", event.Log, "err", err.Error())
					}
				}
			}
			return nil
		},
	})
}
