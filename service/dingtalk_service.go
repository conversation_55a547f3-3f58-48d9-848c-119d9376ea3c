package service

import (
	"context"
	"fmt"
	"sync"

	"git.7k7k.com/data/abAdmin/gopkg/dtalk"
	"git.7k7k.com/data/abAdmin/httpd/request"
	"git.7k7k.com/data/abAdmin/infra/config"
	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/model/dto"
	"github.com/blinkbean/dingtalk"
	"github.com/spf13/cast"
)

type DingtalkCommandHandleFunc func(ctx context.Context, msg *dtalk.OutGoingCmd) dtalk.Sendable

// @autowire(set=service)
type DingtalkCommandService struct {
	ExpService   *ExpService
	GroupService *GroupService

	once       sync.Once                            `wire:"-"`
	prefixCMDs map[string]DingtalkCommandHandleFunc `wire:"-"`
}

func (s *DingtalkCommandService) init() {
	s.once.Do(func() {
		s.prefixCMDs = map[string]DingtalkCommandHandleFunc{
			"list": s.List,
			"ps":   s.List,
			"s":    s.Search,
		}
	})
}

func (s *DingtalkCommandService) help() dtalk.Sendable {
	return dingtalk.NewTextMsg("支持的命令：\n" +
		"- list 列出最近运行的实验\n" +
		"- s 搜索实验（默认功能）\n")
}

func (s *DingtalkCommandService) Handle(ctx context.Context, msg *dtalk.OutGoingCmd) dtalk.Sendable {
	s.init()

	prefix := msg.Prefix
	handleFunc, ok := s.prefixCMDs[prefix]
	if !ok {
		handleFunc = s.Search
		msg.Args = append([]string{prefix}, msg.Args...)
	}

	return handleFunc(ctx, msg)
}

// List 列出最近运行的实验
func (s *DingtalkCommandService) List(ctx context.Context, msg *dtalk.OutGoingCmd) dtalk.Sendable {
	expDao := s.ExpService.DS.Exp
	exps, err := expDao.Where(expDao.State.Eq(model.ExpStateRunning)).Order(expDao.StartTime.Desc()).Limit(10).Find()
	if err != nil {
		return nil
	}

	md := &dto.Markdown{}
	md.AddLine("## 最近运行的实验")
	for _, exp := range exps {
		md.AddLine(
			fmt.Sprintf("- %s #%d",
				md.LinkTo(md.URL(exp.AbUrl(config.GlobalConfig.ABURL)), exp.Name),
				exp.ID,
			),
		)
	}

	return dingtalk.NewMarkDownMsg("最近运行的实验.", md.ToText())
}

// Search 根据ID搜索实验
func (s *DingtalkCommandService) Search(ctx context.Context, msg *dtalk.OutGoingCmd) dtalk.Sendable {
	keyword := msg.Args[0]
	id := cast.ToInt(keyword)

	md := &dto.Markdown{}

	matchedResult := false
	// 实验ID
	if id > 0 {
		exp, _ := s.ExpService.Detail(int64(id))
		if exp != nil && exp.ID > 0 {
			md.AddLine(
				fmt.Sprintf("- %s #%d",
					md.LinkTo(md.URL(exp.AbUrl(config.GlobalConfig.ABURL)), exp.Name),
					exp.ID,
				),
			)
			matchedResult = true
		}
	}

	// 实验名称
	{
		exps, _ := s.ExpService.List(request.ExpListReq{
			Search:   keyword,
			Page:     1,
			PageSize: 10,
		}, ctx, -1)
		if exps != nil && len(exps.List) > 0 {
			for _, expd := range exps.List {
				exp := expd.ToModel()
				md.AddLine(
					fmt.Sprintf("- %s #%d",
						md.LinkTo(md.URL(exp.AbUrl(config.GlobalConfig.ABURL)), exp.Name),
						exp.ID,
					),
				)
				matchedResult = true
			}
		}
	}

	// 实验组ID
	if id > 0 {
		grps, _ := s.GroupService.GetGroups(ctx, []int64{int64(id)})
		if len(grps) > 0 {
			exp, _ := s.ExpService.Detail(int64(grps[0].ExpID))
			if exp != nil && exp.ID > 0 {
				md.AddLine(
					fmt.Sprintf("- %s #%d",
						md.LinkTo(md.URL(exp.AbUrl(config.GlobalConfig.ABURL)), exp.Name+"（"+grps[0].Name+"）"),
						exp.ID,
					),
				)
				matchedResult = true
			}
		}
	}

	if !matchedResult {
		return s.help()
	}

	return dingtalk.NewMarkDownMsg("查询结果.", md.ToText())
}
