package service

import (
	"context"
	"fmt"

	"git.7k7k.com/data/abAdmin/infra/redises"
	"github.com/cockroachdb/errors"
	"github.com/redis/go-redis/v9"
)

// @autowire(set=service)
type MetricService struct {
	RedisClts *redises.Clients
}

// GetGrpUserCount 批量获取指定实验组的用户数
func (s *MetricService) GetGrpUserCount(ctx context.Context, grpIds []int64) (map[int64]int64, error) {
	pipe := s.RedisClts.AdminRedis.Pipeline()
	cmds := make(map[int64]*redis.IntCmd, len(grpIds))

	// 将所有命令添加到pipeline
	for _, grpId := range grpIds {
		key := fmt.Sprintf("pf_g_u:g%d", grpId)
		cmds[grpId] = pipe.PFCount(ctx, key)
	}

	// 执行pipeline
	_, err := pipe.Exec(ctx)
	if err != nil {
		return nil, err
	}

	// 收集结果
	result := make(map[int64]int64, len(grpIds))
	for grpId, cmd := range cmds {
		count, err := cmd.Result()
		if err != nil {
			return nil, err
		}
		result[grpId] = count
	}

	return result, nil
}

// GetExpUserCount 批量获取指定实验的用户数
func (s *MetricService) GetExpUserCount(ctx context.Context, expIds []int64) (map[int64]int64, error) {
	pipe := s.RedisClts.AdminRedis.Pipeline()
	cmds := make(map[int64]*redis.IntCmd, len(expIds))

	// 将所有命令添加到pipeline
	for _, expId := range expIds {
		key := fmt.Sprintf("pf_e_u:e%d", expId)
		cmds[expId] = pipe.PFCount(ctx, key)
	}

	// 执行pipeline
	_, err := pipe.Exec(ctx)
	if err != nil {
		return nil, err
	}

	// 收集结果
	result := make(map[int64]int64, len(expIds))
	for expId, cmd := range cmds {
		count, err := cmd.Result()
		if err != nil {
			return nil, err
		}
		result[expId] = count
	}

	return result, nil
}

// RealtimeGetGroupCount 获取实时数据
func (s *MetricService) RealtimeGetGroupCount(ctx context.Context, grpIds []int64) (grpCount map[int64]int64, err error) {
	cli := s.RedisClts.AdminRedis
	pipe := cli.Pipeline()
	cmds := make(map[int64]*redis.StringCmd, len(grpIds))

	for _, grpId := range grpIds {
		cmds[grpId] = pipe.Get(ctx, fmt.Sprintf("real_g_u:%d", grpId))
	}

	_, err = pipe.Exec(ctx)
	if err != nil && err != redis.Nil {
		return nil, errors.Wrapf(err, "RealtimeGetGroupCount")
	}

	grpCount = make(map[int64]int64, len(grpIds))
	for grpId, cmd := range cmds {
		val, err := cmd.Int64()
		if err == nil && val > 0 {
			grpCount[grpId] = val
		}
	}

	return grpCount, nil
}
