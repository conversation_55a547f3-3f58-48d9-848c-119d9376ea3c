package service

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"math"
	"strings"
	"time"

	"git.7k7k.com/data/abAdmin/httpd/base"
	"git.7k7k.com/data/abAdmin/httpd/request"
	"git.7k7k.com/data/abAdmin/httpd/response"
	"git.7k7k.com/data/abAdmin/infra/auth"
	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/model/dto"
	"git.7k7k.com/data/abAdmin/repository"
	"git.7k7k.com/data/abAdmin/repository/dao"
	"github.com/cockroachdb/errors"
	carbon "github.com/dromara/carbon/v2"
	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

// Query 查询服务
// @autowire(set=service)
type ExpService struct {
	DS            *dao.Query
	LayerService  *LayerService
	MetricService *MetricService
	Query
	QueryForBIv1              *repository.QueryForBIv1
	ExpStateService           *ExpStateService
	ExpLogService             *ExpLogService
	GroupService              *GroupService
	Events                    *Events
	ProjectService            *ProjectService
	ApprovalTaskManageService *ApprovalTaskManageService
	ApprovalRuleManageService *ApprovalRuleManageService
}

func (s *ExpService) ExpForStrategy(search string, ctx *gin.Context) ([]*dto.StrategyNode, error) {
	expDao := s.DS.Exp
	//expList := make([]map[string]any, 0, 500)
	where := make([]gen.Condition, 0, 3)
	where = append(where,
		expDao.ProjectID.Eq(auth.GetProjectId64(ctx)),
		expDao.State.Neq(model.ExpStateDraft),
		expDao.IsDeleted.Eq(model.NotDeleted),
	)
	if len(search) != 0 {
		name := fmt.Sprintf("%%%s%%", search)
		expId := cast.ToInt64(search)
		subWhere := expDao.Where(expDao.Name.Like(name)).Or(expDao.ID.Eq(expId))
		where = append(where, subWhere)
	}
	expList, err := expDao.WithContext(ctx).
		Where(where...).
		Find()
	if err != nil {
		return nil, err
	}
	groupDao := s.DS.Group
	expIdArr := make([]int64, 0, 1000)
	lo.ForEach(expList, func(item *model.Exp, index int) {
		expIdArr = append(expIdArr, item.ID)
	})
	groupList, err := groupDao.
		WithContext(ctx).
		Where(groupDao.IsDeleted.Eq(model.NotDeleted), groupDao.ExpID.In(expIdArr...)).
		Find()
	if err != nil {
		return nil, err
	}
	groupListStra := make(map[string][]*dto.StrategyNode, 20000)
	for _, group := range groupList {
		expId := cast.ToString(group.ExpID)
		label := cast.ToString(group.Name)
		value := cast.ToString(group.ID)
		if _, ok := groupListStra[expId]; ok {
			groupListStra[expId] = append(groupListStra[expId],
				&dto.StrategyNode{Label: label, Value: value})
		} else {
			groupListStra[expId] = make([]*dto.StrategyNode, 0, 100)
			groupListStra[expId] = append(groupListStra[expId],
				&dto.StrategyNode{Label: label, Value: value})
		}

	}
	ret := make([]*dto.StrategyNode, 0, 10000)
	for _, exp := range expList {
		expId := cast.ToString(exp.ID)
		label := cast.ToString(exp.Name)
		node := &dto.StrategyNode{
			Value:    expId,
			Label:    label,
			Children: make([]*dto.StrategyNode, 0),
		}
		if _, ok := groupListStra[expId]; ok {
			node.Children = groupListStra[expId]
		}
		ret = append(ret, node)
	}

	return ret, err
}

func (s *ExpService) GroupForStrategy(search string, expId int64, ctx *gin.Context) ([]map[string]any, error) {
	groupDao := s.DS.Group
	ret := make([]map[string]any, 0, 200)
	where := make([]gen.Condition, 0, 3)
	where = append(where,
		groupDao.IsDeleted.Eq(model.NotDeleted),
		groupDao.ExpID.Eq(expId),
	)
	if len(search) != 0 {
		name := fmt.Sprintf("%%%s%%", search)
		groupId := cast.ToInt64(search)
		subWhere := groupDao.Where(groupDao.Name.Like(name)).Or(groupDao.ID.Eq(groupId))
		where = append(where, subWhere)
	}
	err := groupDao.WithContext(ctx).
		Where(where...).
		Select(groupDao.ID.As("group_id"), groupDao.Name).
		Scan(&ret)
	if err != nil {
		return ret, err
	}
	return ret, err
}

func (s *ExpService) TagIdsToTagNames(tagIdsStr string) (tagNameStr string, err error) {
	if tagIdsStr == "" {
		return "", nil
	}
	tagIdStrArr := strings.Split(tagIdsStr, ",")
	tagIds := lo.Map(tagIdStrArr, func(item string, index int) int64 {
		return cast.ToInt64(item)
	})
	tagList, err := s.DS.Tag.Where(s.DS.Tag.ID.In(tagIds...)).Find()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return "", err
	}
	tagName := strings.Join(lo.Map(tagList, func(item *model.Tag, index int) string {
		return item.Name
	}), ",")
	return tagName, nil
}

func (s *ExpService) UserIdsToUserNames(uids any) (userNames []string, err error) {
	uq := s.QueryForBIv1.User
	users, _ := uq.
		Select(uq.ID, uq.Account, uq.Realname, uq.Email, uq.Telephone, uq.Description, uq.CreatedAt, uq.UpdatedAt).
		Find()
	userMap := lo.KeyBy(users, func(item *model.User) int32 { return item.ID })
	switch uids := uids.(type) {
	case []int32:
		for _, uid := range uids {
			if user, ok := userMap[uid]; ok {
				userNames = append(userNames, user.Realname)
			}
		}
	case int32:
		if user, ok := userMap[uids]; ok {
			userNames = append(userNames, user.Realname)
		}
	case string:
		uidStrArr := strings.Split(uids, ",")
		for _, uidStr := range uidStrArr {
			uidInt := cast.ToInt32(uidStr)
			if user, ok := userMap[uidInt]; ok {
				userNames = append(userNames, user.Realname)
			}
		}
	}
	return userNames, nil
}
func (s *ExpService) IsValidExpName(name string, ctx *gin.Context) bool {
	expDao := s.DS.Exp
	exp, err := expDao.WithContext(ctx).Where(expDao.IsDeleted.Eq(model.NotDeleted)).Where(expDao.Name.Eq(name)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return true
		}
		return false
	}
	if exp.ID > 0 {
		return false
	}
	return true

}
func (s *ExpService) isExpNameRepeat(expName string, originExp *model.Exp, ctx context.Context, tx *dao.Query) error {
	expDao := s.DS.Exp
	query := tx.Exp.
		WithContext(ctx).
		Where(expDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Where(expDao.IsDeleted.Eq(model.NotDeleted)).
		Where(expDao.Name.Eq(expName))
	if originExp != nil {
		query = query.Where(expDao.ID.Neq(originExp.ID))
	}
	exp, err := query.Take()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil
		}
		return err
	}
	if exp.ID > 0 {
		return base.NewBizError(errors.New("实验名称，已经存在，已有实验ID：" + cast.ToString(exp.ID)))
	}
	return nil
}
func (s *ExpService) verfiyExpName(expName string, originExp *model.Exp, ctx context.Context) error {
	expDao := s.DS.Exp
	query := expDao.
		WithContext(ctx).
		Where(expDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Where(expDao.IsDeleted.Eq(model.NotDeleted)).
		Where(expDao.Name.Eq(expName))
	if originExp != nil {
		query = query.Where(expDao.ID.Neq(originExp.ID))
	}
	exp, err := query.Take()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil
		}
		return err
	}
	if exp.ID > 0 {
		return base.NewBizError(errors.New("实验名称，已经存在，已有实验ID：" + cast.ToString(exp.ID)))
	}
	return nil
}
func (s *ExpService) GetDetail(expId int64, ctx *gin.Context) (map[string]any, error) {
	expDao := s.DS.Exp
	groupDao := s.DS.Group
	layerDao := s.DS.Layer
	batchDao := s.DS.Batch
	paramsTypeDao := s.DS.ParamsType
	var err error
	result := make(map[string]interface{}, 3)
	expInfoDetail := &response.ExpDetail{}
	expInfo, err := expDao.Debug().Where(expDao.ProjectID.Eq(auth.GetProjectId64(ctx))).Where(expDao.ID.Eq(expId)).Take()
	if err != nil {
		return nil, err
	}
	layerInfo := &model.Layer{}
	if expInfo.LayerID > 0 {
		layerInfo, _ = layerDao.Where(layerDao.ID.Eq(expInfo.LayerID)).Take()
	}
	batchInfo := &model.Batch{}
	if expInfo.BatchID > 0 {
		batchInfo, _ = batchDao.Where(batchDao.ID.Eq(expInfo.BatchID)).Take()
	}
	expInfoDetail.Id = expInfo.ID
	expInfoDetail.Name = expInfo.Name
	expInfoDetail.GroupLimitHour = expInfo.GroupLimitHour
	expInfoDetail.Uid = expInfo.UID
	expInfoDetail.LayerId = expInfo.LayerID
	expInfoDetail.LayerName = layerInfo.Name
	expInfoDetail.LayerRate = expInfo.LayerRate
	expInfoDetail.BatchId = batchInfo.ID
	expInfoDetail.BatchName = batchInfo.Name
	expInfoDetail.State = expInfo.State
	expInfoDetail.StateName = model.ExpStateMap[int(expInfo.State)]
	expInfoDetail.ExpType = expInfo.ExpType
	expInfoDetail.ExpTypeName = model.ExpTypeMap[int(expInfo.ExpType)]
	expInfoDetail.Desc = expInfo.Desc
	if expInfo.StartTime != nil {
		expInfoDetail.StartTime = cast.ToTime(expInfo.StartTime).Format("2006-01-02 15:04:05")
		expInfoDetail.EndTime = cast.ToTime(expInfo.EndTime).Format("2006-01-02 15:04:05")
	}
	expInfoDetail.TagId = expInfo.TagID
	expInfoDetail.Step = expInfo.Step
	expInfoDetail.Owner = expInfo.Owner
	expInfoDetail.Days = expInfo.Days
	expInfoDetail.Strategy = expInfo.Strategy
	expInfoDetail.Csr = expInfo.Csr
	expInfoDetail.Version = expInfo.Version
	expInfoDetail.GroupLimitHour = expInfo.GroupLimitHour
	if expInfoDetail.State == model.ExpStateDraft {
		expInfoDetail.NextStep = expInfoDetail.Step + 1
	}
	approvalTask, err := s.DS.ApprovalTask.
		Where(s.DS.ApprovalTask.ExpID.Eq(expInfo.ID)).
		Where(s.DS.ApprovalTask.State.Eq(model.ApprovalInit)).
		Take()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	if approvalTask != nil {
		expInfoDetail.ApprovalId = approvalTask.ID
		expInfoDetail.ApprovalUid = approvalTask.CreatorID
	}
	// 实验所属人
	userNames, _ := s.UserIdsToUserNames(expInfoDetail.Owner)
	expInfoDetail.OwnerName = strings.Join(userNames, ",")
	// 实验标签
	tagName, _ := s.TagIdsToTagNames(expInfoDetail.TagId)
	expInfoDetail.TagName = tagName
	isApprovalRunning, err := s.ApprovalTaskManageService.IsExpApprovalRunning(ctx, expInfo.ID)
	if err != nil {
		return nil, err
	}
	expInfoDetail.IsApprovalRunning = isApprovalRunning
	// 获取实验下的实验组（方案）数据
	groupList := make([]*response.Group, 0, 10)
	groupSelect := []field.Expr{
		groupDao.ID.As("group_id"), groupDao.Name, batchDao.Name.As("batch_name"),
		groupDao.BatchID, groupDao.GroupType, groupDao.Desc,
		paramsTypeDao.Name.As("params_name"), groupDao.ParamsContent, groupDao.IsProgress,
		groupDao.State, groupDao.IsDeleted, groupDao.WhiteList,
	}
	err = s.DS.WithContext(ctx).Group.Debug().
		LeftJoin(batchDao, groupDao.BatchID.EqCol(batchDao.ID)).
		Where(groupDao.ExpID.Eq(expId)).Where(groupDao.IsDeleted.Eq(model.NotDeleted)).
		LeftJoin(paramsTypeDao, groupDao.ParamsType.EqCol(paramsTypeDao.ID)).
		Order(groupDao.ID.Asc()).
		Select(groupSelect...).
		Scan(&groupList)
	if err != nil {
		return nil, err
	}
	expRunTime, _ := getDaysHours(expInfoDetail.StartTime, expInfoDetail.EndTime)
	expInfoDetail.ExpRunTime = expRunTime
	if expInfo.StartTime != nil {
		expInfoDetail.StartTime = cast.ToTime(expInfo.StartTime).Format("2006-01-02 15:04:05")
	}
	if expInfo.EndTime != nil {
		expInfoDetail.EndTime = cast.ToTime(expInfo.EndTime).Format("2006-01-02 15:04:05")
	}
	// 获取同层其他的实验信息
	expWithLayer := make([]*response.ExpList, 0, 10)
	expWithLayerSelect := []field.Expr{
		expDao.ID, expDao.Name.As("exp_name"), expDao.LayerRate,
	}
	err = s.DS.WithContext(ctx).Exp.Debug().
		Where(expDao.LayerID.Eq(expInfoDetail.LayerId)).
		Where(expDao.State.In(model.ExpStateRunning, model.ExpStateGrayRunning, model.ExpStateTodo)).
		Where(expDao.IsDeleted.Eq(model.NotDeleted)).
		Where(expDao.ID.Neq(expInfoDetail.Id)).
		Select(expWithLayerSelect...).
		Scan(&expWithLayer)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	// 获取运行中或者灰度运行中的的实验组数量
	groupRunningCount := 0
	groupIds := make([]int64, 0, len(groupList))
	for _, group := range groupList {
		if group.State == model.ExpStateRunning || group.State == model.ExpStateGrayRunning {
			groupRunningCount++
		}
		groupIds = append(groupIds, group.GroupId)
	}

	// 获取实验组（方案）的进组人数
	groupsCountMap, err := s.MetricService.GetGrpUserCount(ctx, groupIds)
	groupsCurrentCountMap, err := s.MetricService.RealtimeGetGroupCount(ctx, groupIds)
	if err != nil {
		return nil, err
	}
	for _, group := range groupList {
		group.GroupTypeName = model.GroupTypeMap[group.GroupType]
		group.UserCount = groupsCountMap[group.GroupId]
		group.UserCurrentCount = groupsCurrentCountMap[group.GroupId]
	}

	// 获取实验的总人数
	var totalUser, totalCurrentUser int64
	expUserMap, err := s.MetricService.GetExpUserCount(ctx, []int64{expId})
	groupsCurrentUserMap, err := s.MetricService.RealtimeGetGroupCount(ctx, groupIds)
	if err != nil {
		return nil, err
	}
	if _, ok := expUserMap[expId]; ok {
		totalUser = expUserMap[expId]
	}
	for _, cnt := range groupsCurrentUserMap {
		totalCurrentUser += cnt
	}

	result["exp_info"] = expInfoDetail
	result["exp_with_layer"] = expWithLayer
	result["groups"] = groupList
	result["total_user"] = totalUser
	result["total_current_user"] = totalCurrentUser
	result["group_count"] = groupRunningCount
	return result, err
}
func (s *ExpService) CanSplitExpList(req request.SplitExpReq, ctx *gin.Context) ([]*response.ExpList, error) {
	expDao := s.DS.Exp
	layerDao := s.DS.Layer
	first, err := expDao.
		Where(expDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		WithContext(ctx).Where(expDao.ID.Eq(req.ExpId)).
		First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, base.NewBizError(errors.New("实验不存在，请求不合法"))
		} else {
			return nil, err
		}
	}
	myExpList := make([]*response.ExpList, 0)
	err = s.DS.WithContext(ctx).Exp.
		Select(expDao.ID.As("exp_id"), expDao.Name.As("exp_name"), expDao.LayerRate, expDao.State).
		Where(expDao.LayerID.Eq(first.LayerID)).
		Where(expDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Where(expDao.State.In(model.ExpStateRunning, model.ExpStateRunningNotProgress)).
		Where(expDao.ID.Neq(req.ExpId)).
		Where(expDao.IsDeleted.Eq(model.NotDeleted)).
		Scan(&myExpList)
	if err != nil {
		return nil, err
	}

	expList := make([]*response.ExpList, 0)
	layer, _ := layerDao.
		Where(layerDao.ID.In(first.LayerID)).
		Where(layerDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		First()

	layerInfos, _ := layerDao.
		Where(layerDao.State.Eq(model.StateOnline)).
		Where(layerDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Where(layerDao.ID.Neq(layer.ID)).
		Where(layerDao.IsExclusion.Neq(layer.IsExclusion)).
		Find()
	layerIds := make([]int64, 0, len(layerInfos))
	for _, layerInfo := range layerInfos {
		layerIds = append(layerIds, layerInfo.ID)
	}

	err = s.DS.WithContext(ctx).Exp.Debug().
		Where(expDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Select(expDao.ID.As("exp_id"), expDao.Name.As("exp_name"), expDao.LayerRate, expDao.State).
		Where(expDao.State.In(model.ExpStateRunning, model.ExpStateRunningNotProgress)).
		Where(expDao.LayerID.In(layerIds...)).
		Where(expDao.IsDeleted.Eq(model.NotDeleted)).
		Scan(&expList)
	if err != nil {
		return nil, err
	}
	myExpList = append(myExpList, expList...)
	return myExpList, nil
}
func (s *ExpService) CanSplitGroupsList(req request.SplitGroupReq, ctx *gin.Context) (any, error) {
	expDao := s.DS.Exp
	groupDao := s.DS.Group
	_, err := expDao.Where(expDao.ID.Eq(req.ExpId)).First()
	if err != nil {
		return nil, err
	}
	where := make([]gen.Condition, 0, 2)
	if req.Operation == model.PlanOperationTypeClose && req.Type == 1 {
		where = append(where, groupDao.State.In(model.GroupStateRunning))
	} else {
		where = append(where, groupDao.State.In(model.GroupStateRunning, model.GroupStateRunningNotProgress))
	}
	groups := make([]*response.Group, 0)
	groupSelect := make([]field.Expr, 0, 10)
	groupSelect = append(groupSelect, groupDao.ID.As("group_id"),
		groupDao.Name, groupDao.BatchID, groupDao.GroupType,
		groupDao.Desc, groupDao.ParamsType, groupDao.ParamsContent,
		groupDao.IsProgress, groupDao.State, groupDao.IsDeleted)
	err = groupDao.WithContext(ctx).
		Where(where...).
		Select(groupSelect...).
		Where(groupDao.ExpID.Eq(req.ExpId)).
		Where(groupDao.IsDeleted.Eq(model.NotDeleted)).
		Scan(&groups)
	if err != nil {
		return nil, err
	}
	return groups, nil
}

func (s *ExpService) SaveBaseV2(ctx context.Context, exp *model.Exp, query *dao.Query) (*model.Exp, error) {
	ownerCount := strings.Count(exp.Owner, ",")
	if ownerCount > 9 {
		return exp, errors.New("实验拥有者不能超过10个")
	}
	var originExp *model.Exp
	var err error
	originExp, err = s.isValidExpV2(exp.ID, exp.Version, ctx, query)
	if err != nil {
		return exp, err
	}
	err = s.isExpNameRepeat(exp.Name, originExp, ctx, query)

	exp.UID = cast.ToInt32(auth.GetUserId(ctx))
	exp.ProjectID = auth.GetProjectId64(ctx)
	err = query.Transaction(func(tx *dao.Query) error {
		if originExp != nil {
			_, err = tx.WithContext(ctx).Exp.Select(
				tx.Exp.ProjectID,
				tx.Exp.Name,
				tx.Exp.ExpType,
				tx.Exp.TagID,
				tx.Exp.BatchID,
				tx.Exp.Days,
				tx.Exp.UID,
				tx.Exp.Owner,
				tx.Exp.Desc,
				tx.Exp.Version,
				tx.Exp.UpdateTime,
			).Where(tx.Exp.ID.Eq(exp.ID)).Updates(exp)
		} else {
			err = tx.WithContext(ctx).Exp.Create(exp)
		}

		if err != nil {
			return err
		}
		var group *model.Group
		if originExp != nil && exp.BatchID != originExp.BatchID {
			group, err = tx.Group.Where(tx.Group.ExpID.Eq(exp.ID)).Where(tx.Group.IsDeleted.Eq(model.NotDeleted)).Take()
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return err
			}
			if group == nil {
				return nil
			}
			_, err = tx.Group.WithContext(ctx).Where(tx.Group.ExpID.Eq(exp.ID)).Update(tx.Group.BatchID, exp.BatchID)
			if err != nil {
				return err
			}
		}
		return nil
	})
	return exp, err
}

// 实验基本信息添加
func (s *ExpService) SaveBase(req request.AddExpBaseReq, ctx *gin.Context) (*model.Exp, error) {
	expParam := &model.Exp{}
	err := copier.Copy(expParam, req)
	if err != nil {
		return nil, err
	}
	expParam.ID = req.ExpId
	expParam.UID = int32(auth.GetUserId(ctx))
	expParam.ProjectID = auth.GetProjectId64(ctx)
	expParam.Version = req.Version
	// 判断实验是否在审批中
	isApprovalRunning, err := s.ApprovalTaskManageService.IsExpApprovalRunning(ctx, expParam.ID)
	if err != nil {
		return nil, err
	}
	if isApprovalRunning {
		return nil, base.NewAlertTip("实验正在审批，不允许修改")
	}
	exp, err := s.SaveBaseV2(ctx, expParam, s.DS)
	return exp, err
}

func (s *ExpService) ExpWithLayer(id int64) ([]*model.Exp, error) {
	expDao := s.DS.Exp
	expList, err := s.DS.Exp.Where(expDao.IsDeleted.Eq(2)).Where(expDao.ID.Eq(id)).Find()
	return expList, err
}

func (s *ExpService) SaveStrategyV2(ctx context.Context, exp *model.Exp, query *dao.Query) (*model.Exp, error) {
	var originExp *model.Exp
	var err error
	if originExp, err = s.isValidExpV2(exp.ID, exp.Version, ctx, query); err != nil {
		return exp, err
	}

	err = s.LayerService.AvailableRate(exp.LayerID, exp.ID, exp.LayerRate)
	if err != nil {
		return exp, err
	}

	if err = strategyValid(exp.Strategy, exp.Csr); err != nil {
		return exp, err
	}

	expDao := s.DS.Exp
	err = query.Transaction(func(tx *dao.Query) error {
		_, err = tx.WithContext(ctx).Exp.
			Select(
				expDao.LayerID,
				expDao.LayerRate,
				expDao.Step,
				expDao.Csr,
				expDao.Strategy,
				expDao.Version,
				expDao.GroupLimitHour,
			).Updates(exp)
		return err
	})
	if err != nil {
		return exp, err
	}
	if originExp.LayerID != 0 && originExp.LayerID != exp.LayerID {
		// 当实验的层发生变化时，需要更新该层下的实验冲突信息
		err = s.ProjectService.RemoveLayerExpFeature(ctx, originExp, nil)
		if err != nil {
			return exp, err
		}
	}
	return exp, err
}

// SaveStrategy 保存策略
func (s *ExpService) SaveStrategy(ctx *gin.Context, req request.AddExpStrategyReq) (*model.Exp, error) {
	exp := &model.Exp{}
	err := copier.Copy(exp, req)
	if err != nil {
		return nil, err
	}
	exp.ID = req.ExpId
	// 判断是否在审批中
	isApprovalRunning, err := s.ApprovalTaskManageService.IsExpApprovalRunning(ctx, exp.ID)
	if err != nil {
		return exp, err
	}
	if isApprovalRunning {
		return exp, base.NewAlertTip("实验正在审批中，不允许修改")
	}
	exp, err = s.SaveStrategyV2(ctx, exp, s.DS)
	return exp, err
}

// strategyValid 对策略&分流进行校验
func strategyValid(strategy *model.Strategy, csr []*model.CSRData) error {
	if strategy == nil && len(csr) == 0 {
		return nil
	}

	if strategy != nil {
		if strategy.TriggerUserKey != nil {
			ok, err := IsValid(strategy.TriggerUserKey)
			if err != nil {
				return base.NewAlertTip(fmt.Sprintf("触发用户规则验证失败, %s", err.Error()))
			}
			if !ok {
				return base.NewAlertTip("触发用户规则验证失败")
			}
		}

		if strategy.TriggerEndKey != nil {
			ok, err := IsValid(strategy.TriggerEndKey)
			if err != nil {
				return base.NewAlertTip(fmt.Sprintf("关闭触发规则验证失败, %s", err.Error()))
			}
			if !ok {
				return base.NewAlertTip("关闭触发规则验证失败")
			}
		}

		if strategy.TriggerStopKey != nil {
			ok, err := IsValid(strategy.TriggerStopKey)
			if err != nil {
				return base.NewAlertTip(fmt.Sprintf("停止进量触发规则验证失败, %s", err.Error()))
			}
			if !ok {
				return base.NewAlertTip("停止进量触发规则验证失败")
			}
		}
	}

	if len(csr) > 0 {
		ok, err := IsValidCSR(csr)
		if err != nil {
			return base.NewAlertTip(fmt.Sprintf("分流用户群划分规则验证失败, %s", err.Error()))
		}
		if !ok {
			return base.NewAlertTip("分流用户群划分规则验证失败")
		}
	}

	return nil
}
func (s *ExpService) isValidGroupV2(ctx context.Context, group []*model.Group) error {
	isRepeat := make(map[string]bool, 50)
	projectDao := s.DS.Project
	project, err := projectDao.Where(projectDao.ID.Eq(auth.GetProjectId64(ctx))).Take()
	if err != nil {
		return err
	}
	var compareGroup, expGroup int
	for _, g := range group {
		if g.GroupType == model.GroupTypeCompare {
			compareGroup++
		}
		if g.GroupType == model.GroupTypeExp {
			expGroup++
		}
		if isRepeat[g.Name] {
			return errors.New("实验组名称，出现重复")
		}
		err = project.Data.GroupParamStyle.Processor().Valid(g.ParamsContent)
		if err != nil {
			return err
		}
		// 重复行，校验
		isRepeat[g.Name] = true
	}
	if compareGroup < 1 {
		return errors.New("对照组，至少有一个")
	}
	if expGroup < 1 {
		return errors.New("实验组，至少有一个")
	}
	return nil
}

func (s *ExpService) isValidGroup(ctx context.Context, group []*request.Group) error {
	isRepeat := make(map[string]bool, 50)
	projectDao := s.DS.Project
	project, err := projectDao.Where(projectDao.ID.Eq(auth.GetProjectId64(ctx))).Take()
	if err != nil {
		return err
	}
	var compareGroup, expGroup int
	for _, g := range group {
		if g.GroupType == model.GroupTypeCompare {
			compareGroup++
		}
		if g.GroupType == model.GroupTypeExp {
			expGroup++
		}
		if isRepeat[g.Name] {
			return errors.New("实验组名称，出现重复")
		}
		err = project.Data.GroupParamStyle.Processor().Valid(g.ParamsContent)
		if err != nil {
			return err
		}
		// 重复行，校验
		isRepeat[g.Name] = true
	}
	if compareGroup < 1 {
		return errors.New("对照组，至少有一个")
	}
	if expGroup < 1 {
		return errors.New("实验组，至少有一个")
	}
	return nil
}
func (s *ExpService) SaveGroupsV2(ctx context.Context, exp *model.Exp, ignore bool, query *dao.Query) (*model.Exp, []*response.ConflictGroup, error) {
	var err error
	ret := make(map[string]any)
	var originExp *model.Exp
	if originExp, err = s.isValidExpV2(exp.ID, exp.Version, ctx, query); err != nil {
		return exp, nil, err
	}
	err = s.isValidGroupV2(ctx, exp.Groups)
	if err != nil {
		return exp, nil, err
	}
	// 转换请求数据为模型
	addGroups, updateGroups, deleteGroupIds, err := s.handelGroupModel(ctx, exp.Groups, originExp)
	if err != nil {
		return exp, nil, err
	}
	// 检验feature冲突
	allGroups := append(addGroups, updateGroups...)
	conflictGroups, err := s.ProjectService.GetConflictGroups(ctx, originExp, allGroups)
	if err != nil {
		return exp, nil, err
	}

	if len(conflictGroups) > 0 && !ignore {
		ret["conflict"] = conflictGroups
		return exp, conflictGroups, nil
	}
	// 记录上传实验方案入参
	slog.InfoContext(ctx, "save_groups", "exp", exp, "addGroups", addGroups, "updateGroups", updateGroups, "deleteGroupIds", deleteGroupIds)

	// 执行事务
	err = s.executeGroupTransaction(ctx, exp, addGroups, updateGroups, deleteGroupIds, query)
	if err != nil {
		return exp, conflictGroups, err
	}
	// 待启动下，修改方案情况，不点到第四步
	if originExp.State == model.ExpStateTodo {
		err = s.ProjectService.SaveProjectFeatureStats(ctx, originExp)
		if err != nil {
			return exp, conflictGroups, err
		}
	}
	return exp, conflictGroups, err
}

// 执行事务操作
func (s *ExpService) executeGroupTransaction(ctx context.Context, exp *model.Exp, addGroups, updateGroups []*model.Group, deleteGroupIds []int64, query *dao.Query) error {
	return query.Transaction(func(tx *dao.Query) error {
		if _, err := tx.WithContext(ctx).Exp.Where(tx.Exp.ID.Eq(exp.ID)).Select(tx.Exp.Step, tx.Exp.Version, tx.Exp.UpdateTime).Updates(exp); err != nil {
			return err
		}

		if len(deleteGroupIds) > 0 {
			if _, err := tx.WithContext(ctx).Group.Debug().
				Where(tx.Group.ExpID.Eq(exp.ID)).
				Where(tx.Group.ID.In(deleteGroupIds...)).
				Update(tx.Group.IsDeleted, model.Deleted); err != nil {
				return err
			}
		}

		if len(addGroups) > 0 {
			if err := tx.WithContext(ctx).Group.Debug().CreateInBatches(addGroups, len(addGroups)); err != nil {
				return err
			}
		}

		for _, ug := range updateGroups {
			if _, err := tx.WithContext(ctx).Group.Debug().
				Where(tx.Group.ID.Eq(ug.ID)).
				Where(tx.Group.ExpID.Eq(ug.ExpID)).
				Updates(ug); err != nil {
				return err
			}
		}

		return nil
	})
}

// 将请求数据转换为模型
// addGroups, updateGroups, deleteGroupIds
func (s *ExpService) handelGroupModel(ctx context.Context, groups []*model.Group, originExp *model.Exp) (addGroups []*model.Group, updateGroups []*model.Group, deleteGroupIds []int64, err error) {
	originGroups, _ := s.DS.WithContext(ctx).Group.Select(s.DS.Group.ID, s.DS.Group.ExpID).
		Where(s.DS.Group.ExpID.Eq(originExp.ID)).
		Where(s.DS.Group.IsDeleted.Eq(model.NotDeleted)).
		Find()

	updateGroupMap := make(map[int64]bool)
	for _, group := range groups {
		newGroup := &model.Group{}
		if err := copier.Copy(newGroup, group); err != nil {
			return nil, nil, nil, err
		}
		// todo 实验->方案的状态流转
		newGroup.State = originExp.State

		newGroup.BatchID = originExp.BatchID
		if batch, _ := s.DS.Batch.Where(s.DS.Batch.ID.Eq(originExp.BatchID)).First(); batch != nil {
			newGroup.Data.BatchName = batch.Name
		}

		if group.ID != 0 {
			updateGroups = append(updateGroups, newGroup)
			updateGroupMap[group.ID] = true
		} else {
			newGroup.ExpID = group.ExpID
			addGroups = append(addGroups, newGroup)
		}
	}

	for _, group := range originGroups {
		if !updateGroupMap[group.ID] {
			deleteGroupIds = append(deleteGroupIds, group.ID)
		}
	}

	return addGroups, updateGroups, deleteGroupIds, nil
}

func (s *ExpService) SaveGroups(req request.AddGroupReq, ctx *gin.Context) (*response.ExpGroup, error) {
	exp := &model.Exp{}
	err := copier.Copy(exp, req)
	if err != nil {
		return nil, err
	}
	exp.ID = req.ExpId
	convert := func(group *request.Group) (*model.Group, error) {
		g := &model.Group{}
		err = copier.Copy(g, group)
		if err != nil {
			return nil, err
		}
		g.ID = group.GroupId
		return g, nil
	}
	var groups []*model.Group
	for _, group := range req.Groups {
		g, err := convert(group)
		if err != nil {
			return nil, err
		}
		groups = append(groups, g)
	}
	// 判断是否在审批中
	isApprovalRunning, err := s.ApprovalTaskManageService.IsExpApprovalRunning(ctx, exp.ID)
	if err != nil {
		return nil, err
	}
	if isApprovalRunning {
		return nil, base.NewAlertTip("实验正在审批中，不允许修改")
	}
	exp.Groups = groups
	exp, conflictGroups, err := s.SaveGroupsV2(ctx, exp, req.Ignore, s.DS)
	if err != nil {
		return nil, err
	}
	return &response.ExpGroup{
		ExpId:    exp.ID,
		Version:  exp.Version,
		Conflict: conflictGroups,
	}, nil
}
func (s *ExpService) SetWhite(ctx *gin.Context, req *request.WhiteReq) (any, error) {
	groupDao := s.DS.Group
	groupInfo, err := groupDao.WithContext(ctx).
		Where(groupDao.ID.Eq(req.GroupId)).
		Where(groupDao.State.NotIn(model.GroupStateClose, model.GroupStateDraft)).
		Take()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("方案不存在，group_id 不合法")
		}
		return nil, err
	}
	req.White = strings.ReplaceAll(req.White, "，", ",")
	err = s.isValidWhiteList(ctx, groupInfo, req.White)
	if err != nil {
		return nil, err
	}
	groupInfo.WhiteList = req.White
	groupInfo.UpdateTime = time.Now()
	_, err = groupDao.WithContext(ctx).Select(groupDao.WhiteList, groupDao.UpdateTime).Debug().Where(groupDao.ID.Eq(req.GroupId)).Updates(groupInfo)
	if err != nil {
		return nil, err
	}
	return nil, nil
}

func (s *ExpService) isValidWhiteList(ctx context.Context, group *model.Group, white string) error {
	expDao := s.DS.Exp
	groupDao := s.DS.Group
	if len(white) == 0 {
		return nil
	}
	expInfo, err := expDao.WithContext(ctx).
		Where(expDao.ID.Eq(group.ExpID)).
		Where(expDao.State.NotIn(model.GroupStateClose, model.GroupStateDraft)).
		Where(expDao.IsDeleted.Eq(model.NotDeleted)).
		Take()
	if err != nil {
		return err
	}
	expList, err := expDao.WithContext(ctx).
		Where(expDao.LayerID.Eq(expInfo.LayerID)).
		Where(expDao.IsDeleted.Eq(model.NotDeleted)).
		Where(expDao.State.NotIn(model.ExpStateClose, model.ExpStateDraft)).
		Find()
	if err != nil {
		return err
	}
	if len(expList) == 0 {
		return nil
	}
	expListMap := lo.KeyBy(expList, func(exp *model.Exp) int64 {
		return exp.ID
	})
	expIds := lo.Map(expList, func(exp *model.Exp, index int) int64 {
		return exp.ID
	})
	diffGroupList, err := groupDao.
		Where(groupDao.ExpID.In(expIds...)).
		Where(groupDao.ID.NotIn(group.ID)).
		Where(groupDao.WhiteList.Neq("")).
		Where(groupDao.State.NotIn(model.GroupStateClose, model.GroupStateDraft)).
		Find()
	if err != nil {
		return err
	}
	if len(diffGroupList) == 0 {
		return nil
	}
	diffGroupListMap := lo.KeyBy(diffGroupList, func(group *model.Group) int64 {
		return group.ID
	})
	diffWhiteMap := make(map[string]int64, len(diffGroupList))
	for _, diffGroup := range diffGroupList {
		if diffGroup.WhiteList == "" {
			continue
		}
		diffWhiteListStr := strings.ReplaceAll(diffGroup.WhiteList, "\n", "")
		diffWhiteList := strings.Split(strings.ReplaceAll(diffWhiteListStr, "，", ","), ",")
		if len(diffWhiteList) == 0 {
			continue
		}
		for _, disId := range diffWhiteList {
			diffWhiteMap[disId] = diffGroup.ID
		}
	}
	whiteStr := strings.ReplaceAll(white, "\n", "")
	whiteList := strings.Split(whiteStr, ",")
	for _, disId := range whiteList {
		if disId == "" {
			continue
		}
		diffWhiteGroupId, ok := diffWhiteMap[disId]
		if !ok {
			continue
		}
		diffGroupInfo, ok := diffGroupListMap[diffWhiteGroupId]
		if !ok {
			continue
		}
		diffExpInfo, ok := expListMap[diffGroupInfo.ExpID]
		if ok {
			return errors.New(fmt.Sprintf("用户ID【%s】在实验【%s】方案【%s】已配置，请先删除后再配置", disId, diffExpInfo.Name, diffGroupInfo.Name))
		}
	}
	return nil
}
func (s *ExpService) isValidExpV2(expId int64, version int64, ctx context.Context, query *dao.Query) (*model.Exp, error) {
	// 新增实验，没有exp_id,不做版本校验
	if expId == 0 {
		return nil, nil
	}
	expDao := s.DS.Exp
	exp, err := query.WithContext(ctx).
		Exp.
		Where(expDao.IsDeleted.Eq(model.NotDeleted)).
		Where(expDao.ID.Eq(expId)).
		Where(expDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Take()
	if err != nil {
		return nil, err
	}
	// todo 后边会添加强校验
	if exp.Version > version {
		return exp, base.NewBizError(errors.New("实验版本，已不是最新状态，请刷新页面重试"))
	}
	if auth.IsAdmin(ctx) {
		return exp, nil
	}
	if exp.State == model.ExpStateDraft {
		if exp.UID != cast.ToInt32(auth.GetUserId(ctx)) {
			return nil, base.NewBizError(errors.New("实验草稿状态，只有创建者可以操作"))
		}
		return exp, nil
	}
	ownerArr := strings.Split(exp.Owner, ",")
	if exp.UID == cast.ToInt32(auth.GetUserId(ctx)) || lo.Contains(ownerArr, cast.ToString(auth.GetUserId(ctx))) {
		return exp, nil
	}
	if !auth.CanName(ctx, "abtest.exp.edit") {
		return nil, base.NewBizError(errors.New("没有实验编辑权限"))
	}

	return exp, nil
}
func (s *ExpService) isValidExp(expId int64, version int64, ctx context.Context) (*model.Exp, error) {
	// 新增实验，没有exp_id,不做版本校验
	if expId == 0 {
		return nil, nil
	}
	expDao := s.DS.Exp
	exp, err := s.DS.WithContext(ctx).
		Exp.
		Preload(expDao.Groups).
		Where(expDao.IsDeleted.Eq(model.NotDeleted)).
		Where(expDao.ID.Eq(expId)).
		Where(expDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Take()
	if err != nil {
		return nil, err
	}
	// todo 后边会添加强校验
	if exp.State != model.ExpStateDraft && exp.Version > version {
		return exp, base.NewBizError(errors.New("实验版本，已不是最新状态，请刷新页面重试"))
	}
	if auth.IsAdmin(ctx) {
		return exp, nil
	}
	if exp.State == model.ExpStateDraft {
		if exp.UID != cast.ToInt32(auth.GetUserId(ctx)) {
			return nil, base.NewBizError(errors.New("实验草稿状态，只有创建者可以操作"))
		}
		return exp, nil
	}
	ownerArr := strings.Split(exp.Owner, ",")
	if exp.UID == cast.ToInt32(auth.GetUserId(ctx)) || lo.Contains(ownerArr, cast.ToString(auth.GetUserId(ctx))) {
		return exp, nil
	}
	if !auth.CanName(ctx, "abtest.exp.edit") {
		return nil, base.NewBizError(errors.New("没有实验编辑权限"))
	}

	return exp, nil
}
func (s *ExpService) SaveStatus(ctx context.Context, exp *model.Exp, query *dao.Query) (*model.Exp, error) {
	var originExp *model.Exp
	var err error
	if originExp, err = s.isValidExpV2(exp.ID, exp.Version, ctx, query); err != nil {
		return exp, err
	}

	expDao := s.DS.Exp
	groupDao := s.DS.Group
	err = s.LayerService.AvailableRate(originExp.LayerID, originExp.ID, originExp.LayerRate)
	if err != nil {
		return exp, err
	}
	exp.State = model.ExpStateTodo
	err = query.Transaction(func(tx *dao.Query) error {
		_, err = tx.WithContext(ctx).Exp.Select(tx.Exp.Step, tx.Exp.State, tx.Exp.Version, tx.Exp.UpdateTime).Where(expDao.ID.Eq(exp.ID)).Updates(exp)
		if err != nil {
			return err
		}
		_, err = tx.WithContext(ctx).Group.
			Where(groupDao.IsDeleted.Eq(model.NotDeleted)).
			Where(groupDao.ExpID.Eq(originExp.ID)).Update(tx.Group.State, model.GroupStateTodo)
		if err != nil {
			return err
		}

		_ = s.Events.ExpUpdated.Push(ctx, ExpUpdatedEvent{ExpID: exp.ID})

		return err
	})

	if err != nil {
		return exp, err
	}
	// 处理全部分组的特性，新增和修改
	err = s.ProjectService.SaveProjectFeatureStats(ctx, originExp)
	if err != nil {
		return exp, err
	}
	return exp, err
}
func (s *ExpService) Save(req *request.AddExpReq, ctx *gin.Context) (*model.Exp, error) {
	exp := &model.Exp{}
	err := copier.Copy(exp, req)
	if err != nil {
		return nil, err
	}
	exp.ID = req.ExpId
	exp.Groups = nil
	// 判断实验是否在审批中
	isApprovalRunning, err := s.ApprovalTaskManageService.IsExpApprovalRunning(ctx, exp.ID)
	if err != nil {
		return exp, err
	}
	if isApprovalRunning {
		return nil, base.NewAlertTip("实验正在审批中，不允许修改")
	}
	return s.SaveStatus(ctx, exp, s.DS)
}
func (s *ExpService) EditSave(req *request.EditSaveExpReq, ctx *gin.Context) (any, error) {
	var err error
	ret := make(map[string]any)
	ret["conflict"] = []any{}
	ret["is_approval"] = false
	var originExp *model.Exp
	if originExp, err = s.isValidExp(req.ExpInfo.Id, req.ExpInfo.Version, ctx); err != nil {
		return ret, err
	}

	// 对策略&分流进行校验
	if err = strategyValid(req.ExpInfo.Strategy, req.ExpInfo.Csr); err != nil {
		return ret, err
	}
	var layerRate int32
	if originExp.State != model.ExpStateRunningNotProgress {
		layerRate = req.ExpInfo.LayerRate
	}
	// 对实验流量是否可用进行校验
	if err = s.LayerService.AvailableRate(originExp.LayerID, originExp.ID, layerRate); err != nil {
		return ret, err
	}
	ownerCount := strings.Count(req.ExpInfo.Owner, ",")
	if ownerCount > 9 {
		return ret, base.NewBizError(errors.New("实验拥有者不能超过10个"))
	}
	project, err := s.ProjectService.DetailCtx(ctx)
	if err != nil {
		return ret, err
	}

	groups, err := s.GroupService.QueryGroupByExpId(ctx, originExp.ID)
	groupMap := lo.KeyBy(groups, func(group *model.Group) int64 { return group.ID })

	now := time.Now()
	addGroups := make([]*model.Group, 0, len(req.Groups))
	updateGroups := make([]*model.Group, 0, len(req.Groups))
	unchangedGroups := make([]*model.Group, 0, len(req.Groups))
	isRepeat := make(map[string]bool, 50)
	for _, group := range req.Groups {
		if isRepeat[group.Name] {
			return ret, base.NewBizError(errors.New("实验组名称，出现重复"))
		}
		isRepeat[group.Name] = true

		if project.Data.GroupParamStyle.Processor().Valid(group.ParamsContent) != nil {
			return ret, base.NewBizError(errors.New("实验组参数，出现不合法，应为json格式"))
		}

		groupModel := &model.Group{}
		groupModel.UpdateTime = now
		if group.GroupId == 0 {
			groupModel.CreateTime = now
			err = copier.Copy(groupModel, group)
			if err != nil {
				return ret, err
			}
			groupModel.ExpID = req.ExpInfo.Id
			groupModel.State = model.GroupStateRunningNotProgress
			if group.IsProgress == 1 {
				groupModel.State = model.GroupStateRunning
			}
			addGroups = append(addGroups, groupModel)
		} else {
			originGroup := groupMap[group.GroupId]
			if originGroup.State == group.State && originGroup.BatchID == group.BatchId {
				// 数据没有变化则不需要更新，目前只有状态一个值会发生变更
				unchangedGroups = append(unchangedGroups, originGroup)
				continue
			}
			groupModel.ExpID = req.ExpInfo.Id
			groupModel.ID = group.GroupId
			err = copier.Copy(groupModel, group)
			if err != nil {
				return ret, err
			}
			groupModel.State = group.State
			updateGroups = append(updateGroups, groupModel)
		}

		groupModel.Data.BatchName = group.BatchName
		if model.IsFreezed(groupModel.State) {
			groupModel.Data.FreezedAt = now
		}
		if model.IsClosed(groupModel.State) {
			groupModel.Data.ClosedAt = now
		}
	}
	closeGroupsIds := make([]int64, 0, len(req.CloseGroup))
	for _, closeGroup := range req.CloseGroup {
		closeGroupsIds = append(closeGroupsIds, closeGroup.Groups.GroupId)
	}
	closeGroups, err := s.GroupService.GetGroups(ctx, closeGroupsIds)
	if err != nil {
		return ret, err
	}
	releaseGroup := make([]*model.SplitGroup, 0, len(req.CloseGroup))
	splitGroupArr := make([]*model.SplitGroup, 0, len(req.CloseGroup))
	if req.CloseGroup != nil && len(req.CloseGroup) != 0 {
		for _, group := range req.CloseGroup {
			splitGroup := &model.SplitGroup{}
			splitGroup.CreateTime = now
			splitGroup.UpdateTime = now
			splitGroup.FromExpID = group.Groups.ExpId
			splitGroup.FromExpName = group.Groups.ExpName
			splitGroup.FromGroupID = group.Groups.GroupId
			splitGroup.FromGroupName = group.Groups.GroupName
			splitGroup.SplitType = model.SplitGroupTypeClose
			if group.ToGroups == nil {
				releaseGroup = append(releaseGroup, splitGroup)
				continue
			}
			splitGroup.ToExpID = group.ToGroups.ExpId
			splitGroup.ToExpName = group.ToGroups.ExpName
			splitGroup.ToGroupID = group.ToGroups.GroupId
			splitGroup.ToGroupName = group.ToGroups.GroupName
			rate := int32(cast.ToFloat64(group.ToGroups.Rate) * 100)
			splitGroup.Rate = rate
			countMap, _ := s.MetricService.RealtimeGetGroupCount(ctx, []int64{group.Groups.GroupId})
			count := countMap[group.Groups.GroupId]
			splitGroup.Cnt = int32(count) * rate / 10000
			splitGroupArr = append(splitGroupArr, splitGroup)
		}
	}

	if req.RedirectGroup != nil && len(req.RedirectGroup) != 0 {
		for _, group := range req.RedirectGroup {
			splitGroup := &model.SplitGroup{}
			splitGroup.CreateTime = now
			splitGroup.UpdateTime = now
			splitGroup.FromExpID = group.Groups.ExpId
			splitGroup.FromExpName = group.Groups.ExpName
			splitGroup.FromGroupID = group.Groups.GroupId
			splitGroup.FromGroupName = group.Groups.GroupName
			splitGroup.SplitType = model.SplitGroupTypeRedirect
			splitGroup.ToExpID = group.ToGroups.ExpId
			splitGroup.ToExpName = group.ToGroups.ExpName
			splitGroup.ToGroupID = group.ToGroups.GroupId
			splitGroup.ToGroupName = group.ToGroups.GroupName
			rate := int32(cast.ToFloat64(group.ToGroups.Rate) * 100)
			countMap, _ := s.MetricService.RealtimeGetGroupCount(ctx, []int64{group.Groups.GroupId})
			count := countMap[group.Groups.GroupId]
			splitGroup.Cnt = int32(count) * rate / 10000
			splitGroup.Rate = rate
			splitGroupArr = append(splitGroupArr, splitGroup)
		}
	}
	if req.RunningRedirectGroup != nil && len(req.RunningRedirectGroup) != 0 {
		for _, group := range req.RunningRedirectGroup {
			splitGroup := &model.SplitGroup{}
			splitGroup.CreateTime = now
			splitGroup.UpdateTime = now
			splitGroup.FromExpID = group.Groups.ExpId
			splitGroup.FromExpName = group.Groups.ExpName
			splitGroup.FromGroupID = group.Groups.GroupId
			splitGroup.FromGroupName = group.Groups.GroupName
			splitGroup.SplitType = model.SplitGroupTypeRunningRedirect
			splitGroup.ToExpID = group.ToGroups.ExpId
			splitGroup.ToExpName = group.ToGroups.ExpName
			splitGroup.ToGroupID = group.ToGroups.GroupId
			splitGroup.ToGroupName = group.ToGroups.GroupName
			rate := int32(cast.ToFloat64(group.ToGroups.Rate) * 100)
			splitGroup.Rate = rate
			countMap, _ := s.MetricService.RealtimeGetGroupCount(ctx, []int64{group.Groups.GroupId})
			count := countMap[group.Groups.GroupId]
			splitGroup.Cnt = int32(count) * rate / 10000
			splitGroupArr = append(splitGroupArr, splitGroup)
		}
	}

	updateExpMap := make(map[string]any)
	updateExpMap["update_time"] = now
	csr, _ := json.Marshal(req.ExpInfo.Csr)
	updateExpMap["csr"] = string(csr)
	strategy, _ := json.Marshal(req.ExpInfo.Strategy)
	updateExpMap["strategy"] = string(strategy)
	updateExpMap["version"] = req.ExpInfo.Version
	updateExpMap["days"] = req.ExpInfo.Days
	updateExpMap["layer_rate"] = req.ExpInfo.LayerRate
	updateExpMap["owner"] = req.ExpInfo.Owner
	updateExpMap["group_limit_hour"] = req.ExpInfo.GroupLimitHour

	if originExp.Days != req.ExpInfo.Days && originExp.StartTime != nil {
		updateExpMap["days"] = req.ExpInfo.Days
		endTime := originExp.StartTime.AddDate(0, 0, int(req.ExpInfo.Days))
		updateExpMap["end_time"] = endTime
	}
	conflictGroups, err := s.ProjectService.GetConflictGroups(ctx, originExp, addGroups)
	if len(conflictGroups) > 0 && !req.Ignore {
		ret["conflict"] = conflictGroups
		return ret, nil
	}
	// 是否有正在审批的任务
	isHasApprovalTask, err := s.ApprovalTaskManageService.IsExpApprovalRunning(ctx, originExp.ID)
	if err != nil {
		return nil, err
	}
	if isHasApprovalTask {
		return nil, base.NewBizError(errors.New("实验正在审批中，不能修改"))
	}
	expLog, changeNodeList, err := s.buildExpLog(ctx, originExp, req.ExpInfo, addGroups, updateGroups, unchangedGroups, append(releaseGroup, splitGroupArr...))
	if err != nil {
		// TODO
	}
	expLog.Desc = req.Desc
	isNeedApproval, _, err := s.ApprovalRuleManageService.IsNeedApproval(ctx, model.ExpMsgChange, changeNodeList)
	if err != nil {
		return nil, err
	}
	expLog.State = 1
	err = s.DS.WithContext(ctx).ExpLog.Create(expLog)
	if err != nil {
		return nil, err
	}
	// 是否需要审批
	if isNeedApproval {
		baseChanges := GetBaseChanges(changeNodeList)
		batchChanges := GetBatchChanges(req.Groups)
		changes := &model.Changes{
			BaseChanges:  baseChanges,
			BatchChanges: batchChanges,
		}
		_, err = s.ApprovalTaskManageService.CreateApprovalTask(ctx, model.ExpMsgChange, &model.ApprovalData{
			ExpId:   originExp.ID,
			AfterId: expLog.ID,
			Desc:    req.Desc,
			Changes: changes,
		})
		ret["is_approval"] = true
		return ret, err
	}
	err = s.DS.Transaction(func(tx *dao.Query) error {
		expDao := s.DS.Exp
		_, err = tx.WithContext(ctx).Exp.Debug().Where(expDao.ID.Eq(req.ExpInfo.Id)).Updates(updateExpMap)
		if err != nil {
			return err
		}
		// 添加新方案
		if len(addGroups) != 0 {
			err = tx.WithContext(ctx).Group.Debug().Create(addGroups...)
			if err != nil {
				return err
			}
		}
		// 去掉执行的运行时，重定向
		if req.RunningRedirectGroup != nil && len(req.RunningRedirectGroup) != 0 {
			splitGroupDao := s.DS.SplitGroup
			_, err = tx.WithContext(ctx).SplitGroup.
				Where(splitGroupDao.FromExpID.Eq(req.ExpInfo.Id), splitGroupDao.SplitType.Eq(model.SplitGroupTypeRunningRedirect)).
				Updates(map[string]any{"state": 2})
			if err != nil {
				return err
			}
		}
		// 更新方案
		if len(updateGroups) != 0 {
			groupDao := s.DS.Group
			for _, uGroup := range updateGroups {
				_, err = tx.WithContext(ctx).Group.Debug().Where(groupDao.ID.Eq(uGroup.ID)).Where(groupDao.ExpID.Eq(uGroup.ExpID)).Updates(uGroup)
				if err != nil {
					return err
				}
			}
		}
		// 添加分流重定向
		if len(splitGroupArr) != 0 {
			err = tx.WithContext(ctx).SplitGroup.Debug().Create(splitGroupArr...)
			if err != nil {
				return err
			}
		}

		// 实验天数有变更，需要同步变更实验任务
		if req.ExpInfo.Days != originExp.Days {
			runningStateMap := map[model.ExpStatus]bool{
				model.ExpStatusRunning:     true,
				model.ExpStatusGrayRunning: true,
			}
			// 运行中编辑模式，需要同步变更任务的时间
			if runningStateMap[model.ExpStatus(originExp.State)] {
				diffDays := cast.ToInt(math.Abs(float64(req.ExpInfo.Days - originExp.Days)))
				err = UpdateTaskOnExpDayChange(tx, originExp.ID, diffDays)
				if err != nil {
					return err
				}
			}
		}
		// 实验日志
		expLogDao := s.DS.ExpLog
		_, err = tx.WithContext(ctx).ExpLog.Where(expLogDao.ID.Eq(expLog.ID)).Updates(map[string]any{"state": 2, "show": 1})
		if err != nil {
			return err
		}
		_ = s.Events.ExpUpdated.Push(ctx, ExpUpdatedEvent{ExpID: req.ExpInfo.Id})

		return nil
	})

	if err != nil {
		return ret, err
	}
	_ = s.Events.ExpUpdated.Push(ctx, ExpUpdatedEvent{ExpID: req.ExpInfo.Id, Log: expLog})

	// 处理删除的分组,删除特性
	if len(closeGroups) > 0 {
		err = s.ProjectService.RemoveLayerExpFeature(ctx, originExp, closeGroups)
		if err != nil {
			return ret, err
		}
	}
	if len(addGroups) > 0 || len(updateGroups) > 0 {
		_ = s.ProjectService.SaveProjectFeatureStats(ctx, originExp)
	}
	return ret, nil
}

func GetBaseChanges(changeNodes []*model.ChangeNode) []*model.Iterm {
	// 处理基础变更节点（虽然最终会被覆盖，但保留原始逻辑）
	baseChanges := make([]*model.Iterm, 0, len(changeNodes))
	changeNodeByType := lo.GroupBy(changeNodes, func(item *model.ChangeNode) string {
		return item.GroupType
	})
	for nodeType, nodeList := range changeNodeByType {
		// 对每个节点类型进行处理
		values := lo.Map(nodeList, func(item *model.ChangeNode, index int) string {
			return item.Change
		})
		nodeTypeStr := model.ChangeNodeTypeMap[nodeType]
		baseChanges = append(baseChanges, &model.Iterm{
			Label: nodeTypeStr,
			Value: strings.Join(values, ""),
		})
	}
	return baseChanges
}

func GetBatchChanges(reqGroups []*request.EditSaveGroup) []*model.Iterm {
	// 处理请求组生成批量变更
	stateGroupMapping := map[int32]string{
		model.GroupStateRunning:            "运行中的方案",
		model.GroupStateRunningNotProgress: "运行中不进量的方案",
		model.GroupStateClose:              "关闭的方案",
	}
	const batchNameLabel = "指定批次"
	groupsByState := lo.GroupBy(reqGroups, func(g *request.EditSaveGroup) int32 { return g.State })
	batchChanges := make([]*model.Iterm, 0, len(reqGroups)*2) // 预分配合理容量
	// 处理每个状态分组
	for state, groupTypeLabel := range stateGroupMapping {
		groups, ok := groupsByState[state]
		if !ok {
			continue
		}

		// 按组名分组处理批次
		groupsByBatch := lo.GroupBy(groups, func(g *request.EditSaveGroup) string { return g.BatchName })
		for batchName, batchGroups := range groupsByBatch {
			// 生成组名称列表
			groupNames := lo.Map(batchGroups, func(g *request.EditSaveGroup, _ int) string { return g.Name })
			groupItem := &model.Iterm{
				Label: groupTypeLabel,
				Value: strings.Join(groupNames, ","),
			}

			// 生成批次项
			batchItem := &model.Iterm{
				Label: batchNameLabel,
				Value: batchName,
			}
			batchChanges = append(batchChanges, groupItem, batchItem)
		}
	}
	groups := lo.Filter(reqGroups, func(g *request.EditSaveGroup, _ int) bool {
		if g.GroupId == 0 {
			return true
		}
		return false
	})

	if len(groups) > 0 {
		groupNames := lo.Map(groups, func(g *request.EditSaveGroup, _ int) string { return g.Name })
		groupItem := &model.Iterm{
			Label: "新增方案",
			Value: strings.Join(groupNames, ","),
		}
		baseName := groups[0].BatchName
		// 生成批次项
		batchItem := &model.Iterm{
			Label: batchNameLabel,
			Value: baseName,
		}
		batchChanges = append(batchChanges, groupItem, batchItem)
	}

	return batchChanges
}

func (s *ExpService) DraftList(req request.ExpListReq, ctx *gin.Context) (any, error) {
	expDao := s.DS.Exp
	userDao := s.QueryForBIv1.User
	expList := make([]*response.DraftExpList, 0, 10)
	var likeSubQuery dao.IExpDo
	wheres := make([]gen.Condition, 0, 10)
	if req.Search != "" {
		likeClause := fmt.Sprintf("%%%s%%", req.Search)
		users, _ := userDao.Where(userDao.Realname.Like(likeClause)).Debug().Select(userDao.ID, userDao.Realname).Find()
		likeSubQuery = expDao.Where(expDao.Name.Like(likeClause)).Or(expDao.Desc.Like(likeClause))
		if len(users) > 0 {
			for _, user := range users {
				likeSubQuery = likeSubQuery.Or(expDao.Owner.Like(fmt.Sprintf("%%%d%%", user.ID)))
			}
		}
		wheres = append(wheres, likeSubQuery)
	}
	wheres = append(wheres, expDao.IsDeleted.Eq(model.NotDeleted))
	wheres = append(wheres, expDao.UID.Eq(cast.ToInt32(auth.GetUserId(ctx))))
	wheres = append(wheres, expDao.ProjectID.Eq(auth.GetProjectId64(ctx)))
	result, total, err := expDao.Where(wheres...).
		Where(expDao.State.Eq(model.ExpStateDraft)).
		Order(expDao.UpdateTime.Desc()).Debug().
		FindByPage(s.Query.CalcOffsetLimit(req.Page, req.PageSize))
	if err != nil {
		return nil, err
	}
	for _, resultItem := range result {
		expItem := &response.DraftExpList{}
		err = copier.Copy(expItem, resultItem)
		if err != nil {
			return nil, err
		}
		createTime := carbon.CreateFromStdTime(resultItem.CreateTime).Format("Y-m-d H:i:s")
		updateTime := carbon.CreateFromStdTime(resultItem.UpdateTime).Format("Y-m-d H:i:s")
		expItem.CreateTime = createTime
		expItem.UpdateTime = updateTime
		stepStr := cast.ToString(resultItem.Step)
		stropTotalStr := cast.ToString(model.ExpStepMeasure)
		expItem.Step = stepStr + "/" + stropTotalStr
		expItem.NextStep = resultItem.Step + 1
		ownerNameArr, _ := s.UserIdsToUserNames(expItem.Owner)
		expItem.OwnerName = strings.Join(ownerNameArr, ",")
		expList = append(expList, expItem)
	}
	list := make(map[string]any, 4)
	list["total"] = total
	list["list"] = expList
	list["page"] = req.Page
	list["page_size"] = req.PageSize
	return list, err
}
func (s *ExpService) GetBatchList(ctx *gin.Context) (any, error) {
	batchDao := s.DS.Batch
	batches, _ := batchDao.
		Where(batchDao.IsDeleted.Eq(model.NotDeleted)).
		Where(batchDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Select(batchDao.ID, batchDao.Name).
		Find()
	batchesMap := make([]map[string]any, 0, 10)
	for _, batch := range batches {
		mapItem := make(map[string]any, 2)
		mapItem["id"] = batch.ID
		mapItem["name"] = batch.Name
		mapItem["desc"] = batch.Desc
		batchesMap = append(batchesMap, mapItem)
	}
	return batchesMap, nil
}
func (s *ExpService) GetTagList(ctx *gin.Context) (any, error) {
	tagDao := s.DS.Tag
	tags, _ := tagDao.
		Where(tagDao.IsDeleted.Eq(model.NotDeleted)).
		Where(tagDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Select(tagDao.ID, tagDao.Name).
		Find()
	tagsMap := make([]map[string]any, 0, 10)
	for _, batch := range tags {
		mapItem := make(map[string]any, 2)
		mapItem["id"] = batch.ID
		mapItem["name"] = batch.Name
		tagsMap = append(tagsMap, mapItem)
	}
	return tagsMap, nil
}
func getDaysHours(startTime string, endTime string) (string, int64) {
	var days int
	var hour int
	var minutes int64
	if startTime == "" || endTime == "" {
		return "0天0小时", minutes
	} else {
		if time.Since(cast.ToTime(endTime)).Minutes() > 0 {
			minutes = carbon.Parse(startTime).DiffAbsInMinutes(carbon.Parse(endTime))
		} else {
			minutes = carbon.Parse(startTime).DiffAbsInMinutes()
		}
		days = int(minutes / (24 * 60))
		hour = int((minutes % (24 * 60)) / 60)
	}
	runningTime := fmt.Sprintf("%d天%d小时", days, hour)
	return runningTime, minutes
}

type ListExpOut struct {
	Total    int64                 `json:"total"`
	List     []*response.ExpDetail `json:"list"`
	Page     int                   `json:"page"`
	PageSize int                   `json:"page_size"`
}

func (s *ExpService) List(req request.ExpListReq, ctx context.Context, prjID int64) (*ListExpOut, error) {
	expDao := s.DS.Exp
	layerDao := s.DS.Layer
	batchDao := s.DS.Batch

	expList := make([]*response.ExpDetail, 0, 10)
	var likeSubQuery dao.IExpDo
	var tagIdSubQuery dao.IExpDo
	wheres := make([]gen.Condition, 0, 10)
	if req.Search != "" {
		likeClause := fmt.Sprintf("%%%s%%", req.Search)
		userDao := s.QueryForBIv1.User
		users, _ := userDao.WithContext(ctx).Where(userDao.Realname.Like(likeClause)).Debug().Select(userDao.ID, userDao.Realname).Find()
		likeSubQuery = expDao.WithContext(ctx).Where(expDao.Name.Like(likeClause)).Or(expDao.Desc.Like(likeClause))
		if len(users) > 0 {
			for _, user := range users {
				likeSubQuery = likeSubQuery.Or(expDao.Owner.Like(fmt.Sprintf("%%%d%%", user.ID)))
			}
		}
		wheres = append(wheres, likeSubQuery)
	}
	if len(req.StartTime) != 0 {
		startTime, _ := time.Parse("2006-01-02 15:04:05", req.StartTime[0]+" 00:00:00")
		endTime, _ := time.Parse("2006-01-02 15:04:05", req.StartTime[1]+" 23:59:59")
		wheres = append(wheres, expDao.StartTime.Between(startTime, endTime))
	}
	if len(req.EndTime) != 0 {
		startTime, _ := time.Parse("2006-01-02 15:04:05", req.EndTime[0]+" 00:00:00")
		endTime, _ := time.Parse("2006-01-02 15:04:05", req.EndTime[1]+" 23:59:59")
		wheres = append(wheres, expDao.EndTime.Between(startTime, endTime))
	}
	if len(req.ExpType) != 0 {
		expTypes := make([]int32, 0)
		lo.ForEach(strings.Split(req.ExpType, ","), func(item string, index int) {
			expTypes = append(expTypes, cast.ToInt32(item))
		})
		wheres = append(wheres, expDao.ExpType.In(expTypes...))
	}
	if len(req.LayerId) != 0 {
		layerIds := make([]int64, 0)
		lo.ForEach(strings.Split(req.LayerId, ","), func(item string, index int) {
			layerIds = append(layerIds, cast.ToInt64(item))
		})
		wheres = append(wheres, expDao.LayerID.In(layerIds...))
	}
	if len(req.State) != 0 {
		stateIds := make([]int32, 0)
		lo.ForEach(strings.Split(req.State, ","), func(item string, index int) {
			stateIds = append(stateIds, cast.ToInt32(item))
		})
		wheres = append(wheres, expDao.State.In(stateIds...))
	}
	if len(req.TagId) != 0 {
		lo.ForEach(strings.Split(req.TagId, ","), func(tagIdStr string, index int) {
			likeClause := fmt.Sprintf("%%%s%%", tagIdStr)
			if index == 0 {
				tagIdSubQuery = expDao.Where(expDao.TagID.Like(likeClause))
			} else {
				tagIdSubQuery = tagIdSubQuery.Or(expDao.TagID.Like(likeClause))
			}
		})
		wheres = append(wheres, tagIdSubQuery)
	}
	if len(req.BatchId) != 0 {
		batchIds := make([]int64, 0)
		lo.ForEach(strings.Split(req.BatchId, ","), func(item string, index int) {
			batchIds = append(batchIds, cast.ToInt64(item))
		})
		wheres = append(wheres, expDao.BatchID.In(batchIds...))
	}
	wheres = append(wheres, expDao.IsDeleted.Eq(model.NotDeleted))
	wheres = append(wheres, expDao.State.Neq(model.ExpStateDraft))
	if prjID >= 0 {
		wheres = append(wheres, expDao.ProjectID.Eq(prjID))
	}
	orders := make([]field.Expr, 0, 2)
	switch req.OrderField {
	case "create_time":
		if req.OrderType == "asc" {
			orders = append(orders, expDao.CreateTime.Asc())
		} else if req.OrderType == "desc" {
			orders = append(orders, expDao.CreateTime.Desc())
		}
	case "update_time":
		if req.OrderType == "asc" {
			orders = append(orders, expDao.UpdateTime.Asc())
		} else if req.OrderType == "desc" {
			orders = append(orders, expDao.UpdateTime.Desc())
		}
	case "start_time":
		if req.OrderType == "asc" {
			orders = append(orders, expDao.StartTime.Asc())
		} else if req.OrderType == "desc" {
			orders = append(orders, expDao.StartTime.Desc())
		}
	default:
		orders = append(orders, expDao.UpdateTime.Desc())
	}
	approvalDao := s.DS.ApprovalTask
	expInfoList, err := expDao.WithContext(ctx).
		Preload(expDao.Groups, expDao.Layer, expDao.Batch).
		Preload(expDao.ApprovalTasks.Clauses(approvalDao.State.Eq(model.ApprovalInit))).
		Where(wheres...).
		Order(orders...).
		Scopes(Paginate(req.Page, req.PageSize)).
		Find()
	if err != nil {
		return nil, err
	}
	expIdsArr := make([]int64, 0)
	for _, exp := range expInfoList {
		expIdsArr = append(expIdsArr, exp.ID)
	}
	expTotalUserMap, _ := s.MetricService.GetExpUserCount(ctx, expIdsArr)
	for _, expInfo := range expInfoList {
		expInfoDetail := &response.ExpDetail{}
		expInfoDetail.Id = expInfo.ID
		expInfoDetail.Name = expInfo.Name
		expInfoDetail.GroupLimitHour = expInfo.GroupLimitHour
		expInfoDetail.Uid = expInfo.UID
		expInfoDetail.LayerId = expInfo.LayerID
		expInfoDetail.ProjectID = expInfo.ProjectID
		expInfoDetail.LayerName = expInfo.Layer.Name
		expInfoDetail.LayerRate = expInfo.LayerRate
		expInfoDetail.BatchId = expInfo.BatchID
		expInfoDetail.BatchName = expInfo.Batch.Name
		expInfoDetail.State = expInfo.State
		expInfoDetail.StateName = model.ExpStateMap[int(expInfo.State)]
		expInfoDetail.ExpType = expInfo.ExpType
		expInfoDetail.ExpTypeName = model.ExpTypeMap[int(expInfo.ExpType)]
		expInfoDetail.Desc = expInfo.Desc
		expInfoDetail.TagId = expInfo.TagID
		expInfoDetail.Step = expInfo.Step
		expInfoDetail.Owner = expInfo.Owner
		expInfoDetail.Days = expInfo.Days
		expInfoDetail.Strategy = expInfo.Strategy
		expInfoDetail.Csr = expInfo.Csr
		expInfoDetail.Version = expInfo.Version
		expInfoDetail.GroupLimitHour = expInfo.GroupLimitHour
		if expInfo.StartTime != nil {
			expInfoDetail.StartTime = cast.ToTime(expInfo.StartTime).Format("2006-01-02 15:04:05")
			expInfoDetail.EndTime = cast.ToTime(expInfo.EndTime).Format("2006-01-02 15:04:05")
		}
		expRunTime, minutes := getDaysHours(expInfoDetail.StartTime, expInfoDetail.EndTime)
		expInfoDetail.ExpRunTime = expRunTime
		expInfoDetail.ExpRunTimeMinutes = minutes
		if expInfo.TagID != "" {
			tagNames, _ := s.TagIdsToTagNames(expInfo.TagID)
			expInfoDetail.TagName = tagNames
		}
		expInfoDetail.CreateTime = cast.ToTime(expInfo.CreateTime).Format("2006-01-02 15:04:05")
		expInfoDetail.UpdateTime = cast.ToTime(expInfo.UpdateTime).Format("2006-01-02 15:04:05")

		if expInfo.Owner != "" {
			userNames, _ := s.UserIdsToUserNames(expInfo.Owner)
			expInfoDetail.OwnerName = strings.Join(userNames, ",")
		}
		var groupCurrenSum int64
		if expInfo.Groups != nil {
			groupIdArr := make([]int64, 0, 10)
			lo.ForEach(expInfo.Groups, func(item *model.Group, index int) {
				groupIdArr = append(groupIdArr, item.ID)
			})
			if len(groupIdArr) != 0 {
				groupCurrentMap, _ := s.MetricService.RealtimeGetGroupCount(ctx, groupIdArr)
				for _, groupCount := range groupCurrentMap {
					groupCurrenSum = groupCurrenSum + groupCount
				}
			}
		}
		expInfoDetail.TotalUser = expTotalUserMap[expInfo.ID]
		expInfoDetail.TotalCurrentUser = groupCurrenSum
		expInfoDetail.ExpTypeName = model.ExpTypeMap[int(expInfo.ExpType)]
		expInfoDetail.StateName = model.ExpStateMap[int(expInfo.State)]
		var isApprovalRunning bool
		var approvalUid int32
		var approvalId int64
		if len(expInfo.ApprovalTasks) != 0 {
			lo.ForEach(expInfo.ApprovalTasks, func(item *model.ApprovalTask, index int) {
				if item.State == model.ApprovalInit {
					isApprovalRunning = true
					approvalUid = item.CreatorID
					approvalId = item.ID
					return
				}
			})
		}
		expInfoDetail.IsApprovalRunning = isApprovalRunning
		expInfoDetail.ApprovalUid = approvalUid
		expInfoDetail.ApprovalId = approvalId
		expList = append(expList, expInfoDetail)
	}
	total, err := expDao.WithContext(ctx).
		Where(wheres...).
		LeftJoin(layerDao, expDao.LayerID.EqCol(layerDao.ID)).
		LeftJoin(batchDao, expDao.BatchID.EqCol(batchDao.ID)).Count()

	return &ListExpOut{
		Total:    total,
		List:     expList,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, err
}

// ChangeState 修改实验状态 1：开启，2:灰度开启，3:不进量，4:关闭，5：重启, 6：开启不进量
func (s *ExpService) ChangeState(ctx context.Context, req *request.ChangeStateReq) (any, error) {
	expDao := s.DS.Exp
	ret := make(map[string]any)
	ret["is_approval"] = false
	exp, err := expDao.Where(expDao.ID.Eq(req.ExpId)).First()
	if err != nil {
		return ret, err
	}

	switch req.Operation {
	case 1:
		err = s.Open(ctx, exp)
	case 2:
		err = s.OpenGray(ctx, exp)
	case 3:
		err = s.WithoutFlow(ctx, exp)
	case 4:
		err = s.Close(ctx, exp)
	case 5:
		err = s.Restart(ctx, exp)
	case 6:
		err = s.OpenWithoutFlow(ctx, exp)
	default:
		return ret, base.NewBizError(errors.New("operation not supported"))
	}
	if errors.Is(err, model.ExpStateChangeNeedApprovalError) {
		ret["is_approval"] = true
		err = nil
	}
	return ret, err
}

func (s *ExpService) Delete(req request.DeleteStateReq, ctx *gin.Context) error {
	expDao := s.DS.Exp
	groupDao := s.DS.Group
	first, err := expDao.WithContext(ctx).
		Where(expDao.ID.Eq(req.ExpId)).
		Where(expDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Take()
	if err != nil {
		return err
	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return errors.New("该实验，不存在")
	}
	if first.IsDeleted == 1 {
		return errors.New("该实验，已经被删除")
	}
	err = s.DS.Transaction(func(tx *dao.Query) error {
		_, err = tx.Exp.WithContext(ctx).
			Where(expDao.ID.Eq(req.ExpId)).
			Where(expDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
			Update(expDao.IsDeleted, 1)
		if err != nil {
			return err
		}
		_, err = tx.Group.WithContext(ctx).
			Where(groupDao.IsDeleted.Eq(1)).
			Where(groupDao.ExpID.Eq(req.ExpId)).
			Update(groupDao.IsDeleted, req.IsDel)
		if err != nil {
			return err
		}
		return nil
	})

	return err
}

func (s *ExpService) ListHeader(ctx *gin.Context) (any, error) {
	headerList := make(map[string]any, 5)
	// 实验类型
	expTypeMap := make([]response.Item, 0, 5)
	for id, state := range model.ExpTypeMap {
		item := response.Item{
			Value: id,
			Label: state,
		}
		expTypeMap = append(expTypeMap, item)
	}
	headerList["exp_type"] = expTypeMap
	// 实验状态
	expStateMap := make([]response.Item, 0, 5)
	for id, state := range model.ExpStateMap {
		if id == model.ExpStateDraft {
			continue
		}
		item := response.Item{
			Value: id,
			Label: state,
		}
		expStateMap = append(expStateMap, item)
	}
	headerList["state"] = expStateMap
	// 实验分层
	layerDao := s.DS.Layer
	layers, _ := layerDao.
		Where(layerDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Where(layerDao.State.Eq(model.StateOnline)).
		Select(layerDao.ID, layerDao.Name).
		Find()
	layersMap := make([]response.Item, 0, 5)
	for _, layer := range layers {
		item := response.Item{
			Value: layer.ID,
			Label: layer.Name,
		}
		layersMap = append(layersMap, item)
	}
	headerList["layer"] = layersMap
	// 实验标签
	tagDao := s.DS.Tag
	tags, _ := tagDao.
		Where(tagDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Where(tagDao.IsDeleted.Eq(model.NotDeleted)).
		Select(tagDao.ID, tagDao.Name).
		Find()
	tagsMap := make([]response.Item, 0, 5)
	for _, tag := range tags {
		item := response.Item{
			Value: tag.ID,
			Label: tag.Name,
		}
		tagsMap = append(tagsMap, item)
	}
	headerList["tag"] = tagsMap
	// 实验批次
	batchDao := s.DS.Batch
	batches, _ := batchDao.
		Where(batchDao.IsDeleted.Eq(model.NotDeleted)).
		Where(batchDao.ProjectID.Eq(auth.GetProjectId64(ctx))).
		Select(batchDao.ID, batchDao.Name).
		Find()
	batchesMap := make([]response.Item, 0, 5)
	for _, batch := range batches {
		item := response.Item{
			Value: batch.ID,
			Label: batch.Name,
		}
		batchesMap = append(batchesMap, item)
	}
	headerList["batch"] = batchesMap
	return headerList, nil
}

// UpdateExpState 更新实验的状态，如果是首次的开启，则需要更新实验的开始和结束时间
func UpdateExpState(ctx context.Context, tx *dao.Query, exp *model.Exp, oldStatus model.ExpStatus) error {
	if exp == nil || exp.ID == 0 {
		return base.NewAlertTip("实验数据不允许为空")
	}

	now := time.Now()
	updateExp := &model.Exp{
		State:      exp.State,
		Version:    exp.Version + 1,
		Data:       exp.Data,
		UpdateTime: now,
	}
	if exp.State == model.ExpStateClose && exp.EndTime != nil {
		updateExp.EndTime = &now
	}
	if oldStatus == model.ExpStatusPending {
		endTime := now.AddDate(0, 0, int(exp.Days))
		exp.StartTime = &now
		exp.EndTime = &endTime
		exp.State = updateExp.State
		exp.Version = updateExp.Version

		runningStatusMap := map[model.ExpStatus]bool{
			model.ExpStatusGrayRunning:        true,
			model.ExpStatusRunningWithoutFlow: true,
			model.ExpStatusRunning:            true,
		}
		if runningStatusMap[model.ExpStatus(exp.State)] {
			updateExp.StartTime = &now
			updateExp.EndTime = &endTime
		}
	}
	if model.IsFreezed(updateExp.State) {
		updateExp.Data.FreezedAt = now
	}
	if model.IsClosed(updateExp.State) {
		updateExp.Data.ClosedAt = now
	}
	result, err := tx.Exp.Where(tx.Exp.ID.Eq(exp.ID)).Updates(updateExp)
	if err != nil {
		return base.NewAlertTip("更新实验状态失败")
	}
	if result.RowsAffected != 1 {
		return base.NewAlertTip(fmt.Sprintf("更新实验的条数为%d,与预期1条不符", result.RowsAffected))
	}
	return nil
}

// QueryRunningExp 查询运行中的实验
func (s *ExpService) QueryRunningExp(offset, limit int) ([]any, error) {
	now := time.Now()
	expDao := s.DS.Exp
	list, err := expDao.Where(
		expDao.EndTime.Gte(now),
		expDao.State.In(
			int32(model.ExpStatusGrayRunning),
			int32(model.ExpStatusGrayRunningWithoutFlow),
			int32(model.ExpStatusRunning),
			int32(model.ExpStatusRunningWithoutFlow),
		),
		expDao.IsDeleted.Eq(model.NotDeleted)).
		Order(expDao.ID.Asc(), expDao.UpdateTime.Desc()).
		Offset(offset).Limit(limit).Find()
	if err != nil {
		return nil, err
	}
	var data []any
	for _, v := range list {
		data = append(data, v)
	}
	return data, nil
}

// buildExpLog 构建实验变更的日志信息（含快照）
func (s *ExpService) buildExpLog(ctx context.Context, originExp *model.Exp, reqExp *request.EditSaveBase,
	addGroups, updateGroups, unchangedGroups []*model.Group, splitGroupArr []*model.SplitGroup) (*model.ExpLog, []*model.ChangeNode, error) {
	original := &dto.ExpLog{
		Exp: originExp,
	}
	// 构建实验实验日志信息
	newExp := &model.Exp{}
	if err := copier.Copy(newExp, reqExp); err != nil {
		return nil, nil, err
	}
	newExp.StartTime = originExp.StartTime
	newExp.EndTime = originExp.EndTime
	if diffDay := originExp.Days - reqExp.Days; diffDay != 0 {
		endTime := originExp.StartTime.AddDate(0, 0, int(reqExp.Days))
		newExp.EndTime = &endTime
	}

	newExp.CreateTime = originExp.CreateTime
	newExp.UpdateTime = time.Now()
	newExp.Version += 1
	newExp.Desc = reqExp.Desc
	current := &dto.ExpLog{
		Exp:             newExp,
		AddGroups:       addGroups,
		UpdateGroups:    updateGroups,
		UnchangedGroups: unchangedGroups,
		SplitGroup:      splitGroupArr,
		OriginExp:       originExp,
	}
	return s.ExpLogService.BuildExpLog(ctx, original, current)
}

// Detail 根据实验ID获取实验明细数据
func (s *ExpService) Detail(id int64) (*model.Exp, error) {
	expDao := s.DS.Exp
	return expDao.Where(expDao.ID.Eq(id)).Preload(expDao.Batch).First()
}

// 根据实验id获取实验信息
func (s *ExpService) QueryExpByIds(ctx context.Context, expId []int64) ([]*model.Exp, error) {
	expDao := s.DS.Exp
	return expDao.WithContext(ctx).Where(expDao.ID.In(expId...)).Find()
}
