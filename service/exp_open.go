package service

import (
	"context"
	"fmt"
	"git.7k7k.com/data/abAdmin/httpd/base"
	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/repository/dao"
)

// 开启实验有三种业务场景，一个是灰度运行，一个是全量运行，一个是开启不进量

// OpenGray 开启灰度实验
func (s *ExpService) OpenGray(ctx context.Context, exp *model.Exp) error {
	err := s.LayerService.AvailableRate(exp.LayerID, exp.ID, exp.LayerRate)
	if err != nil {
		return base.NewAlertTip(fmt.Sprintf("开启灰度失败，%s", err.Error()))
	}
	return s.ExpStateService.ToggleExpStatus(ctx, exp, &OpenGrayStrategy{ApprovalNode: model.ExpOpen})
}

// Open 全量开启
func (s *ExpService) Open(ctx context.Context, exp *model.Exp) error {
	err := s.LayerService.AvailableRate(exp.LayerID, exp.ID, exp.LayerRate)
	if err != nil {
		return base.NewAlertTip(fmt.Sprintf("开启全量失败，%s", err.Error()))
	}
	return s.ExpStateService.ToggleExpStatus(ctx, exp, &OpenStrategy{ApprovalNode: model.ExpOpen})
}

// OpenWithoutFlow 开启不进量
func (s *ExpService) OpenWithoutFlow(ctx context.Context, exp *model.Exp) error {
	return s.ExpStateService.ToggleExpStatus(ctx, exp, &OpenWithoutFlowStrategy{ApprovalNode: model.ExpOpen})
}

/**********************************************开启灰度实验*********************************************************/

// OpenGrayStrategy 开启灰度实验
type OpenGrayStrategy struct {
	Status       model.ExpStatus
	ApprovalNode string
}

// TryTransStatus 开启灰度，待开始->灰度运行中
func (o *OpenGrayStrategy) TryTransStatus(status model.ExpStatus) bool {
	o.Status = model.ExpStatusGrayRunning
	return status == model.ExpStatusPending
}

// GetStatusAfterTrans 获取状态，灰度运行中
func (o *OpenGrayStrategy) GetStatusAfterTrans() model.ExpStatus {
	return o.Status
}

// GetOperate 开启灰度实验
func (o *OpenGrayStrategy) GetOperate() ExpOpt {
	return expOpenGrayOpt
}

// CreateOrUpdateTask 创建灰度实验,oldExp 为空，newExp不为空
func (o *OpenGrayStrategy) CreateOrUpdateTask(tx *dao.Query, newExp *model.Exp) error {
	return CreateTask(tx, newExp)
}
func (o *OpenGrayStrategy) PrepareGroupData(groups []*model.Group) error {
	err := OpenGroup(groups)
	if err != nil {
		return err
	}
	return nil
}

func (o *OpenGrayStrategy) GetApprovalNode() string {
	return o.ApprovalNode
}

// UpdateGroupStatus 开启实验组
func (o *OpenGrayStrategy) UpdateGroupStatus(tx *dao.Query, groups []*model.Group) error {
	err := UpdateGroup(tx, groups)
	if err != nil {
		return err
	}
	return nil
}

/**********************************************开启全量实验*********************************************************/

// OpenStrategy 开启全量实验
type OpenStrategy struct {
	Status       model.ExpStatus
	BeforeStatus model.ExpStatus
	ApprovalNode string
}

// TryTransStatus 开启全量运行 待开始->运行中，灰度运行中->运行中
func (o *OpenStrategy) TryTransStatus(status model.ExpStatus) bool {
	o.BeforeStatus = status
	o.Status = model.ExpStatusRunning
	return status == model.ExpStatusPending || status == model.ExpStatusGrayRunning
}

// GetStatusAfterTrans 获取状态，运行中
func (o *OpenStrategy) GetStatusAfterTrans() model.ExpStatus {
	return o.Status
}

// GetOperate 开启实验
func (o *OpenStrategy) GetOperate() ExpOpt {
	return expOpenOpt
}
func (o *OpenStrategy) GetApprovalNode() string {
	return o.ApprovalNode
}

// CreateOrUpdateTask 待开始-->全量运行创建，灰度运行中-->全量运行则更新
func (o *OpenStrategy) CreateOrUpdateTask(tx *dao.Query, newExp *model.Exp) error {
	if o.BeforeStatus == model.ExpStatusPending {
		// 待开始-->全量运行
		return CreateTask(tx, newExp)
	} else if o.BeforeStatus == model.ExpStatusGrayRunning {
		// 灰度运行中-->全量运行
		return UpdateTask(tx, newExp, model.TaskStatusPending)
	}
	return fmt.Errorf("unsupport exp state: %d for Open", o.BeforeStatus)
}
func (o *OpenStrategy) PrepareGroupData(groups []*model.Group) error {
	err := OpenGroup(groups)
	if err != nil {
		return err
	}
	return nil
}

// UpdateGroupStatus 开启实验组
func (o *OpenStrategy) UpdateGroupStatus(tx *dao.Query, groups []*model.Group) error {
	err := UpdateGroup(tx, groups)
	if err != nil {
		return err
	}
	return nil
}

/**********************************************开启不进量*********************************************************/

// OpenWithoutFlowStrategy 开启不进量
type OpenWithoutFlowStrategy struct {
	Status       model.ExpStatus
	ApprovalNode string
}

// TryTransStatus 开启不进量 待开始->运行中不进量
func (o *OpenWithoutFlowStrategy) TryTransStatus(status model.ExpStatus) bool {
	o.Status = model.ExpStatusRunningWithoutFlow
	return status == model.ExpStatusPending
}

// GetStatusAfterTrans 获取状态，运行中不进量
func (o *OpenWithoutFlowStrategy) GetStatusAfterTrans() model.ExpStatus {
	return o.Status
}

// GetOperate 开启不进量
func (o *OpenWithoutFlowStrategy) GetOperate() ExpOpt {
	return expOpenWithoutFlowOpt
}
func (o *OpenWithoutFlowStrategy) GetApprovalNode() string {
	return o.ApprovalNode
}

// CreateOrUpdateTask 创建灰度实验,oldExp 为空，newExp不为空
func (o *OpenWithoutFlowStrategy) CreateOrUpdateTask(tx *dao.Query, newExp *model.Exp) error {
	return CreateTask(tx, newExp)
}
func (o *OpenWithoutFlowStrategy) PrepareGroupData(groups []*model.Group) error {
	err := OpenGroupWithoutFlow(groups)
	if err != nil {
		return err
	}
	return nil
}

// UpdateGroupStatus 开启实验组不进量
func (o *OpenWithoutFlowStrategy) UpdateGroupStatus(tx *dao.Query, groups []*model.Group) error {
	err := UpdateGroup(tx, groups)
	if err != nil {
		return err
	}
	return nil
}
