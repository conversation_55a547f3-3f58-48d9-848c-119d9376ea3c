package auth

import (
	"context"
	"sync"

	"github.com/spf13/cast"
)

var lock sync.RWMutex
var projectMap = make(map[int]*MemStore)

func GetProjectStore(projectId int) *MemStore {
	lock.RLock()
	defer lock.RUnlock()

	return projectMap[projectId]
}

func RefreshProjects(projectId int, store *MemStore) {
	lock.Lock()
	defer lock.Unlock()

	projectMap[projectId] = store
}

func GetProjectId(c context.Context) int {
	return cast.ToInt(c.Value(ContextKeyProjectID))
}

func GetProjectId64(c context.Context) int64 {
	return cast.ToInt64(GetProjectId(c))
}

// Can 判断用户拥有权限
func Can(c context.Context, permId int) bool {

	store := GetProjectStore(GetProjectId(c))
	return IsAdmin(c) || store.Can(GetUserId(c), permId)
}

// CanName 判断用户拥有权限
func CanName(c context.Context, permName string) bool {
	store := GetProjectStore(GetProjectId(c))
	return IsAdmin(c) || store.CanName(GetUserId(c), permName)
}

// InProject 判断用户在不在项目里
func InProject(c context.Context, projectId int) bool {
	store := GetProjectStore(projectId)
	return IsAdmin(c) || len(store.UserRoles[GetUserId(c)]) > 0
}
