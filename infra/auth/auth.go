package auth

import (
	"context"

	"git.7k7k.com/pkg/common/ctxs"
	"github.com/samber/lo"
	"github.com/spf13/cast"
)

var (
	ContextKeyUserID    = ctxs.KeyUserID
	ContextKeyIsAdmin   = "auth_is_admin"
	ContextKeyProjectID = "auth_project_id"
)

func GetUserId(c context.Context) int {
	return cast.ToInt(c.Value(ContextKeyUserID))
}

func GetUserIdInt32(c context.Context) int32 {
	return cast.ToInt32(c.Value(ContextKeyUserID))
}

func IsAdmin(c context.Context) bool {
	return cast.ToBool(c.Value(ContextKeyIsAdmin))
}

type MemStore struct {
	UserRoles   map[int][]int       `json:"user_roles" form:"user_roles"`
	PermRoles   map[int][]int       `json:"perm_roles" form:"perm_roles"`
	PermNameIdx map[string]int      `json:"perm_name_idx" form:"perm_name_idx"`
	UserPerms   map[int][]int       `json:"user_perms" form:"user_perms"`
	Perms       map[int]*PermSimple `json:"perms" form:"perms"`
}

type PermSimple struct {
	ID          int32  `json:"id"`
	Name        string `json:"name"`
	DisplayName string `json:"display_name"`
}

func (c *MemStore) Can(userId, permId int) bool {
	return len(lo.Intersect(c.UserRoles[userId], c.PermRoles[permId])) > 0
}

func (c *MemStore) CanName(userId int, permName string) bool {
	permId, ok := c.PermNameIdx[permName]
	if !ok {
		return false
	}
	return c.Can(userId, permId)
}
