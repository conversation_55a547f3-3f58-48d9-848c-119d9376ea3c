package redises

import (
	"context"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/redis/go-redis/v9"
	"time"
)

const luaScript = `
    if redis.call("GET", KEYS[1]) == ARGV[1] then
       return redis.call("DEL", KEYS[1])
    else
       return 0
    end`

// @autowire(set=infra)
func NewRedsync(rds *redis.Client) *redsync.Redsync {
	return redsync.New(goredis.NewPool((*redis.Client)(rds)))
}

// TryLock 尝试获取锁, true表示加锁成功，false表示加锁失败
func (rdb *Clients) TryLock(key string, value string, expiration time.Duration) (bool, error) {
	// 使用SET+ EXPIRE + NX原子命令尝试获取锁
	result, err := rdb.AdminRedis.SetNX(context.Background(), key, value, expiration).Result()
	if err != nil {
		return false, err
	}
	return result, nil
}

// Unlock 释放锁
func (rdb *Clients) Unlock(key string, value string) (bool, error) {
	// 使用Lua脚本保证原子性地检查和删除锁
	result, err := rdb.AdminRedis.Eval(context.Background(), luaScript, []string{key}, value).Result()
	if err != nil {
		return false, err
	}
	return result.(int64) == 1, nil
}
