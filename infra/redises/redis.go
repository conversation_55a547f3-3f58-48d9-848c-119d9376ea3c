package redises

import (
	"context"
	"fmt"
	"log"
	"log/slog"
	"os"
	"time"

	"git.7k7k.com/data/abAdmin/infra/config"
	"github.com/redis/go-redis/v9"
)

type Clients struct {
	AdminRedis *redis.Client // admin 后台使用
}

// @autowire(set=infra)
func NewClients(conf *config.Config) *Clients {
	return &Clients{
		AdminRedis: newRedis(conf, "admin"),
	}
}

func Use(cli *redis.Client) *Clients {
	return &Clients{
		AdminRedis: cli,
	}
}

func newRedis(conf *config.Config, db string) *redis.Client {
	redis.SetLogger(&logger{log.New(os.Stdout, "redis", log.Lshortfile|log.LstdFlags)})
	c := conf.DataBase.Redis[db]
	cli := redis.NewClient(&redis.Options{
		ClientName:   "abtest-admin",
		Addr:         c.<PERSON>,
		DB:           c.DB,
		Password:     c.Password,
		DialTimeout:  3 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
	})
	if err := cli.Ping(context.Background()).Err(); err != nil {
		slog.Info("redis ping failed", "config", c, "err", err.Error())
		panic(err)
	}
	return cli
}

type logger struct {
	log *log.Logger
}

func (l *logger) Printf(ctx context.Context, format string, v ...interface{}) {
	_ = l.log.Output(2, fmt.Sprintf(format, v...))
}
