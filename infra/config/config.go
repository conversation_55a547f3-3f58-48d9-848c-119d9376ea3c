package config

import (
	"encoding/json"
	"io"
	"os"
	"sync"

	"git.7k7k.com/data/abAdmin/gopkg/condition"
	"git.7k7k.com/data/abAdmin/model/dto"

	"github.com/BurntSushi/toml"
)

var once sync.Once

var GlobalConfig *Config

var (
	// Operators 操作符，operators.json 文件
	Operators map[condition.OpType]*dto.Operator
	// Strategies 策略 strategies.json 文件
	Strategies []*dto.Condition
	// Region 地区/国家列表 region.json
	Region []*dto.StrategyNode
	// Country 国家列表 region.json
	Country map[string]*dto.Country
	// ExpressionKeys 表达式的key，expression_keys.json
	ExpressionKeys map[string]*dto.ExpressionKey
)

type Config struct {
	Addr      string
	Mode      string
	AssetPath string
	ABURL     string `toml:"ab_url"`
	BIURL     string `toml:"bi_url"`
	DataBase  DataBase
	JWT       JWT
	Sentry    Sentry
	Task      TaskConf           `toml:"task"`
	ExpSync   map[string]ExpSync `toml:"exp_sync"`
}

func (c *Config) IsProd() bool {
	return c.Mode == "prod"
}

// TaskConf 任务的配置结构体
type TaskConf struct {
	AccountId                        int  `toml:"account_id"`                           // 定时任务的账号ID
	CloseExpEnabled                  bool `toml:"close_exp_enabled"`                    // true表示开启自动关闭实验任务
	ExpStrategyTriggerEnabled        bool `toml:"exp_strategy_trigger_enabled"`         // true表示开启实验策略触发任务
	CloseExpIntervalSecond           uint `toml:"close_exp_interval_second"`            // 关闭实验任务的调度间隔，单位秒
	CloseExpDaysAgo                  uint `toml:"close_exp_days_ago"`                   // 关闭实验任务，计划调度多少天前的实验，单位天
	ExpStrategyTriggerIntervalSecond uint `toml:"exp_strategy_trigger_interval_second"` // 实验策略的调度间隔，单位秒
}

type JWT struct {
	Key       string
	SSOSecret string `toml:"sso_secret"`
	Admin     []int
}
type Sentry struct {
	Env string `toml:"env"`
	DSN string `toml:"dsn"`
}

type DataBase struct {
	MySQL map[string]MySQL
	Redis map[string]Redis

	MySQLDefault string `toml:"mysql_default"`
}

type MySQL struct {
	DSN   string
	Debug bool
}

type Redis struct {
	Addr     string
	DB       int
	Password string
}

type ExpSync struct {
	ApiURL string `toml:"api_url"`
}

var (
	glocalConfigPath string
	assetsPath       string
)

func SetGlobalConfig(configPath, _assetPath string) {
	glocalConfigPath = configPath
	assetsPath = _assetPath
}

// @autowire(set=infra)
func LoadGlobalConfig() (*Config, error) {
	var err error
	GlobalConfig, err = LoadConfig(glocalConfigPath)
	if err != nil {
		return nil, err
	}

	ApplyENV(GlobalConfig)

	GlobalConfig.AssetPath = assetsPath
	return GlobalConfig, err
}
func LoadConfig(configPath string) (*Config, error) {
	tomlData, err := os.ReadFile(configPath)
	if err != nil {
		return nil, err
	}

	c := &Config{}
	_, err = toml.Decode(string(tomlData), c) // 修改这里，添加下划线以忽略第一个返回值
	if err != nil {                           // 添加错误检查
		return nil, err
	}

	return c, nil
}

// LoadStaticData 加载静态资源
func LoadStaticData() (err error) {
	once.Do(func() {
		// 操作符
		Operators = make(map[condition.OpType]*dto.Operator)
		err = loadFile(GlobalConfig.AssetPath+"operators.json", &Operators)
		if err != nil {
			return
		}
		// 策略
		err = loadFile(GlobalConfig.AssetPath+"strategies.json", &Strategies)
		if err != nil {
			return
		}

		// 加载地区/国家
		err = loadFile(GlobalConfig.AssetPath+"region.json", &Region)
		if err != nil {
			return
		}

		// 加载国家
		Country = make(map[string]*dto.Country)
		err = loadFile(GlobalConfig.AssetPath+"country_keys.json", &Country)
		if err != nil {
			return
		}

		// 条件表达式的Key
		ExpressionKeys = make(map[string]*dto.ExpressionKey)
		err = loadFile(GlobalConfig.AssetPath+"expression_keys.json", &ExpressionKeys)
	})
	return err
}

// 加载文件
func loadFile(filePath string, v any) error {
	file, err := os.Open(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	jsonData, err := io.ReadAll(file)
	if err != nil {
		return err
	}

	return json.Unmarshal(jsonData, &v)
}

func ApplyENV(c *Config) {
	if key, ok := os.LookupEnv("config_jwt_sso_secret"); ok {
		c.JWT.SSOSecret = key
	}
}
