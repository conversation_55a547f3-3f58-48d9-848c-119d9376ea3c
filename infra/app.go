package infra

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"

	"git.7k7k.com/data/abAdmin/service"
)

// @autowire.init(set=infra)
type Application struct {
	Config *Config

	HTTP *http.Server

	Auth           *service.AuthService
	Task           *service.TaskService
	ExpSyncService *service.ExpSyncService
	ExpLogService  *service.ExpLogService
	NotifyService  *service.NotifyService
	Listener       *service.Listener
}

func (app *Application) RunHTTP() {
	app.HTTP.Addr = app.Config.Addr
	go func() {
		err := app.HTTP.ListenAndServe()
		if err != nil && err != http.ErrServerClosed {
			log.Fatalf("HTTP Server ListenAndServe %s", err.Error())
		}
	}()

	// 定时任务
	ctx, cancel := context.WithCancel(context.Background())
	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		app.Task.Execute(ctx)
	}()

	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt, syscall.SIGKILL)
	<-quit
	cancel()
	wg.Wait()
}
