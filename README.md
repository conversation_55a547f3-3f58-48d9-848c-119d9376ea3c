# abtest admin

## 首次配置项目，安装依赖

	go install github.com/silenceper/gowatch@latest
	go install gorm.io/gen/tools/gentool@latest
	go install github.com/google/wire/cmd/wire@v0.6.0
	go install github.com/Just-maple/go-autowire/cmd/gutowire@latest

## 开发

更新 gorm-gen

	go run ./repository/cmd

更新 autowire

	make wire

运行

```
make gorun	# go run
make watch	# 依赖 gowatch 监控本地文件变化
```

从DB生成

```
# 主库
gentool -dsn "xxgame_abtest_slt:CJy9p+lh7s4Wc6PG@tcp(10.9.46.118:3306)/abtest_dev?charset=utf8mb4&parseTime=True&loc=Local" -onlyModel -outPath='./model' -tables "project_role_user"

# bi库 权限表
gentool -dsn "h5app:CggIkhuZBNo6MAP27j6K@tcp(10.9.66.246:3306)/ad?charset=utf8mb4&parseTime=True&loc=Local&timeout=3s&readTimeout=5s" -onlyModel -outPath='./model' -tables "users,roles,permissions,permission_role"
```
