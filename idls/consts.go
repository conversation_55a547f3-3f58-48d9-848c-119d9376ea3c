package idls

// 只增不改

type ExpState int32
type ExpStatus = ExpState

// 实验状态：1:草稿 2:待开始 3:灰度运行中 4:灰度运行中不进量 5:运行中 6:运行中不进量 7:已关闭
const (
	ExpStatusDraft ExpState = iota + 1
	ExpStatusPending
	ExpStatusRunning
	ExpStatusRunningWithoutFlow
	ExpStatusClosed
	ExpStatusGrayRunning
	ExpStatusGrayRunningWithoutFlow

	// 兼容分流引流
	ExpStatusRunningNoFlow        = ExpStatusRunningWithoutFlow
	ExpStatusGrayRelease          = ExpStatusGrayRunning
	ExpStatusGrayReleaseNoNewFlow = ExpStatusGrayRunningWithoutFlow
)

// Name 返回枚举值的字符串名称
func (e ExpState) Name() string {
	switch e {
	case ExpStatusDraft:
		return "草稿"
	case ExpStatusPending:
		return "待开始"
	case ExpStatusRunning:
		return "运行中"
	case ExpStatusRunningWithoutFlow:
		return "运行中不进量"
	case ExpStatusClosed:
		return "已关闭"
	case ExpStatusGrayRunning:
		return "灰度运行中"
	case ExpStatusGrayRunningWithoutFlow:
		return "灰度运行中不进量"
	}
	return "Unknown"
}
