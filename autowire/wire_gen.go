// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package autowire

import (
	"git.7k7k.com/data/abAdmin/gopkg/sso"
	"git.7k7k.com/data/abAdmin/httpd"
	"git.7k7k.com/data/abAdmin/infra"
	"git.7k7k.com/data/abAdmin/infra/config"
	"git.7k7k.com/data/abAdmin/infra/redises"
	"git.7k7k.com/data/abAdmin/repository"
	"git.7k7k.com/data/abAdmin/service"
	"git.7k7k.com/data/abAdmin/service/oapi"
)

// Injectors from wire.gen.go:

func InitializeApplication() (*infra.Application, func(), error) {
	configConfig, err := config.LoadGlobalConfig()
	if err != nil {
		return nil, nil, err
	}
	query := repository.InitDefaultQuery(configConfig)
	serviceQuery := service.Query{}
	layerService := &service.LayerService{
		DS:    query,
		Query: serviceQuery,
	}
	clients := redises.NewClients(configConfig)
	metricService := &service.MetricService{
		RedisClts: clients,
	}
	groupService := &service.GroupService{
		DS:            query,
		MetricService: metricService,
	}
	iUserInfoDo := repository.NewIUserInfoDo(query)
	queryForBIv1 := repository.InitQueryForBIv1(configConfig)
	authService := &service.AuthService{
		UserInfoDo:   iUserInfoDo,
		Query:        query,
		QueryForBIv1: queryForBIv1,
	}
	projectService := &service.ProjectService{
		DS:           query,
		LayerService: layerService,
		GroupService: groupService,
		AuthService:  authService,
	}
	projectController := httpd.ProjectController{
		ProjectService: projectService,
	}
	layerController := httpd.LayerController{
		LayerService: layerService,
	}
	batchService := &service.BatchService{
		DS:    query,
		Query: serviceQuery,
	}
	tagService := &service.TagService{
		DS:    query,
		Query: serviceQuery,
	}
	strategyService := &service.StrategyService{
		DS: query,
	}
	approvalRuleManageService := &service.ApprovalRuleManageService{
		DS:           query,
		Query:        serviceQuery,
		QueryForBIv1: queryForBIv1,
	}
	events := service.NewEvents()
	approvalTaskManageService := &service.ApprovalTaskManageService{
		DS:           query,
		Query:        serviceQuery,
		QueryForBIv1: queryForBIv1,
		Events:       events,
		LayerService: layerService,
	}
	expLogService := &service.ExpLogService{
		DS:                        query,
		Query:                     serviceQuery,
		MetricService:             metricService,
		UserQuery:                 queryForBIv1,
		LayerService:              layerService,
		GroupService:              groupService,
		BatchService:              batchService,
		TagService:                tagService,
		StrategyService:           strategyService,
		ApprovalRuleManageService: approvalRuleManageService,
		ApprovalTaskManageService: approvalTaskManageService,
	}
	expStateService := &service.ExpStateService{
		DS:                        query,
		UserQuery:                 queryForBIv1,
		MetricService:             metricService,
		ExpLogService:             expLogService,
		LayerService:              layerService,
		GroupService:              groupService,
		ProjectService:            projectService,
		Events:                    events,
		ApprovalRuleManageService: approvalRuleManageService,
		ApprovalTaskManageService: approvalTaskManageService,
	}
	expService := &service.ExpService{
		DS:                        query,
		LayerService:              layerService,
		MetricService:             metricService,
		Query:                     serviceQuery,
		QueryForBIv1:              queryForBIv1,
		ExpStateService:           expStateService,
		ExpLogService:             expLogService,
		GroupService:              groupService,
		Events:                    events,
		ProjectService:            projectService,
		ApprovalTaskManageService: approvalTaskManageService,
		ApprovalRuleManageService: approvalRuleManageService,
	}
	expBatchService := &service.ExpBatchService{
		DS:              query,
		LayerService:    layerService,
		MetricService:   metricService,
		ExpStateService: expStateService,
		ExpLogService:   expLogService,
		GroupService:    groupService,
		ExpService:      expService,
		StrategyService: strategyService,
	}
	expController := httpd.ExpController{
		ExpService:      expService,
		GroupService:    groupService,
		ExpBatchService: expBatchService,
	}
	tagController := httpd.TagController{
		TagService: tagService,
	}
	strategyController := httpd.StrategyController{
		StrategyService: strategyService,
	}
	batchController := httpd.BatchController{
		BatchService: batchService,
	}
	groupController := httpd.GroupController{
		GroupService: groupService,
	}
	expLogController := httpd.ExpLogController{
		ExpLogService: expLogService,
	}
	expToolsController := httpd.ExpToolsController{}
	serviceApprovalRuleManageService := service.ApprovalRuleManageService{
		DS:           query,
		Query:        serviceQuery,
		QueryForBIv1: queryForBIv1,
	}
	approvalRuleController := httpd.ApprovalRuleController{
		ApprovalRuleManageService: serviceApprovalRuleManageService,
	}
	serviceApprovalTaskManageService := service.ApprovalTaskManageService{
		DS:           query,
		Query:        serviceQuery,
		QueryForBIv1: queryForBIv1,
		Events:       events,
		LayerService: layerService,
	}
	approvalTaskController := httpd.ApprovalTaskController{
		ApprovalTaskManageService: serviceApprovalTaskManageService,
	}
	serviceExpService := service.ExpService{
		DS:                        query,
		LayerService:              layerService,
		MetricService:             metricService,
		Query:                     serviceQuery,
		QueryForBIv1:              queryForBIv1,
		ExpStateService:           expStateService,
		ExpLogService:             expLogService,
		GroupService:              groupService,
		Events:                    events,
		ProjectService:            projectService,
		ApprovalTaskManageService: approvalTaskManageService,
		ApprovalRuleManageService: approvalRuleManageService,
	}
	expExtraService := &service.ExpExtraService{
		DS:         query,
		ExpService: serviceExpService,
	}
	autoCompleteService := &service.AutoCompleteService{
		Query:        query,
		QueryForBIv1: queryForBIv1,
	}
	dingtalkCommandService := &service.DingtalkCommandService{
		ExpService:   expService,
		GroupService: groupService,
	}
	ssoService := sso.NewSSOService(configConfig, queryForBIv1)
	query2 := &service.Query{}
	oapiExpService := &oapi.ExpService{
		ExpService: expService,
	}
	openAPI := &httpd.OpenAPI{
		ExpService: oapiExpService,
	}
	handlers := &httpd.Handlers{
		ProjectController:      projectController,
		LayerController:        layerController,
		ExpController:          expController,
		TagController:          tagController,
		StrategyController:     strategyController,
		BatchController:        batchController,
		GroupController:        groupController,
		ExpLogController:       expLogController,
		ExpToolsController:     expToolsController,
		ApprovalRuleController: approvalRuleController,
		ApprovalTaskController: approvalTaskController,
		LayerService:           layerService,
		ExpExtraService:        expExtraService,
		AuthService:            authService,
		AutoCompleteService:    autoCompleteService,
		DingtalkCmd:            dingtalkCommandService,
		SSOService:             ssoService,
		Query:                  query2,
		OpenAPI:                openAPI,
	}
	server, cleanup := httpd.NewHTTPServer(configConfig, handlers)
	taskService := &service.TaskService{
		DS:              query,
		Query:           serviceQuery,
		Rdb:             clients,
		MetricService:   metricService,
		ExpStateService: expStateService,
		ExpService:      expService,
		GroupService:    groupService,
	}
	expSyncService := service.NewExpSyncService(query, expService, groupService, events, configConfig)
	notifyService := &service.NotifyService{
		ExpService:     expService,
		Config:         configConfig,
		ProjectService: projectService,
	}
	listener := &service.Listener{
		Events:         events,
		ExpSyncService: expSyncService,
		NotifyService:  notifyService,
	}
	application := &infra.Application{
		Config:         configConfig,
		HTTP:           server,
		Auth:           authService,
		Task:           taskService,
		ExpSyncService: expSyncService,
		ExpLogService:  expLogService,
		NotifyService:  notifyService,
		Listener:       listener,
	}
	return application, func() {
		cleanup()
	}, nil
}
