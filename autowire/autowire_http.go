// Code generated by go-autowire. DO NOT EDIT.

//go:build wireinject
// +build wireinject

package autowire

import (
	"github.com/google/wire"

	"git.7k7k.com/data/abAdmin/httpd"
)

var HttpSet = wire.NewSet(
	wire.Struct(new(httpd.ApprovalRuleController), "*"),

	wire.Struct(new(httpd.ApprovalTaskController), "*"),

	wire.Struct(new(httpd.BatchController), "*"),

	wire.Struct(new(httpd.ExpController), "*"),

	wire.Struct(new(httpd.ExpLogController), "*"),

	wire.Struct(new(httpd.ExpToolsController), "*"),

	wire.Struct(new(httpd.GroupController), "*"),

	wire.Struct(new(httpd.Handlers), "*"),

	wire.Struct(new(httpd.LayerController), "*"),

	httpd.NewHTTPServer,

	wire.Struct(new(httpd.OpenAPI), "*"),

	wire.Struct(new(httpd.ProjectController), "*"),

	wire.Struct(new(httpd.StrategyController), "*"),

	wire.Struct(new(httpd.TagController), "*"),
)
