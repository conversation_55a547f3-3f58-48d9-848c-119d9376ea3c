// Code generated by go-autowire. DO NOT EDIT.

//go:build wireinject
// +build wireinject

package autowire

import (
	"github.com/google/wire"

	"git.7k7k.com/data/abAdmin/service"
	"git.7k7k.com/data/abAdmin/service/oapi"
)

var ServiceSet = wire.NewSet(
	wire.Struct(new(service.ApprovalRuleManageService), "*"),

	wire.Struct(new(service.ApprovalTaskManageService), "*"),

	wire.Struct(new(service.AuthService), "*"),

	wire.Struct(new(service.AutoCompleteService), "*"),

	wire.Struct(new(service.BatchService), "*"),

	wire.Struct(new(service.DingtalkCommandService), "*"),

	wire.Struct(new(service.ExpBatchService), "*"),

	wire.Struct(new(service.ExpExtraService), "*"),

	wire.Struct(new(service.ExpLogService), "*"),

	wire.Struct(new(service.ExpService), "*"),

	wire.Struct(new(service.ExpStateService), "*"),

	wire.Struct(new(service.ExpWebService), "*"),

	wire.Struct(new(service.GroupService), "*"),

	wire.Struct(new(service.LayerService), "*"),

	wire.Struct(new(service.Listener), "*"),

	wire.Struct(new(service.MetricService), "*"),

	service.NewEvents,

	service.NewExpSyncService,

	wire.Struct(new(service.NotifyService), "*"),

	wire.Struct(new(service.ProjectService), "*"),

	wire.Struct(new(service.Query), "*"),

	wire.Struct(new(service.StrategyService), "*"),

	wire.Struct(new(service.TagService), "*"),

	wire.Struct(new(service.TaskService), "*"),

	wire.Struct(new(oapi.ExpService), "*"),
)
