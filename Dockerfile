# 构建阶段
FROM 459528473147.dkr.ecr.us-east-2.amazonaws.com/golang:1.22-alpine3.20 as builder

# 设置工作目录
WORKDIR /workspace/golang

# 将 go.mod 和 go.sum 复制到容器中
COPY go.mod go.sum ./

# 设置 GOPROXY 环境变量
ENV GOPROXY=https://goproxy.cn,direct
RUN go env -w GOPROXY=${GOPROXY}

# 拉取依赖，
RUN go mod download

# 复制源代码到容器中
COPY . .

#生成 go.work 文件
#RUN go work init ./core . || true

# 编译 Go 程序
RUN go build -o main .

# 发布阶段
FROM 10.9.100.89:5000/alpine:3.19

# 更新和安装所需包
RUN apk update && \
    apk add --no-cache tzdata

# 设置时区为 UTC+8
RUN cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

# 设置工作目录
WORKDIR /workspace/golang

# 从构建阶段复制编译好的二进制文件
COPY --from=builder /workspace/golang/main .

# 根据 ENV 环境变量选择并复制相应的配置文件
ARG ENV=dev
COPY --from=builder /workspace/golang/resource/config/config_${ENV}.toml ./config/api.toml
COPY --from=builder /workspace/golang/resource/assets/* ./assets/

# 创建并挂载日志目录
VOLUME ["/workspace/golang/logs"]

# 暴露端口
ARG PORT=8001
EXPOSE ${PORT}

# 设置环境变量
ENV APP_ROOT /workspace/golang
ENV APP_MODE debug

# 启动命令
CMD ["./main", "-c", "./config/api.toml", "-a", "./assets/"]
