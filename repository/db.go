package repository

import (
	"context"
	"log/slog"

	"git.7k7k.com/pkg/common/logs"

	"git.7k7k.com/data/abAdmin/infra/config"
	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/repository/dao"
	"git.7k7k.com/pkg/common/gosentry"
	"github.com/samber/lo"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var Logger = logger.Default

var dbs map[string]*gorm.DB = make(map[string]*gorm.DB, 4)

// 新增一个公共函数，用于初始化数据库连接，接受数据库类型作为参数
func initDB(dbname string, c *config.Config) *gorm.DB {
	sql, ok := c.DataBase.MySQL[dbname]
	if !ok {
		panic("mysql config not found " + dbname)
	}

	dsn := sql.DSN
	config := &gorm.Config{
		Logger: Logger,
	}
	gormdb, err := gorm.Open(mysql.Open(dsn), config)
	if err != nil {
		slog.Error("init db error", "dbname", dbname, "dsn", dsn, "error", err.Error())
		panic("无法连接到数据库")
	}
	if sql.Debug {
		gormdb = gormdb.Debug()
	}

	gormdb.Use(&gosentry.GormPlugin{})
	gormdb.Use(&logs.GormPlugin{})
	return gormdb
}

func initOnce(dbname string, c *config.Config) *gorm.DB {
	if _, ok := dbs[dbname]; !ok {
		// TODO 加锁
		dbs[dbname] = initDB(dbname, c)
	}

	return dbs[dbname]
}

// @autowire(set=dao)
func InitDefaultQuery(c *config.Config) *dao.Query {
	return dao.Use(initOnce(c.DataBase.MySQLDefault, c))
}

type QueryForBIv1 dao.Query

// @autowire(set=dao)
func InitQueryForBIv1(c *config.Config) *QueryForBIv1 {
	return (*QueryForBIv1)(dao.Use(initOnce("ad", c)))
}

// QueryUserByIds 查找用户 ctx:上下文，q：用户查询服务，userIds：用户ID集合
func (q *QueryForBIv1) QueryUserByIds(ctx context.Context, userIds ...int32) (map[int32]*model.User, error) {
	uq := q.User
	users, err := uq.WithContext(ctx).
		Select(uq.ID, uq.Realname).
		Where(uq.ID.In(userIds...)).Find()
	if err != nil {
		return nil, err
	}
	return lo.KeyBy(users, func(user *model.User) int32 { return user.ID }), nil
}
