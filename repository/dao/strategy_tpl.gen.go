// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.7k7k.com/data/abAdmin/model"
)

func newStrategyTpl(db *gorm.DB, opts ...gen.DOOption) strategyTpl {
	_strategyTpl := strategyTpl{}

	_strategyTpl.strategyTplDo.UseDB(db, opts...)
	_strategyTpl.strategyTplDo.UseModel(&model.StrategyTpl{})

	tableName := _strategyTpl.strategyTplDo.TableName()
	_strategyTpl.ALL = field.NewAsterisk(tableName)
	_strategyTpl.ID = field.NewInt64(tableName, "id")
	_strategyTpl.Name = field.NewString(tableName, "name")
	_strategyTpl.ProjectID = field.NewInt64(tableName, "project_id")
	_strategyTpl.TplType = field.NewInt32(tableName, "tpl_type")
	_strategyTpl.TplData = field.NewString(tableName, "tpl_data")
	_strategyTpl.State = field.NewInt32(tableName, "state")
	_strategyTpl.CreateTime = field.NewTime(tableName, "create_time")
	_strategyTpl.UpdateTime = field.NewTime(tableName, "update_time")

	_strategyTpl.fillFieldMap()

	return _strategyTpl
}

// strategyTpl 策略模版表
type strategyTpl struct {
	strategyTplDo

	ALL        field.Asterisk
	ID         field.Int64
	Name       field.String // 策略名称
	ProjectID  field.Int64  // ab_project关联id
	TplType    field.Int32  // 类型：1 触发用户规则 4 分流用户规则
	TplData    field.String // 模版内容
	State      field.Int32  // 状态：1删除 2启用 3禁用
	CreateTime field.Time   // 创建时间
	UpdateTime field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (s strategyTpl) Table(newTableName string) *strategyTpl {
	s.strategyTplDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s strategyTpl) As(alias string) *strategyTpl {
	s.strategyTplDo.DO = *(s.strategyTplDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *strategyTpl) updateTableName(table string) *strategyTpl {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.Name = field.NewString(table, "name")
	s.ProjectID = field.NewInt64(table, "project_id")
	s.TplType = field.NewInt32(table, "tpl_type")
	s.TplData = field.NewString(table, "tpl_data")
	s.State = field.NewInt32(table, "state")
	s.CreateTime = field.NewTime(table, "create_time")
	s.UpdateTime = field.NewTime(table, "update_time")

	s.fillFieldMap()

	return s
}

func (s *strategyTpl) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *strategyTpl) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 8)
	s.fieldMap["id"] = s.ID
	s.fieldMap["name"] = s.Name
	s.fieldMap["project_id"] = s.ProjectID
	s.fieldMap["tpl_type"] = s.TplType
	s.fieldMap["tpl_data"] = s.TplData
	s.fieldMap["state"] = s.State
	s.fieldMap["create_time"] = s.CreateTime
	s.fieldMap["update_time"] = s.UpdateTime
}

func (s strategyTpl) clone(db *gorm.DB) strategyTpl {
	s.strategyTplDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s strategyTpl) replaceDB(db *gorm.DB) strategyTpl {
	s.strategyTplDo.ReplaceDB(db)
	return s
}

type strategyTplDo struct{ gen.DO }

type IStrategyTplDo interface {
	gen.SubQuery
	Debug() IStrategyTplDo
	WithContext(ctx context.Context) IStrategyTplDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IStrategyTplDo
	WriteDB() IStrategyTplDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IStrategyTplDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IStrategyTplDo
	Not(conds ...gen.Condition) IStrategyTplDo
	Or(conds ...gen.Condition) IStrategyTplDo
	Select(conds ...field.Expr) IStrategyTplDo
	Where(conds ...gen.Condition) IStrategyTplDo
	Order(conds ...field.Expr) IStrategyTplDo
	Distinct(cols ...field.Expr) IStrategyTplDo
	Omit(cols ...field.Expr) IStrategyTplDo
	Join(table schema.Tabler, on ...field.Expr) IStrategyTplDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IStrategyTplDo
	RightJoin(table schema.Tabler, on ...field.Expr) IStrategyTplDo
	Group(cols ...field.Expr) IStrategyTplDo
	Having(conds ...gen.Condition) IStrategyTplDo
	Limit(limit int) IStrategyTplDo
	Offset(offset int) IStrategyTplDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IStrategyTplDo
	Unscoped() IStrategyTplDo
	Create(values ...*model.StrategyTpl) error
	CreateInBatches(values []*model.StrategyTpl, batchSize int) error
	Save(values ...*model.StrategyTpl) error
	First() (*model.StrategyTpl, error)
	Take() (*model.StrategyTpl, error)
	Last() (*model.StrategyTpl, error)
	Find() ([]*model.StrategyTpl, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.StrategyTpl, err error)
	FindInBatches(result *[]*model.StrategyTpl, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.StrategyTpl) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IStrategyTplDo
	Assign(attrs ...field.AssignExpr) IStrategyTplDo
	Joins(fields ...field.RelationField) IStrategyTplDo
	Preload(fields ...field.RelationField) IStrategyTplDo
	FirstOrInit() (*model.StrategyTpl, error)
	FirstOrCreate() (*model.StrategyTpl, error)
	FindByPage(offset int, limit int) (result []*model.StrategyTpl, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IStrategyTplDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s strategyTplDo) Debug() IStrategyTplDo {
	return s.withDO(s.DO.Debug())
}

func (s strategyTplDo) WithContext(ctx context.Context) IStrategyTplDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s strategyTplDo) ReadDB() IStrategyTplDo {
	return s.Clauses(dbresolver.Read)
}

func (s strategyTplDo) WriteDB() IStrategyTplDo {
	return s.Clauses(dbresolver.Write)
}

func (s strategyTplDo) Session(config *gorm.Session) IStrategyTplDo {
	return s.withDO(s.DO.Session(config))
}

func (s strategyTplDo) Clauses(conds ...clause.Expression) IStrategyTplDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s strategyTplDo) Returning(value interface{}, columns ...string) IStrategyTplDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s strategyTplDo) Not(conds ...gen.Condition) IStrategyTplDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s strategyTplDo) Or(conds ...gen.Condition) IStrategyTplDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s strategyTplDo) Select(conds ...field.Expr) IStrategyTplDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s strategyTplDo) Where(conds ...gen.Condition) IStrategyTplDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s strategyTplDo) Order(conds ...field.Expr) IStrategyTplDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s strategyTplDo) Distinct(cols ...field.Expr) IStrategyTplDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s strategyTplDo) Omit(cols ...field.Expr) IStrategyTplDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s strategyTplDo) Join(table schema.Tabler, on ...field.Expr) IStrategyTplDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s strategyTplDo) LeftJoin(table schema.Tabler, on ...field.Expr) IStrategyTplDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s strategyTplDo) RightJoin(table schema.Tabler, on ...field.Expr) IStrategyTplDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s strategyTplDo) Group(cols ...field.Expr) IStrategyTplDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s strategyTplDo) Having(conds ...gen.Condition) IStrategyTplDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s strategyTplDo) Limit(limit int) IStrategyTplDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s strategyTplDo) Offset(offset int) IStrategyTplDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s strategyTplDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IStrategyTplDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s strategyTplDo) Unscoped() IStrategyTplDo {
	return s.withDO(s.DO.Unscoped())
}

func (s strategyTplDo) Create(values ...*model.StrategyTpl) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s strategyTplDo) CreateInBatches(values []*model.StrategyTpl, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s strategyTplDo) Save(values ...*model.StrategyTpl) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s strategyTplDo) First() (*model.StrategyTpl, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.StrategyTpl), nil
	}
}

func (s strategyTplDo) Take() (*model.StrategyTpl, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.StrategyTpl), nil
	}
}

func (s strategyTplDo) Last() (*model.StrategyTpl, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.StrategyTpl), nil
	}
}

func (s strategyTplDo) Find() ([]*model.StrategyTpl, error) {
	result, err := s.DO.Find()
	return result.([]*model.StrategyTpl), err
}

func (s strategyTplDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.StrategyTpl, err error) {
	buf := make([]*model.StrategyTpl, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s strategyTplDo) FindInBatches(result *[]*model.StrategyTpl, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s strategyTplDo) Attrs(attrs ...field.AssignExpr) IStrategyTplDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s strategyTplDo) Assign(attrs ...field.AssignExpr) IStrategyTplDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s strategyTplDo) Joins(fields ...field.RelationField) IStrategyTplDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s strategyTplDo) Preload(fields ...field.RelationField) IStrategyTplDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s strategyTplDo) FirstOrInit() (*model.StrategyTpl, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.StrategyTpl), nil
	}
}

func (s strategyTplDo) FirstOrCreate() (*model.StrategyTpl, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.StrategyTpl), nil
	}
}

func (s strategyTplDo) FindByPage(offset int, limit int) (result []*model.StrategyTpl, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s strategyTplDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s strategyTplDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s strategyTplDo) Delete(models ...*model.StrategyTpl) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *strategyTplDo) withDO(do gen.Dao) *strategyTplDo {
	s.DO = *do.(*gen.DO)
	return s
}
