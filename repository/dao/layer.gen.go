// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.7k7k.com/data/abAdmin/model"
)

func newLayer(db *gorm.DB, opts ...gen.DOOption) layer {
	_layer := layer{}

	_layer.layerDo.UseDB(db, opts...)
	_layer.layerDo.UseModel(&model.Layer{})

	tableName := _layer.layerDo.TableName()
	_layer.ALL = field.NewAsterisk(tableName)
	_layer.ID = field.NewInt64(tableName, "id")
	_layer.ProjectID = field.NewInt64(tableName, "project_id")
	_layer.Name = field.NewString(tableName, "name")
	_layer.IsExclusion = field.NewInt32(tableName, "is_exclusion")
	_layer.Rate = field.NewInt32(tableName, "rate")
	_layer.Level = field.NewInt32(tableName, "level")
	_layer.Desc = field.NewString(tableName, "desc")
	_layer.State = field.NewInt32(tableName, "state")
	_layer.DownTime = field.NewTime(tableName, "down_time")
	_layer.UpdateTime = field.NewTime(tableName, "update_time")
	_layer.CreateTime = field.NewTime(tableName, "create_time")

	_layer.fillFieldMap()

	return _layer
}

// layer 流量层表
type layer struct {
	layerDo

	ALL         field.Asterisk
	ID          field.Int64
	ProjectID   field.Int64  // ab_project关联id
	Name        field.String // 流量层名称
	IsExclusion field.Int32  // 是否互斥：1是 2否
	Rate        field.Int32  // 独占占比
	Level       field.Int32  // 分层等级，1低级，2中级，3高级
	Desc        field.String // 描述
	State       field.Int32  // 状态：1在线 2下线
	DownTime    field.Time   // 下线时间
	UpdateTime  field.Time   // 更新时间
	CreateTime  field.Time   // 创建时间

	fieldMap map[string]field.Expr
}

func (l layer) Table(newTableName string) *layer {
	l.layerDo.UseTable(newTableName)
	return l.updateTableName(newTableName)
}

func (l layer) As(alias string) *layer {
	l.layerDo.DO = *(l.layerDo.As(alias).(*gen.DO))
	return l.updateTableName(alias)
}

func (l *layer) updateTableName(table string) *layer {
	l.ALL = field.NewAsterisk(table)
	l.ID = field.NewInt64(table, "id")
	l.ProjectID = field.NewInt64(table, "project_id")
	l.Name = field.NewString(table, "name")
	l.IsExclusion = field.NewInt32(table, "is_exclusion")
	l.Rate = field.NewInt32(table, "rate")
	l.Level = field.NewInt32(table, "level")
	l.Desc = field.NewString(table, "desc")
	l.State = field.NewInt32(table, "state")
	l.DownTime = field.NewTime(table, "down_time")
	l.UpdateTime = field.NewTime(table, "update_time")
	l.CreateTime = field.NewTime(table, "create_time")

	l.fillFieldMap()

	return l
}

func (l *layer) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := l.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (l *layer) fillFieldMap() {
	l.fieldMap = make(map[string]field.Expr, 11)
	l.fieldMap["id"] = l.ID
	l.fieldMap["project_id"] = l.ProjectID
	l.fieldMap["name"] = l.Name
	l.fieldMap["is_exclusion"] = l.IsExclusion
	l.fieldMap["rate"] = l.Rate
	l.fieldMap["level"] = l.Level
	l.fieldMap["desc"] = l.Desc
	l.fieldMap["state"] = l.State
	l.fieldMap["down_time"] = l.DownTime
	l.fieldMap["update_time"] = l.UpdateTime
	l.fieldMap["create_time"] = l.CreateTime
}

func (l layer) clone(db *gorm.DB) layer {
	l.layerDo.ReplaceConnPool(db.Statement.ConnPool)
	return l
}

func (l layer) replaceDB(db *gorm.DB) layer {
	l.layerDo.ReplaceDB(db)
	return l
}

type layerDo struct{ gen.DO }

type ILayerDo interface {
	gen.SubQuery
	Debug() ILayerDo
	WithContext(ctx context.Context) ILayerDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ILayerDo
	WriteDB() ILayerDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ILayerDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ILayerDo
	Not(conds ...gen.Condition) ILayerDo
	Or(conds ...gen.Condition) ILayerDo
	Select(conds ...field.Expr) ILayerDo
	Where(conds ...gen.Condition) ILayerDo
	Order(conds ...field.Expr) ILayerDo
	Distinct(cols ...field.Expr) ILayerDo
	Omit(cols ...field.Expr) ILayerDo
	Join(table schema.Tabler, on ...field.Expr) ILayerDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ILayerDo
	RightJoin(table schema.Tabler, on ...field.Expr) ILayerDo
	Group(cols ...field.Expr) ILayerDo
	Having(conds ...gen.Condition) ILayerDo
	Limit(limit int) ILayerDo
	Offset(offset int) ILayerDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ILayerDo
	Unscoped() ILayerDo
	Create(values ...*model.Layer) error
	CreateInBatches(values []*model.Layer, batchSize int) error
	Save(values ...*model.Layer) error
	First() (*model.Layer, error)
	Take() (*model.Layer, error)
	Last() (*model.Layer, error)
	Find() ([]*model.Layer, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Layer, err error)
	FindInBatches(result *[]*model.Layer, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.Layer) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ILayerDo
	Assign(attrs ...field.AssignExpr) ILayerDo
	Joins(fields ...field.RelationField) ILayerDo
	Preload(fields ...field.RelationField) ILayerDo
	FirstOrInit() (*model.Layer, error)
	FirstOrCreate() (*model.Layer, error)
	FindByPage(offset int, limit int) (result []*model.Layer, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ILayerDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (l layerDo) Debug() ILayerDo {
	return l.withDO(l.DO.Debug())
}

func (l layerDo) WithContext(ctx context.Context) ILayerDo {
	return l.withDO(l.DO.WithContext(ctx))
}

func (l layerDo) ReadDB() ILayerDo {
	return l.Clauses(dbresolver.Read)
}

func (l layerDo) WriteDB() ILayerDo {
	return l.Clauses(dbresolver.Write)
}

func (l layerDo) Session(config *gorm.Session) ILayerDo {
	return l.withDO(l.DO.Session(config))
}

func (l layerDo) Clauses(conds ...clause.Expression) ILayerDo {
	return l.withDO(l.DO.Clauses(conds...))
}

func (l layerDo) Returning(value interface{}, columns ...string) ILayerDo {
	return l.withDO(l.DO.Returning(value, columns...))
}

func (l layerDo) Not(conds ...gen.Condition) ILayerDo {
	return l.withDO(l.DO.Not(conds...))
}

func (l layerDo) Or(conds ...gen.Condition) ILayerDo {
	return l.withDO(l.DO.Or(conds...))
}

func (l layerDo) Select(conds ...field.Expr) ILayerDo {
	return l.withDO(l.DO.Select(conds...))
}

func (l layerDo) Where(conds ...gen.Condition) ILayerDo {
	return l.withDO(l.DO.Where(conds...))
}

func (l layerDo) Order(conds ...field.Expr) ILayerDo {
	return l.withDO(l.DO.Order(conds...))
}

func (l layerDo) Distinct(cols ...field.Expr) ILayerDo {
	return l.withDO(l.DO.Distinct(cols...))
}

func (l layerDo) Omit(cols ...field.Expr) ILayerDo {
	return l.withDO(l.DO.Omit(cols...))
}

func (l layerDo) Join(table schema.Tabler, on ...field.Expr) ILayerDo {
	return l.withDO(l.DO.Join(table, on...))
}

func (l layerDo) LeftJoin(table schema.Tabler, on ...field.Expr) ILayerDo {
	return l.withDO(l.DO.LeftJoin(table, on...))
}

func (l layerDo) RightJoin(table schema.Tabler, on ...field.Expr) ILayerDo {
	return l.withDO(l.DO.RightJoin(table, on...))
}

func (l layerDo) Group(cols ...field.Expr) ILayerDo {
	return l.withDO(l.DO.Group(cols...))
}

func (l layerDo) Having(conds ...gen.Condition) ILayerDo {
	return l.withDO(l.DO.Having(conds...))
}

func (l layerDo) Limit(limit int) ILayerDo {
	return l.withDO(l.DO.Limit(limit))
}

func (l layerDo) Offset(offset int) ILayerDo {
	return l.withDO(l.DO.Offset(offset))
}

func (l layerDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ILayerDo {
	return l.withDO(l.DO.Scopes(funcs...))
}

func (l layerDo) Unscoped() ILayerDo {
	return l.withDO(l.DO.Unscoped())
}

func (l layerDo) Create(values ...*model.Layer) error {
	if len(values) == 0 {
		return nil
	}
	return l.DO.Create(values)
}

func (l layerDo) CreateInBatches(values []*model.Layer, batchSize int) error {
	return l.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (l layerDo) Save(values ...*model.Layer) error {
	if len(values) == 0 {
		return nil
	}
	return l.DO.Save(values)
}

func (l layerDo) First() (*model.Layer, error) {
	if result, err := l.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Layer), nil
	}
}

func (l layerDo) Take() (*model.Layer, error) {
	if result, err := l.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Layer), nil
	}
}

func (l layerDo) Last() (*model.Layer, error) {
	if result, err := l.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Layer), nil
	}
}

func (l layerDo) Find() ([]*model.Layer, error) {
	result, err := l.DO.Find()
	return result.([]*model.Layer), err
}

func (l layerDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Layer, err error) {
	buf := make([]*model.Layer, 0, batchSize)
	err = l.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (l layerDo) FindInBatches(result *[]*model.Layer, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return l.DO.FindInBatches(result, batchSize, fc)
}

func (l layerDo) Attrs(attrs ...field.AssignExpr) ILayerDo {
	return l.withDO(l.DO.Attrs(attrs...))
}

func (l layerDo) Assign(attrs ...field.AssignExpr) ILayerDo {
	return l.withDO(l.DO.Assign(attrs...))
}

func (l layerDo) Joins(fields ...field.RelationField) ILayerDo {
	for _, _f := range fields {
		l = *l.withDO(l.DO.Joins(_f))
	}
	return &l
}

func (l layerDo) Preload(fields ...field.RelationField) ILayerDo {
	for _, _f := range fields {
		l = *l.withDO(l.DO.Preload(_f))
	}
	return &l
}

func (l layerDo) FirstOrInit() (*model.Layer, error) {
	if result, err := l.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Layer), nil
	}
}

func (l layerDo) FirstOrCreate() (*model.Layer, error) {
	if result, err := l.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Layer), nil
	}
}

func (l layerDo) FindByPage(offset int, limit int) (result []*model.Layer, count int64, err error) {
	result, err = l.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = l.Offset(-1).Limit(-1).Count()
	return
}

func (l layerDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = l.Count()
	if err != nil {
		return
	}

	err = l.Offset(offset).Limit(limit).Scan(result)
	return
}

func (l layerDo) Scan(result interface{}) (err error) {
	return l.DO.Scan(result)
}

func (l layerDo) Delete(models ...*model.Layer) (result gen.ResultInfo, err error) {
	return l.DO.Delete(models)
}

func (l *layerDo) withDO(do gen.Dao) *layerDo {
	l.DO = *do.(*gen.DO)
	return l
}
