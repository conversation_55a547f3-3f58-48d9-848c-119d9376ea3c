// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:             db,
		ApprovalRule:   newApprovalRule(db, opts...),
		ApprovalTask:   newApprovalTask(db, opts...),
		Batch:          newBatch(db, opts...),
		Exp:            newExp(db, opts...),
		ExpLog:         newExpLog(db, opts...),
		ExpTask:        newExpTask(db, opts...),
		Group:          newGroup(db, opts...),
		Layer:          newLayer(db, opts...),
		ParamsType:     newParamsType(db, opts...),
		Permission:     newPermission(db, opts...),
		PermissionRole: newPermissionRole(db, opts...),
		Project:        newProject(db, opts...),
		Role:           newRole(db, opts...),
		SplitGroup:     newSplitGroup(db, opts...),
		StrategyTpl:    newStrategyTpl(db, opts...),
		Tag:            newTag(db, opts...),
		User:           newUser(db, opts...),
		UserInfo:       newUserInfo(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	ApprovalRule   approvalRule
	ApprovalTask   approvalTask
	Batch          batch
	Exp            exp
	ExpLog         expLog
	ExpTask        expTask
	Group          group
	Layer          layer
	ParamsType     paramsType
	Permission     permission
	PermissionRole permissionRole
	Project        project
	Role           role
	SplitGroup     splitGroup
	StrategyTpl    strategyTpl
	Tag            tag
	User           user
	UserInfo       userInfo
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:             db,
		ApprovalRule:   q.ApprovalRule.clone(db),
		ApprovalTask:   q.ApprovalTask.clone(db),
		Batch:          q.Batch.clone(db),
		Exp:            q.Exp.clone(db),
		ExpLog:         q.ExpLog.clone(db),
		ExpTask:        q.ExpTask.clone(db),
		Group:          q.Group.clone(db),
		Layer:          q.Layer.clone(db),
		ParamsType:     q.ParamsType.clone(db),
		Permission:     q.Permission.clone(db),
		PermissionRole: q.PermissionRole.clone(db),
		Project:        q.Project.clone(db),
		Role:           q.Role.clone(db),
		SplitGroup:     q.SplitGroup.clone(db),
		StrategyTpl:    q.StrategyTpl.clone(db),
		Tag:            q.Tag.clone(db),
		User:           q.User.clone(db),
		UserInfo:       q.UserInfo.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:             db,
		ApprovalRule:   q.ApprovalRule.replaceDB(db),
		ApprovalTask:   q.ApprovalTask.replaceDB(db),
		Batch:          q.Batch.replaceDB(db),
		Exp:            q.Exp.replaceDB(db),
		ExpLog:         q.ExpLog.replaceDB(db),
		ExpTask:        q.ExpTask.replaceDB(db),
		Group:          q.Group.replaceDB(db),
		Layer:          q.Layer.replaceDB(db),
		ParamsType:     q.ParamsType.replaceDB(db),
		Permission:     q.Permission.replaceDB(db),
		PermissionRole: q.PermissionRole.replaceDB(db),
		Project:        q.Project.replaceDB(db),
		Role:           q.Role.replaceDB(db),
		SplitGroup:     q.SplitGroup.replaceDB(db),
		StrategyTpl:    q.StrategyTpl.replaceDB(db),
		Tag:            q.Tag.replaceDB(db),
		User:           q.User.replaceDB(db),
		UserInfo:       q.UserInfo.replaceDB(db),
	}
}

type queryCtx struct {
	ApprovalRule   IApprovalRuleDo
	ApprovalTask   IApprovalTaskDo
	Batch          IBatchDo
	Exp            IExpDo
	ExpLog         IExpLogDo
	ExpTask        IExpTaskDo
	Group          IGroupDo
	Layer          ILayerDo
	ParamsType     IParamsTypeDo
	Permission     IPermissionDo
	PermissionRole IPermissionRoleDo
	Project        IProjectDo
	Role           IRoleDo
	SplitGroup     ISplitGroupDo
	StrategyTpl    IStrategyTplDo
	Tag            ITagDo
	User           IUserDo
	UserInfo       IUserInfoDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		ApprovalRule:   q.ApprovalRule.WithContext(ctx),
		ApprovalTask:   q.ApprovalTask.WithContext(ctx),
		Batch:          q.Batch.WithContext(ctx),
		Exp:            q.Exp.WithContext(ctx),
		ExpLog:         q.ExpLog.WithContext(ctx),
		ExpTask:        q.ExpTask.WithContext(ctx),
		Group:          q.Group.WithContext(ctx),
		Layer:          q.Layer.WithContext(ctx),
		ParamsType:     q.ParamsType.WithContext(ctx),
		Permission:     q.Permission.WithContext(ctx),
		PermissionRole: q.PermissionRole.WithContext(ctx),
		Project:        q.Project.WithContext(ctx),
		Role:           q.Role.WithContext(ctx),
		SplitGroup:     q.SplitGroup.WithContext(ctx),
		StrategyTpl:    q.StrategyTpl.WithContext(ctx),
		Tag:            q.Tag.WithContext(ctx),
		User:           q.User.WithContext(ctx),
		UserInfo:       q.UserInfo.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
