// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.7k7k.com/data/abAdmin/model"
)

func newApprovalRule(db *gorm.DB, opts ...gen.DOOption) approvalRule {
	_approvalRule := approvalRule{}

	_approvalRule.approvalRuleDo.UseDB(db, opts...)
	_approvalRule.approvalRuleDo.UseModel(&model.ApprovalRule{})

	tableName := _approvalRule.approvalRuleDo.TableName()
	_approvalRule.ALL = field.NewAsterisk(tableName)
	_approvalRule.ID = field.NewInt64(tableName, "id")
	_approvalRule.ProjectID = field.NewInt64(tableName, "project_id")
	_approvalRule.Name = field.NewString(tableName, "name")
	_approvalRule.RuleType = field.NewInt32(tableName, "rule_type")
	_approvalRule.Desc = field.NewString(tableName, "desc")
	_approvalRule.State = field.NewInt32(tableName, "state")
	_approvalRule.Stages = field.NewField(tableName, "stages")
	_approvalRule.Approvers = field.NewField(tableName, "approvers")
	_approvalRule.CreatorID = field.NewInt32(tableName, "creator_id")
	_approvalRule.SearchText = field.NewField(tableName, "search_text")
	_approvalRule.CreatedAt = field.NewTime(tableName, "created_at")
	_approvalRule.UpdatedAt = field.NewTime(tableName, "updated_at")

	_approvalRule.fillFieldMap()

	return _approvalRule
}

// approvalRule 审批规则表
type approvalRule struct {
	approvalRuleDo

	ALL        field.Asterisk
	ID         field.Int64  // 主键ID
	ProjectID  field.Int64  // 项目ID
	Name       field.String // 流程名称
	RuleType   field.Int32  // 审批类型：1：实验状态变更；2：实验信息变更；
	Desc       field.String // 流程描述
	State      field.Int32  // 状态 -1:下线 1: 开启 2: 关闭
	Stages     field.Field  // 审批节点
	Approvers  field.Field  // 审批人
	CreatorID  field.Int32  // 创建人ID
	SearchText field.Field  // 关键词查询索引
	CreatedAt  field.Time   // 创建时间
	UpdatedAt  field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (a approvalRule) Table(newTableName string) *approvalRule {
	a.approvalRuleDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a approvalRule) As(alias string) *approvalRule {
	a.approvalRuleDo.DO = *(a.approvalRuleDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *approvalRule) updateTableName(table string) *approvalRule {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt64(table, "id")
	a.ProjectID = field.NewInt64(table, "project_id")
	a.Name = field.NewString(table, "name")
	a.RuleType = field.NewInt32(table, "rule_type")
	a.Desc = field.NewString(table, "desc")
	a.State = field.NewInt32(table, "state")
	a.Stages = field.NewField(table, "stages")
	a.Approvers = field.NewField(table, "approvers")
	a.CreatorID = field.NewInt32(table, "creator_id")
	a.SearchText = field.NewField(table, "search_text")
	a.CreatedAt = field.NewTime(table, "created_at")
	a.UpdatedAt = field.NewTime(table, "updated_at")

	a.fillFieldMap()

	return a
}

func (a *approvalRule) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *approvalRule) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 12)
	a.fieldMap["id"] = a.ID
	a.fieldMap["project_id"] = a.ProjectID
	a.fieldMap["name"] = a.Name
	a.fieldMap["rule_type"] = a.RuleType
	a.fieldMap["desc"] = a.Desc
	a.fieldMap["state"] = a.State
	a.fieldMap["stages"] = a.Stages
	a.fieldMap["approvers"] = a.Approvers
	a.fieldMap["creator_id"] = a.CreatorID
	a.fieldMap["search_text"] = a.SearchText
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
}

func (a approvalRule) clone(db *gorm.DB) approvalRule {
	a.approvalRuleDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a approvalRule) replaceDB(db *gorm.DB) approvalRule {
	a.approvalRuleDo.ReplaceDB(db)
	return a
}

type approvalRuleDo struct{ gen.DO }

type IApprovalRuleDo interface {
	gen.SubQuery
	Debug() IApprovalRuleDo
	WithContext(ctx context.Context) IApprovalRuleDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IApprovalRuleDo
	WriteDB() IApprovalRuleDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IApprovalRuleDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IApprovalRuleDo
	Not(conds ...gen.Condition) IApprovalRuleDo
	Or(conds ...gen.Condition) IApprovalRuleDo
	Select(conds ...field.Expr) IApprovalRuleDo
	Where(conds ...gen.Condition) IApprovalRuleDo
	Order(conds ...field.Expr) IApprovalRuleDo
	Distinct(cols ...field.Expr) IApprovalRuleDo
	Omit(cols ...field.Expr) IApprovalRuleDo
	Join(table schema.Tabler, on ...field.Expr) IApprovalRuleDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IApprovalRuleDo
	RightJoin(table schema.Tabler, on ...field.Expr) IApprovalRuleDo
	Group(cols ...field.Expr) IApprovalRuleDo
	Having(conds ...gen.Condition) IApprovalRuleDo
	Limit(limit int) IApprovalRuleDo
	Offset(offset int) IApprovalRuleDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IApprovalRuleDo
	Unscoped() IApprovalRuleDo
	Create(values ...*model.ApprovalRule) error
	CreateInBatches(values []*model.ApprovalRule, batchSize int) error
	Save(values ...*model.ApprovalRule) error
	First() (*model.ApprovalRule, error)
	Take() (*model.ApprovalRule, error)
	Last() (*model.ApprovalRule, error)
	Find() ([]*model.ApprovalRule, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ApprovalRule, err error)
	FindInBatches(result *[]*model.ApprovalRule, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ApprovalRule) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IApprovalRuleDo
	Assign(attrs ...field.AssignExpr) IApprovalRuleDo
	Joins(fields ...field.RelationField) IApprovalRuleDo
	Preload(fields ...field.RelationField) IApprovalRuleDo
	FirstOrInit() (*model.ApprovalRule, error)
	FirstOrCreate() (*model.ApprovalRule, error)
	FindByPage(offset int, limit int) (result []*model.ApprovalRule, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IApprovalRuleDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a approvalRuleDo) Debug() IApprovalRuleDo {
	return a.withDO(a.DO.Debug())
}

func (a approvalRuleDo) WithContext(ctx context.Context) IApprovalRuleDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a approvalRuleDo) ReadDB() IApprovalRuleDo {
	return a.Clauses(dbresolver.Read)
}

func (a approvalRuleDo) WriteDB() IApprovalRuleDo {
	return a.Clauses(dbresolver.Write)
}

func (a approvalRuleDo) Session(config *gorm.Session) IApprovalRuleDo {
	return a.withDO(a.DO.Session(config))
}

func (a approvalRuleDo) Clauses(conds ...clause.Expression) IApprovalRuleDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a approvalRuleDo) Returning(value interface{}, columns ...string) IApprovalRuleDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a approvalRuleDo) Not(conds ...gen.Condition) IApprovalRuleDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a approvalRuleDo) Or(conds ...gen.Condition) IApprovalRuleDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a approvalRuleDo) Select(conds ...field.Expr) IApprovalRuleDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a approvalRuleDo) Where(conds ...gen.Condition) IApprovalRuleDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a approvalRuleDo) Order(conds ...field.Expr) IApprovalRuleDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a approvalRuleDo) Distinct(cols ...field.Expr) IApprovalRuleDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a approvalRuleDo) Omit(cols ...field.Expr) IApprovalRuleDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a approvalRuleDo) Join(table schema.Tabler, on ...field.Expr) IApprovalRuleDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a approvalRuleDo) LeftJoin(table schema.Tabler, on ...field.Expr) IApprovalRuleDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a approvalRuleDo) RightJoin(table schema.Tabler, on ...field.Expr) IApprovalRuleDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a approvalRuleDo) Group(cols ...field.Expr) IApprovalRuleDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a approvalRuleDo) Having(conds ...gen.Condition) IApprovalRuleDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a approvalRuleDo) Limit(limit int) IApprovalRuleDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a approvalRuleDo) Offset(offset int) IApprovalRuleDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a approvalRuleDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IApprovalRuleDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a approvalRuleDo) Unscoped() IApprovalRuleDo {
	return a.withDO(a.DO.Unscoped())
}

func (a approvalRuleDo) Create(values ...*model.ApprovalRule) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a approvalRuleDo) CreateInBatches(values []*model.ApprovalRule, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a approvalRuleDo) Save(values ...*model.ApprovalRule) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a approvalRuleDo) First() (*model.ApprovalRule, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ApprovalRule), nil
	}
}

func (a approvalRuleDo) Take() (*model.ApprovalRule, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ApprovalRule), nil
	}
}

func (a approvalRuleDo) Last() (*model.ApprovalRule, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ApprovalRule), nil
	}
}

func (a approvalRuleDo) Find() ([]*model.ApprovalRule, error) {
	result, err := a.DO.Find()
	return result.([]*model.ApprovalRule), err
}

func (a approvalRuleDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ApprovalRule, err error) {
	buf := make([]*model.ApprovalRule, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a approvalRuleDo) FindInBatches(result *[]*model.ApprovalRule, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a approvalRuleDo) Attrs(attrs ...field.AssignExpr) IApprovalRuleDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a approvalRuleDo) Assign(attrs ...field.AssignExpr) IApprovalRuleDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a approvalRuleDo) Joins(fields ...field.RelationField) IApprovalRuleDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a approvalRuleDo) Preload(fields ...field.RelationField) IApprovalRuleDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a approvalRuleDo) FirstOrInit() (*model.ApprovalRule, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ApprovalRule), nil
	}
}

func (a approvalRuleDo) FirstOrCreate() (*model.ApprovalRule, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ApprovalRule), nil
	}
}

func (a approvalRuleDo) FindByPage(offset int, limit int) (result []*model.ApprovalRule, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a approvalRuleDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a approvalRuleDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a approvalRuleDo) Delete(models ...*model.ApprovalRule) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *approvalRuleDo) withDO(do gen.Dao) *approvalRuleDo {
	a.DO = *do.(*gen.DO)
	return a
}
