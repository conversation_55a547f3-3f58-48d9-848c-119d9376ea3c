// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.7k7k.com/data/abAdmin/model"
)

func newExpTask(db *gorm.DB, opts ...gen.DOOption) expTask {
	_expTask := expTask{}

	_expTask.expTaskDo.UseDB(db, opts...)
	_expTask.expTaskDo.UseModel(&model.ExpTask{})

	tableName := _expTask.expTaskDo.TableName()
	_expTask.ALL = field.NewAsterisk(tableName)
	_expTask.ID = field.NewInt64(tableName, "id")
	_expTask.Name = field.NewString(tableName, "name")
	_expTask.ExpID = field.NewInt64(tableName, "exp_id")
	_expTask.ExpStatus = field.NewInt32(tableName, "exp_status")
	_expTask.PlannedExecutionTime = field.NewTime(tableName, "planned_execution_time")
	_expTask.ActualExecutionTime = field.NewTime(tableName, "actual_execution_time")
	_expTask.CreateTime = field.NewTime(tableName, "create_time")
	_expTask.UpdateTime = field.NewTime(tableName, "update_time")
	_expTask.Desc = field.NewString(tableName, "desc")
	_expTask.Status = field.NewInt32(tableName, "status")

	_expTask.fillFieldMap()

	return _expTask
}

// expTask 实验任务表
type expTask struct {
	expTaskDo

	ALL                  field.Asterisk
	ID                   field.Int64
	Name                 field.String // 任务名称
	ExpID                field.Int64  // 实验ID
	ExpStatus            field.Int32  // 实验状态
	PlannedExecutionTime field.Time   // 计划执行时间
	ActualExecutionTime  field.Time   // 实际执行时间
	CreateTime           field.Time   // 创建时间
	UpdateTime           field.Time   // 更新时间
	Desc                 field.String // 执行备注
	Status               field.Int32  // 任务状态 1未开始 2执行中 3取消 4延迟 5已完成 6失败

	fieldMap map[string]field.Expr
}

func (e expTask) Table(newTableName string) *expTask {
	e.expTaskDo.UseTable(newTableName)
	return e.updateTableName(newTableName)
}

func (e expTask) As(alias string) *expTask {
	e.expTaskDo.DO = *(e.expTaskDo.As(alias).(*gen.DO))
	return e.updateTableName(alias)
}

func (e *expTask) updateTableName(table string) *expTask {
	e.ALL = field.NewAsterisk(table)
	e.ID = field.NewInt64(table, "id")
	e.Name = field.NewString(table, "name")
	e.ExpID = field.NewInt64(table, "exp_id")
	e.ExpStatus = field.NewInt32(table, "exp_status")
	e.PlannedExecutionTime = field.NewTime(table, "planned_execution_time")
	e.ActualExecutionTime = field.NewTime(table, "actual_execution_time")
	e.CreateTime = field.NewTime(table, "create_time")
	e.UpdateTime = field.NewTime(table, "update_time")
	e.Desc = field.NewString(table, "desc")
	e.Status = field.NewInt32(table, "status")

	e.fillFieldMap()

	return e
}

func (e *expTask) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := e.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (e *expTask) fillFieldMap() {
	e.fieldMap = make(map[string]field.Expr, 10)
	e.fieldMap["id"] = e.ID
	e.fieldMap["name"] = e.Name
	e.fieldMap["exp_id"] = e.ExpID
	e.fieldMap["exp_status"] = e.ExpStatus
	e.fieldMap["planned_execution_time"] = e.PlannedExecutionTime
	e.fieldMap["actual_execution_time"] = e.ActualExecutionTime
	e.fieldMap["create_time"] = e.CreateTime
	e.fieldMap["update_time"] = e.UpdateTime
	e.fieldMap["desc"] = e.Desc
	e.fieldMap["status"] = e.Status
}

func (e expTask) clone(db *gorm.DB) expTask {
	e.expTaskDo.ReplaceConnPool(db.Statement.ConnPool)
	return e
}

func (e expTask) replaceDB(db *gorm.DB) expTask {
	e.expTaskDo.ReplaceDB(db)
	return e
}

type expTaskDo struct{ gen.DO }

type IExpTaskDo interface {
	gen.SubQuery
	Debug() IExpTaskDo
	WithContext(ctx context.Context) IExpTaskDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IExpTaskDo
	WriteDB() IExpTaskDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IExpTaskDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IExpTaskDo
	Not(conds ...gen.Condition) IExpTaskDo
	Or(conds ...gen.Condition) IExpTaskDo
	Select(conds ...field.Expr) IExpTaskDo
	Where(conds ...gen.Condition) IExpTaskDo
	Order(conds ...field.Expr) IExpTaskDo
	Distinct(cols ...field.Expr) IExpTaskDo
	Omit(cols ...field.Expr) IExpTaskDo
	Join(table schema.Tabler, on ...field.Expr) IExpTaskDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IExpTaskDo
	RightJoin(table schema.Tabler, on ...field.Expr) IExpTaskDo
	Group(cols ...field.Expr) IExpTaskDo
	Having(conds ...gen.Condition) IExpTaskDo
	Limit(limit int) IExpTaskDo
	Offset(offset int) IExpTaskDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IExpTaskDo
	Unscoped() IExpTaskDo
	Create(values ...*model.ExpTask) error
	CreateInBatches(values []*model.ExpTask, batchSize int) error
	Save(values ...*model.ExpTask) error
	First() (*model.ExpTask, error)
	Take() (*model.ExpTask, error)
	Last() (*model.ExpTask, error)
	Find() ([]*model.ExpTask, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ExpTask, err error)
	FindInBatches(result *[]*model.ExpTask, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ExpTask) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IExpTaskDo
	Assign(attrs ...field.AssignExpr) IExpTaskDo
	Joins(fields ...field.RelationField) IExpTaskDo
	Preload(fields ...field.RelationField) IExpTaskDo
	FirstOrInit() (*model.ExpTask, error)
	FirstOrCreate() (*model.ExpTask, error)
	FindByPage(offset int, limit int) (result []*model.ExpTask, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IExpTaskDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (e expTaskDo) Debug() IExpTaskDo {
	return e.withDO(e.DO.Debug())
}

func (e expTaskDo) WithContext(ctx context.Context) IExpTaskDo {
	return e.withDO(e.DO.WithContext(ctx))
}

func (e expTaskDo) ReadDB() IExpTaskDo {
	return e.Clauses(dbresolver.Read)
}

func (e expTaskDo) WriteDB() IExpTaskDo {
	return e.Clauses(dbresolver.Write)
}

func (e expTaskDo) Session(config *gorm.Session) IExpTaskDo {
	return e.withDO(e.DO.Session(config))
}

func (e expTaskDo) Clauses(conds ...clause.Expression) IExpTaskDo {
	return e.withDO(e.DO.Clauses(conds...))
}

func (e expTaskDo) Returning(value interface{}, columns ...string) IExpTaskDo {
	return e.withDO(e.DO.Returning(value, columns...))
}

func (e expTaskDo) Not(conds ...gen.Condition) IExpTaskDo {
	return e.withDO(e.DO.Not(conds...))
}

func (e expTaskDo) Or(conds ...gen.Condition) IExpTaskDo {
	return e.withDO(e.DO.Or(conds...))
}

func (e expTaskDo) Select(conds ...field.Expr) IExpTaskDo {
	return e.withDO(e.DO.Select(conds...))
}

func (e expTaskDo) Where(conds ...gen.Condition) IExpTaskDo {
	return e.withDO(e.DO.Where(conds...))
}

func (e expTaskDo) Order(conds ...field.Expr) IExpTaskDo {
	return e.withDO(e.DO.Order(conds...))
}

func (e expTaskDo) Distinct(cols ...field.Expr) IExpTaskDo {
	return e.withDO(e.DO.Distinct(cols...))
}

func (e expTaskDo) Omit(cols ...field.Expr) IExpTaskDo {
	return e.withDO(e.DO.Omit(cols...))
}

func (e expTaskDo) Join(table schema.Tabler, on ...field.Expr) IExpTaskDo {
	return e.withDO(e.DO.Join(table, on...))
}

func (e expTaskDo) LeftJoin(table schema.Tabler, on ...field.Expr) IExpTaskDo {
	return e.withDO(e.DO.LeftJoin(table, on...))
}

func (e expTaskDo) RightJoin(table schema.Tabler, on ...field.Expr) IExpTaskDo {
	return e.withDO(e.DO.RightJoin(table, on...))
}

func (e expTaskDo) Group(cols ...field.Expr) IExpTaskDo {
	return e.withDO(e.DO.Group(cols...))
}

func (e expTaskDo) Having(conds ...gen.Condition) IExpTaskDo {
	return e.withDO(e.DO.Having(conds...))
}

func (e expTaskDo) Limit(limit int) IExpTaskDo {
	return e.withDO(e.DO.Limit(limit))
}

func (e expTaskDo) Offset(offset int) IExpTaskDo {
	return e.withDO(e.DO.Offset(offset))
}

func (e expTaskDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IExpTaskDo {
	return e.withDO(e.DO.Scopes(funcs...))
}

func (e expTaskDo) Unscoped() IExpTaskDo {
	return e.withDO(e.DO.Unscoped())
}

func (e expTaskDo) Create(values ...*model.ExpTask) error {
	if len(values) == 0 {
		return nil
	}
	return e.DO.Create(values)
}

func (e expTaskDo) CreateInBatches(values []*model.ExpTask, batchSize int) error {
	return e.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (e expTaskDo) Save(values ...*model.ExpTask) error {
	if len(values) == 0 {
		return nil
	}
	return e.DO.Save(values)
}

func (e expTaskDo) First() (*model.ExpTask, error) {
	if result, err := e.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ExpTask), nil
	}
}

func (e expTaskDo) Take() (*model.ExpTask, error) {
	if result, err := e.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ExpTask), nil
	}
}

func (e expTaskDo) Last() (*model.ExpTask, error) {
	if result, err := e.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ExpTask), nil
	}
}

func (e expTaskDo) Find() ([]*model.ExpTask, error) {
	result, err := e.DO.Find()
	return result.([]*model.ExpTask), err
}

func (e expTaskDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ExpTask, err error) {
	buf := make([]*model.ExpTask, 0, batchSize)
	err = e.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (e expTaskDo) FindInBatches(result *[]*model.ExpTask, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return e.DO.FindInBatches(result, batchSize, fc)
}

func (e expTaskDo) Attrs(attrs ...field.AssignExpr) IExpTaskDo {
	return e.withDO(e.DO.Attrs(attrs...))
}

func (e expTaskDo) Assign(attrs ...field.AssignExpr) IExpTaskDo {
	return e.withDO(e.DO.Assign(attrs...))
}

func (e expTaskDo) Joins(fields ...field.RelationField) IExpTaskDo {
	for _, _f := range fields {
		e = *e.withDO(e.DO.Joins(_f))
	}
	return &e
}

func (e expTaskDo) Preload(fields ...field.RelationField) IExpTaskDo {
	for _, _f := range fields {
		e = *e.withDO(e.DO.Preload(_f))
	}
	return &e
}

func (e expTaskDo) FirstOrInit() (*model.ExpTask, error) {
	if result, err := e.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ExpTask), nil
	}
}

func (e expTaskDo) FirstOrCreate() (*model.ExpTask, error) {
	if result, err := e.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ExpTask), nil
	}
}

func (e expTaskDo) FindByPage(offset int, limit int) (result []*model.ExpTask, count int64, err error) {
	result, err = e.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = e.Offset(-1).Limit(-1).Count()
	return
}

func (e expTaskDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = e.Count()
	if err != nil {
		return
	}

	err = e.Offset(offset).Limit(limit).Scan(result)
	return
}

func (e expTaskDo) Scan(result interface{}) (err error) {
	return e.DO.Scan(result)
}

func (e expTaskDo) Delete(models ...*model.ExpTask) (result gen.ResultInfo, err error) {
	return e.DO.Delete(models)
}

func (e *expTaskDo) withDO(do gen.Dao) *expTaskDo {
	e.DO = *do.(*gen.DO)
	return e
}
