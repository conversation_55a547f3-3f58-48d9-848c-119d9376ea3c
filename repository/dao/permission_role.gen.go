// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.7k7k.com/data/abAdmin/model"
)

func newPermissionRole(db *gorm.DB, opts ...gen.DOOption) permissionRole {
	_permissionRole := permissionRole{}

	_permissionRole.permissionRoleDo.UseDB(db, opts...)
	_permissionRole.permissionRoleDo.UseModel(&model.PermissionRole{})

	tableName := _permissionRole.permissionRoleDo.TableName()
	_permissionRole.ALL = field.NewAsterisk(tableName)
	_permissionRole.PermissionID = field.NewInt32(tableName, "permission_id")
	_permissionRole.RoleID = field.NewInt32(tableName, "role_id")

	_permissionRole.fillFieldMap()

	return _permissionRole
}

type permissionRole struct {
	permissionRoleDo

	ALL          field.Asterisk
	PermissionID field.Int32
	RoleID       field.Int32

	fieldMap map[string]field.Expr
}

func (p permissionRole) Table(newTableName string) *permissionRole {
	p.permissionRoleDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p permissionRole) As(alias string) *permissionRole {
	p.permissionRoleDo.DO = *(p.permissionRoleDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *permissionRole) updateTableName(table string) *permissionRole {
	p.ALL = field.NewAsterisk(table)
	p.PermissionID = field.NewInt32(table, "permission_id")
	p.RoleID = field.NewInt32(table, "role_id")

	p.fillFieldMap()

	return p
}

func (p *permissionRole) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *permissionRole) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 2)
	p.fieldMap["permission_id"] = p.PermissionID
	p.fieldMap["role_id"] = p.RoleID
}

func (p permissionRole) clone(db *gorm.DB) permissionRole {
	p.permissionRoleDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p permissionRole) replaceDB(db *gorm.DB) permissionRole {
	p.permissionRoleDo.ReplaceDB(db)
	return p
}

type permissionRoleDo struct{ gen.DO }

type IPermissionRoleDo interface {
	gen.SubQuery
	Debug() IPermissionRoleDo
	WithContext(ctx context.Context) IPermissionRoleDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPermissionRoleDo
	WriteDB() IPermissionRoleDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPermissionRoleDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPermissionRoleDo
	Not(conds ...gen.Condition) IPermissionRoleDo
	Or(conds ...gen.Condition) IPermissionRoleDo
	Select(conds ...field.Expr) IPermissionRoleDo
	Where(conds ...gen.Condition) IPermissionRoleDo
	Order(conds ...field.Expr) IPermissionRoleDo
	Distinct(cols ...field.Expr) IPermissionRoleDo
	Omit(cols ...field.Expr) IPermissionRoleDo
	Join(table schema.Tabler, on ...field.Expr) IPermissionRoleDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPermissionRoleDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPermissionRoleDo
	Group(cols ...field.Expr) IPermissionRoleDo
	Having(conds ...gen.Condition) IPermissionRoleDo
	Limit(limit int) IPermissionRoleDo
	Offset(offset int) IPermissionRoleDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPermissionRoleDo
	Unscoped() IPermissionRoleDo
	Create(values ...*model.PermissionRole) error
	CreateInBatches(values []*model.PermissionRole, batchSize int) error
	Save(values ...*model.PermissionRole) error
	First() (*model.PermissionRole, error)
	Take() (*model.PermissionRole, error)
	Last() (*model.PermissionRole, error)
	Find() ([]*model.PermissionRole, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PermissionRole, err error)
	FindInBatches(result *[]*model.PermissionRole, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.PermissionRole) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPermissionRoleDo
	Assign(attrs ...field.AssignExpr) IPermissionRoleDo
	Joins(fields ...field.RelationField) IPermissionRoleDo
	Preload(fields ...field.RelationField) IPermissionRoleDo
	FirstOrInit() (*model.PermissionRole, error)
	FirstOrCreate() (*model.PermissionRole, error)
	FindByPage(offset int, limit int) (result []*model.PermissionRole, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPermissionRoleDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p permissionRoleDo) Debug() IPermissionRoleDo {
	return p.withDO(p.DO.Debug())
}

func (p permissionRoleDo) WithContext(ctx context.Context) IPermissionRoleDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p permissionRoleDo) ReadDB() IPermissionRoleDo {
	return p.Clauses(dbresolver.Read)
}

func (p permissionRoleDo) WriteDB() IPermissionRoleDo {
	return p.Clauses(dbresolver.Write)
}

func (p permissionRoleDo) Session(config *gorm.Session) IPermissionRoleDo {
	return p.withDO(p.DO.Session(config))
}

func (p permissionRoleDo) Clauses(conds ...clause.Expression) IPermissionRoleDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p permissionRoleDo) Returning(value interface{}, columns ...string) IPermissionRoleDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p permissionRoleDo) Not(conds ...gen.Condition) IPermissionRoleDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p permissionRoleDo) Or(conds ...gen.Condition) IPermissionRoleDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p permissionRoleDo) Select(conds ...field.Expr) IPermissionRoleDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p permissionRoleDo) Where(conds ...gen.Condition) IPermissionRoleDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p permissionRoleDo) Order(conds ...field.Expr) IPermissionRoleDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p permissionRoleDo) Distinct(cols ...field.Expr) IPermissionRoleDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p permissionRoleDo) Omit(cols ...field.Expr) IPermissionRoleDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p permissionRoleDo) Join(table schema.Tabler, on ...field.Expr) IPermissionRoleDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p permissionRoleDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPermissionRoleDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p permissionRoleDo) RightJoin(table schema.Tabler, on ...field.Expr) IPermissionRoleDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p permissionRoleDo) Group(cols ...field.Expr) IPermissionRoleDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p permissionRoleDo) Having(conds ...gen.Condition) IPermissionRoleDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p permissionRoleDo) Limit(limit int) IPermissionRoleDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p permissionRoleDo) Offset(offset int) IPermissionRoleDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p permissionRoleDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPermissionRoleDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p permissionRoleDo) Unscoped() IPermissionRoleDo {
	return p.withDO(p.DO.Unscoped())
}

func (p permissionRoleDo) Create(values ...*model.PermissionRole) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p permissionRoleDo) CreateInBatches(values []*model.PermissionRole, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p permissionRoleDo) Save(values ...*model.PermissionRole) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p permissionRoleDo) First() (*model.PermissionRole, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.PermissionRole), nil
	}
}

func (p permissionRoleDo) Take() (*model.PermissionRole, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.PermissionRole), nil
	}
}

func (p permissionRoleDo) Last() (*model.PermissionRole, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.PermissionRole), nil
	}
}

func (p permissionRoleDo) Find() ([]*model.PermissionRole, error) {
	result, err := p.DO.Find()
	return result.([]*model.PermissionRole), err
}

func (p permissionRoleDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PermissionRole, err error) {
	buf := make([]*model.PermissionRole, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p permissionRoleDo) FindInBatches(result *[]*model.PermissionRole, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p permissionRoleDo) Attrs(attrs ...field.AssignExpr) IPermissionRoleDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p permissionRoleDo) Assign(attrs ...field.AssignExpr) IPermissionRoleDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p permissionRoleDo) Joins(fields ...field.RelationField) IPermissionRoleDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p permissionRoleDo) Preload(fields ...field.RelationField) IPermissionRoleDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p permissionRoleDo) FirstOrInit() (*model.PermissionRole, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.PermissionRole), nil
	}
}

func (p permissionRoleDo) FirstOrCreate() (*model.PermissionRole, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.PermissionRole), nil
	}
}

func (p permissionRoleDo) FindByPage(offset int, limit int) (result []*model.PermissionRole, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p permissionRoleDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p permissionRoleDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p permissionRoleDo) Delete(models ...*model.PermissionRole) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *permissionRoleDo) withDO(do gen.Dao) *permissionRoleDo {
	p.DO = *do.(*gen.DO)
	return p
}
