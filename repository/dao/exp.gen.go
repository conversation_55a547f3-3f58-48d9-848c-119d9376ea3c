// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.7k7k.com/data/abAdmin/model"
)

func newExp(db *gorm.DB, opts ...gen.DOOption) exp {
	_exp := exp{}

	_exp.expDo.UseDB(db, opts...)
	_exp.expDo.UseModel(&model.Exp{})

	tableName := _exp.expDo.TableName()
	_exp.ALL = field.NewAsterisk(tableName)
	_exp.ID = field.NewInt64(tableName, "id")
	_exp.ProjectID = field.NewInt64(tableName, "project_id")
	_exp.LayerID = field.NewInt64(tableName, "layer_id")
	_exp.Name = field.NewString(tableName, "name")
	_exp.LayerRate = field.NewInt32(tableName, "layer_rate")
	_exp.ExpType = field.NewInt32(tableName, "exp_type")
	_exp.TagID = field.NewString(tableName, "tag_id")
	_exp.BatchID = field.NewInt64(tableName, "batch_id")
	_exp.Days = field.NewInt32(tableName, "days")
	_exp.Strategy = field.NewField(tableName, "strategy")
	_exp.Csr = field.NewField(tableName, "csr")
	_exp.UID = field.NewInt32(tableName, "uid")
	_exp.Owner = field.NewString(tableName, "owner")
	_exp.Desc = field.NewString(tableName, "desc")
	_exp.Step = field.NewInt32(tableName, "step")
	_exp.State = field.NewInt32(tableName, "state")
	_exp.IsDeleted = field.NewInt32(tableName, "is_deleted")
	_exp.StartTime = field.NewTime(tableName, "start_time")
	_exp.EndTime = field.NewTime(tableName, "end_time")
	_exp.Version = field.NewInt64(tableName, "version")
	_exp.GroupLimitHour = field.NewInt32(tableName, "group_limit_hour")
	_exp.Data = field.NewField(tableName, "data")
	_exp.CreateTime = field.NewTime(tableName, "create_time")
	_exp.UpdateTime = field.NewTime(tableName, "update_time")
	_exp.Groups = expHasManyGroups{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Groups", "model.Group"),
	}

	_exp.ApprovalTasks = expHasManyApprovalTasks{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("ApprovalTasks", "model.ApprovalTask"),
		ApprovalRule: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("ApprovalTasks.ApprovalRule", "model.ApprovalRule"),
		},
		ExpLog: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("ApprovalTasks.ExpLog", "model.ExpLog"),
		},
		Exp: struct {
			field.RelationField
			Layer struct {
				field.RelationField
			}
			Batch struct {
				field.RelationField
			}
			Groups struct {
				field.RelationField
			}
			ApprovalTasks struct {
				field.RelationField
				ApprovalRule struct {
					field.RelationField
				}
				ExpLog struct {
					field.RelationField
				}
				Exp struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("ApprovalTasks.Exp", "model.Exp"),
			Layer: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("ApprovalTasks.Exp.Layer", "model.Layer"),
			},
			Batch: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("ApprovalTasks.Exp.Batch", "model.Batch"),
			},
			Groups: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("ApprovalTasks.Exp.Groups", "model.Group"),
			},
			ApprovalTasks: struct {
				field.RelationField
				ApprovalRule struct {
					field.RelationField
				}
				ExpLog struct {
					field.RelationField
				}
				Exp struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("ApprovalTasks.Exp.ApprovalTasks", "model.ApprovalTask"),
				ApprovalRule: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("ApprovalTasks.Exp.ApprovalTasks.ApprovalRule", "model.ApprovalRule"),
				},
				ExpLog: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("ApprovalTasks.Exp.ApprovalTasks.ExpLog", "model.ExpLog"),
				},
				Exp: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("ApprovalTasks.Exp.ApprovalTasks.Exp", "model.Exp"),
				},
			},
		},
	}

	_exp.Layer = expBelongsToLayer{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Layer", "model.Layer"),
	}

	_exp.Batch = expBelongsToBatch{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Batch", "model.Batch"),
	}

	_exp.fillFieldMap()

	return _exp
}

// exp 实验表
type exp struct {
	expDo

	ALL            field.Asterisk
	ID             field.Int64
	ProjectID      field.Int64  // project关联id
	LayerID        field.Int64  // layer关联id
	Name           field.String // 实验名称
	LayerRate      field.Int32  // 分层流量占比
	ExpType        field.Int32  // 实验类型：1新增实验 2活跃实验
	TagID          field.String // ab_tag关联id,多个，逗号链接
	BatchID        field.Int64  // ab_batch关联id
	Days           field.Int32  // 实验天数
	Strategy       field.Field  // 实验策略
	Csr            field.Field  // csr
	UID            field.Int32  // 创建者id
	Owner          field.String // 所有者
	Desc           field.String // 描述
	Step           field.Int32  // 添加实验步骤，1：基本信息，2:设置策略，3:添加实验组（方案），4：设置实验指标
	State          field.Int32  // 实验状态：1:草稿 2:待开始 3:运行中 4:运行中不进量 5:已关闭 6:灰度运行中 7:灰度运行中不进量
	IsDeleted      field.Int32  // 状态：1禁用 2启用
	StartTime      field.Time   // 实验开始时间
	EndTime        field.Time   // 实验结束时间
	Version        field.Int64  // 实验版本
	GroupLimitHour field.Int32  // 单方案小时进量限制
	Data           field.Field
	CreateTime     field.Time // 创建时间
	UpdateTime     field.Time // 更新时间
	Groups         expHasManyGroups

	ApprovalTasks expHasManyApprovalTasks

	Layer expBelongsToLayer

	Batch expBelongsToBatch

	fieldMap map[string]field.Expr
}

func (e exp) Table(newTableName string) *exp {
	e.expDo.UseTable(newTableName)
	return e.updateTableName(newTableName)
}

func (e exp) As(alias string) *exp {
	e.expDo.DO = *(e.expDo.As(alias).(*gen.DO))
	return e.updateTableName(alias)
}

func (e *exp) updateTableName(table string) *exp {
	e.ALL = field.NewAsterisk(table)
	e.ID = field.NewInt64(table, "id")
	e.ProjectID = field.NewInt64(table, "project_id")
	e.LayerID = field.NewInt64(table, "layer_id")
	e.Name = field.NewString(table, "name")
	e.LayerRate = field.NewInt32(table, "layer_rate")
	e.ExpType = field.NewInt32(table, "exp_type")
	e.TagID = field.NewString(table, "tag_id")
	e.BatchID = field.NewInt64(table, "batch_id")
	e.Days = field.NewInt32(table, "days")
	e.Strategy = field.NewField(table, "strategy")
	e.Csr = field.NewField(table, "csr")
	e.UID = field.NewInt32(table, "uid")
	e.Owner = field.NewString(table, "owner")
	e.Desc = field.NewString(table, "desc")
	e.Step = field.NewInt32(table, "step")
	e.State = field.NewInt32(table, "state")
	e.IsDeleted = field.NewInt32(table, "is_deleted")
	e.StartTime = field.NewTime(table, "start_time")
	e.EndTime = field.NewTime(table, "end_time")
	e.Version = field.NewInt64(table, "version")
	e.GroupLimitHour = field.NewInt32(table, "group_limit_hour")
	e.Data = field.NewField(table, "data")
	e.CreateTime = field.NewTime(table, "create_time")
	e.UpdateTime = field.NewTime(table, "update_time")

	e.fillFieldMap()

	return e
}

func (e *exp) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := e.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (e *exp) fillFieldMap() {
	e.fieldMap = make(map[string]field.Expr, 28)
	e.fieldMap["id"] = e.ID
	e.fieldMap["project_id"] = e.ProjectID
	e.fieldMap["layer_id"] = e.LayerID
	e.fieldMap["name"] = e.Name
	e.fieldMap["layer_rate"] = e.LayerRate
	e.fieldMap["exp_type"] = e.ExpType
	e.fieldMap["tag_id"] = e.TagID
	e.fieldMap["batch_id"] = e.BatchID
	e.fieldMap["days"] = e.Days
	e.fieldMap["strategy"] = e.Strategy
	e.fieldMap["csr"] = e.Csr
	e.fieldMap["uid"] = e.UID
	e.fieldMap["owner"] = e.Owner
	e.fieldMap["desc"] = e.Desc
	e.fieldMap["step"] = e.Step
	e.fieldMap["state"] = e.State
	e.fieldMap["is_deleted"] = e.IsDeleted
	e.fieldMap["start_time"] = e.StartTime
	e.fieldMap["end_time"] = e.EndTime
	e.fieldMap["version"] = e.Version
	e.fieldMap["group_limit_hour"] = e.GroupLimitHour
	e.fieldMap["data"] = e.Data
	e.fieldMap["create_time"] = e.CreateTime
	e.fieldMap["update_time"] = e.UpdateTime

}

func (e exp) clone(db *gorm.DB) exp {
	e.expDo.ReplaceConnPool(db.Statement.ConnPool)
	return e
}

func (e exp) replaceDB(db *gorm.DB) exp {
	e.expDo.ReplaceDB(db)
	return e
}

type expHasManyGroups struct {
	db *gorm.DB

	field.RelationField
}

func (a expHasManyGroups) Where(conds ...field.Expr) *expHasManyGroups {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a expHasManyGroups) WithContext(ctx context.Context) *expHasManyGroups {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a expHasManyGroups) Session(session *gorm.Session) *expHasManyGroups {
	a.db = a.db.Session(session)
	return &a
}

func (a expHasManyGroups) Model(m *model.Exp) *expHasManyGroupsTx {
	return &expHasManyGroupsTx{a.db.Model(m).Association(a.Name())}
}

type expHasManyGroupsTx struct{ tx *gorm.Association }

func (a expHasManyGroupsTx) Find() (result []*model.Group, err error) {
	return result, a.tx.Find(&result)
}

func (a expHasManyGroupsTx) Append(values ...*model.Group) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a expHasManyGroupsTx) Replace(values ...*model.Group) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a expHasManyGroupsTx) Delete(values ...*model.Group) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a expHasManyGroupsTx) Clear() error {
	return a.tx.Clear()
}

func (a expHasManyGroupsTx) Count() int64 {
	return a.tx.Count()
}

type expHasManyApprovalTasks struct {
	db *gorm.DB

	field.RelationField

	ApprovalRule struct {
		field.RelationField
	}
	ExpLog struct {
		field.RelationField
	}
	Exp struct {
		field.RelationField
		Layer struct {
			field.RelationField
		}
		Batch struct {
			field.RelationField
		}
		Groups struct {
			field.RelationField
		}
		ApprovalTasks struct {
			field.RelationField
			ApprovalRule struct {
				field.RelationField
			}
			ExpLog struct {
				field.RelationField
			}
			Exp struct {
				field.RelationField
			}
		}
	}
}

func (a expHasManyApprovalTasks) Where(conds ...field.Expr) *expHasManyApprovalTasks {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a expHasManyApprovalTasks) WithContext(ctx context.Context) *expHasManyApprovalTasks {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a expHasManyApprovalTasks) Session(session *gorm.Session) *expHasManyApprovalTasks {
	a.db = a.db.Session(session)
	return &a
}

func (a expHasManyApprovalTasks) Model(m *model.Exp) *expHasManyApprovalTasksTx {
	return &expHasManyApprovalTasksTx{a.db.Model(m).Association(a.Name())}
}

type expHasManyApprovalTasksTx struct{ tx *gorm.Association }

func (a expHasManyApprovalTasksTx) Find() (result []*model.ApprovalTask, err error) {
	return result, a.tx.Find(&result)
}

func (a expHasManyApprovalTasksTx) Append(values ...*model.ApprovalTask) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a expHasManyApprovalTasksTx) Replace(values ...*model.ApprovalTask) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a expHasManyApprovalTasksTx) Delete(values ...*model.ApprovalTask) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a expHasManyApprovalTasksTx) Clear() error {
	return a.tx.Clear()
}

func (a expHasManyApprovalTasksTx) Count() int64 {
	return a.tx.Count()
}

type expBelongsToLayer struct {
	db *gorm.DB

	field.RelationField
}

func (a expBelongsToLayer) Where(conds ...field.Expr) *expBelongsToLayer {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a expBelongsToLayer) WithContext(ctx context.Context) *expBelongsToLayer {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a expBelongsToLayer) Session(session *gorm.Session) *expBelongsToLayer {
	a.db = a.db.Session(session)
	return &a
}

func (a expBelongsToLayer) Model(m *model.Exp) *expBelongsToLayerTx {
	return &expBelongsToLayerTx{a.db.Model(m).Association(a.Name())}
}

type expBelongsToLayerTx struct{ tx *gorm.Association }

func (a expBelongsToLayerTx) Find() (result *model.Layer, err error) {
	return result, a.tx.Find(&result)
}

func (a expBelongsToLayerTx) Append(values ...*model.Layer) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a expBelongsToLayerTx) Replace(values ...*model.Layer) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a expBelongsToLayerTx) Delete(values ...*model.Layer) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a expBelongsToLayerTx) Clear() error {
	return a.tx.Clear()
}

func (a expBelongsToLayerTx) Count() int64 {
	return a.tx.Count()
}

type expBelongsToBatch struct {
	db *gorm.DB

	field.RelationField
}

func (a expBelongsToBatch) Where(conds ...field.Expr) *expBelongsToBatch {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a expBelongsToBatch) WithContext(ctx context.Context) *expBelongsToBatch {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a expBelongsToBatch) Session(session *gorm.Session) *expBelongsToBatch {
	a.db = a.db.Session(session)
	return &a
}

func (a expBelongsToBatch) Model(m *model.Exp) *expBelongsToBatchTx {
	return &expBelongsToBatchTx{a.db.Model(m).Association(a.Name())}
}

type expBelongsToBatchTx struct{ tx *gorm.Association }

func (a expBelongsToBatchTx) Find() (result *model.Batch, err error) {
	return result, a.tx.Find(&result)
}

func (a expBelongsToBatchTx) Append(values ...*model.Batch) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a expBelongsToBatchTx) Replace(values ...*model.Batch) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a expBelongsToBatchTx) Delete(values ...*model.Batch) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a expBelongsToBatchTx) Clear() error {
	return a.tx.Clear()
}

func (a expBelongsToBatchTx) Count() int64 {
	return a.tx.Count()
}

type expDo struct{ gen.DO }

type IExpDo interface {
	gen.SubQuery
	Debug() IExpDo
	WithContext(ctx context.Context) IExpDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IExpDo
	WriteDB() IExpDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IExpDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IExpDo
	Not(conds ...gen.Condition) IExpDo
	Or(conds ...gen.Condition) IExpDo
	Select(conds ...field.Expr) IExpDo
	Where(conds ...gen.Condition) IExpDo
	Order(conds ...field.Expr) IExpDo
	Distinct(cols ...field.Expr) IExpDo
	Omit(cols ...field.Expr) IExpDo
	Join(table schema.Tabler, on ...field.Expr) IExpDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IExpDo
	RightJoin(table schema.Tabler, on ...field.Expr) IExpDo
	Group(cols ...field.Expr) IExpDo
	Having(conds ...gen.Condition) IExpDo
	Limit(limit int) IExpDo
	Offset(offset int) IExpDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IExpDo
	Unscoped() IExpDo
	Create(values ...*model.Exp) error
	CreateInBatches(values []*model.Exp, batchSize int) error
	Save(values ...*model.Exp) error
	First() (*model.Exp, error)
	Take() (*model.Exp, error)
	Last() (*model.Exp, error)
	Find() ([]*model.Exp, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Exp, err error)
	FindInBatches(result *[]*model.Exp, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.Exp) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IExpDo
	Assign(attrs ...field.AssignExpr) IExpDo
	Joins(fields ...field.RelationField) IExpDo
	Preload(fields ...field.RelationField) IExpDo
	FirstOrInit() (*model.Exp, error)
	FirstOrCreate() (*model.Exp, error)
	FindByPage(offset int, limit int) (result []*model.Exp, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IExpDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (e expDo) Debug() IExpDo {
	return e.withDO(e.DO.Debug())
}

func (e expDo) WithContext(ctx context.Context) IExpDo {
	return e.withDO(e.DO.WithContext(ctx))
}

func (e expDo) ReadDB() IExpDo {
	return e.Clauses(dbresolver.Read)
}

func (e expDo) WriteDB() IExpDo {
	return e.Clauses(dbresolver.Write)
}

func (e expDo) Session(config *gorm.Session) IExpDo {
	return e.withDO(e.DO.Session(config))
}

func (e expDo) Clauses(conds ...clause.Expression) IExpDo {
	return e.withDO(e.DO.Clauses(conds...))
}

func (e expDo) Returning(value interface{}, columns ...string) IExpDo {
	return e.withDO(e.DO.Returning(value, columns...))
}

func (e expDo) Not(conds ...gen.Condition) IExpDo {
	return e.withDO(e.DO.Not(conds...))
}

func (e expDo) Or(conds ...gen.Condition) IExpDo {
	return e.withDO(e.DO.Or(conds...))
}

func (e expDo) Select(conds ...field.Expr) IExpDo {
	return e.withDO(e.DO.Select(conds...))
}

func (e expDo) Where(conds ...gen.Condition) IExpDo {
	return e.withDO(e.DO.Where(conds...))
}

func (e expDo) Order(conds ...field.Expr) IExpDo {
	return e.withDO(e.DO.Order(conds...))
}

func (e expDo) Distinct(cols ...field.Expr) IExpDo {
	return e.withDO(e.DO.Distinct(cols...))
}

func (e expDo) Omit(cols ...field.Expr) IExpDo {
	return e.withDO(e.DO.Omit(cols...))
}

func (e expDo) Join(table schema.Tabler, on ...field.Expr) IExpDo {
	return e.withDO(e.DO.Join(table, on...))
}

func (e expDo) LeftJoin(table schema.Tabler, on ...field.Expr) IExpDo {
	return e.withDO(e.DO.LeftJoin(table, on...))
}

func (e expDo) RightJoin(table schema.Tabler, on ...field.Expr) IExpDo {
	return e.withDO(e.DO.RightJoin(table, on...))
}

func (e expDo) Group(cols ...field.Expr) IExpDo {
	return e.withDO(e.DO.Group(cols...))
}

func (e expDo) Having(conds ...gen.Condition) IExpDo {
	return e.withDO(e.DO.Having(conds...))
}

func (e expDo) Limit(limit int) IExpDo {
	return e.withDO(e.DO.Limit(limit))
}

func (e expDo) Offset(offset int) IExpDo {
	return e.withDO(e.DO.Offset(offset))
}

func (e expDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IExpDo {
	return e.withDO(e.DO.Scopes(funcs...))
}

func (e expDo) Unscoped() IExpDo {
	return e.withDO(e.DO.Unscoped())
}

func (e expDo) Create(values ...*model.Exp) error {
	if len(values) == 0 {
		return nil
	}
	return e.DO.Create(values)
}

func (e expDo) CreateInBatches(values []*model.Exp, batchSize int) error {
	return e.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (e expDo) Save(values ...*model.Exp) error {
	if len(values) == 0 {
		return nil
	}
	return e.DO.Save(values)
}

func (e expDo) First() (*model.Exp, error) {
	if result, err := e.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Exp), nil
	}
}

func (e expDo) Take() (*model.Exp, error) {
	if result, err := e.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Exp), nil
	}
}

func (e expDo) Last() (*model.Exp, error) {
	if result, err := e.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Exp), nil
	}
}

func (e expDo) Find() ([]*model.Exp, error) {
	result, err := e.DO.Find()
	return result.([]*model.Exp), err
}

func (e expDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Exp, err error) {
	buf := make([]*model.Exp, 0, batchSize)
	err = e.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (e expDo) FindInBatches(result *[]*model.Exp, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return e.DO.FindInBatches(result, batchSize, fc)
}

func (e expDo) Attrs(attrs ...field.AssignExpr) IExpDo {
	return e.withDO(e.DO.Attrs(attrs...))
}

func (e expDo) Assign(attrs ...field.AssignExpr) IExpDo {
	return e.withDO(e.DO.Assign(attrs...))
}

func (e expDo) Joins(fields ...field.RelationField) IExpDo {
	for _, _f := range fields {
		e = *e.withDO(e.DO.Joins(_f))
	}
	return &e
}

func (e expDo) Preload(fields ...field.RelationField) IExpDo {
	for _, _f := range fields {
		e = *e.withDO(e.DO.Preload(_f))
	}
	return &e
}

func (e expDo) FirstOrInit() (*model.Exp, error) {
	if result, err := e.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Exp), nil
	}
}

func (e expDo) FirstOrCreate() (*model.Exp, error) {
	if result, err := e.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Exp), nil
	}
}

func (e expDo) FindByPage(offset int, limit int) (result []*model.Exp, count int64, err error) {
	result, err = e.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = e.Offset(-1).Limit(-1).Count()
	return
}

func (e expDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = e.Count()
	if err != nil {
		return
	}

	err = e.Offset(offset).Limit(limit).Scan(result)
	return
}

func (e expDo) Scan(result interface{}) (err error) {
	return e.DO.Scan(result)
}

func (e expDo) Delete(models ...*model.Exp) (result gen.ResultInfo, err error) {
	return e.DO.Delete(models)
}

func (e *expDo) withDO(do gen.Dao) *expDo {
	e.DO = *do.(*gen.DO)
	return e
}
