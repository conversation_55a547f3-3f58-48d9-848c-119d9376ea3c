// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.7k7k.com/data/abAdmin/model"
)

func newApprovalTask(db *gorm.DB, opts ...gen.DOOption) approvalTask {
	_approvalTask := approvalTask{}

	_approvalTask.approvalTaskDo.UseDB(db, opts...)
	_approvalTask.approvalTaskDo.UseModel(&model.ApprovalTask{})

	tableName := _approvalTask.approvalTaskDo.TableName()
	_approvalTask.ALL = field.NewAsterisk(tableName)
	_approvalTask.ID = field.NewInt64(tableName, "id")
	_approvalTask.ProjectID = field.NewInt64(tableName, "project_id")
	_approvalTask.RuleID = field.NewInt64(tableName, "rule_id")
	_approvalTask.ExpID = field.NewInt64(tableName, "exp_id")
	_approvalTask.BeforeSnapshotID = field.NewInt64(tableName, "before_snapshot_id")
	_approvalTask.AfterSnapshotID = field.NewInt64(tableName, "after_snapshot_id")
	_approvalTask.Desc = field.NewString(tableName, "desc")
	_approvalTask.CreatorID = field.NewInt32(tableName, "creator_id")
	_approvalTask.State = field.NewInt32(tableName, "state")
	_approvalTask.IsEffected = field.NewInt32(tableName, "is_effected")
	_approvalTask.Step = field.NewInt32(tableName, "step")
	_approvalTask.Approvers = field.NewField(tableName, "approvers")
	_approvalTask.FlowApprovers = field.NewField(tableName, "flow_approvers")
	_approvalTask.SearchText = field.NewField(tableName, "search_text")
	_approvalTask.Log = field.NewField(tableName, "log")
	_approvalTask.CreatedAt = field.NewTime(tableName, "created_at")
	_approvalTask.UpdatedAt = field.NewTime(tableName, "updated_at")
	_approvalTask.Changes = field.NewField(tableName, "changes")
	_approvalTask.ApprovalRule = approvalTaskBelongsToApprovalRule{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("ApprovalRule", "model.ApprovalRule"),
	}

	_approvalTask.ExpLog = approvalTaskBelongsToExpLog{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("ExpLog", "model.ExpLog"),
	}

	_approvalTask.Exp = approvalTaskBelongsToExp{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Exp", "model.Exp"),
		Groups: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Exp.Groups", "model.Group"),
		},
		ApprovalTasks: struct {
			field.RelationField
			ApprovalRule struct {
				field.RelationField
			}
			ExpLog struct {
				field.RelationField
			}
			Exp struct {
				field.RelationField
				Layer struct {
					field.RelationField
				}
				Batch struct {
					field.RelationField
				}
				Groups struct {
					field.RelationField
				}
				ApprovalTasks struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Exp.ApprovalTasks", "model.ApprovalTask"),
			ApprovalRule: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Exp.ApprovalTasks.ApprovalRule", "model.ApprovalRule"),
			},
			ExpLog: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Exp.ApprovalTasks.ExpLog", "model.ExpLog"),
			},
			Exp: struct {
				field.RelationField
				Layer struct {
					field.RelationField
				}
				Batch struct {
					field.RelationField
				}
				Groups struct {
					field.RelationField
				}
				ApprovalTasks struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Exp.ApprovalTasks.Exp", "model.Exp"),
				Layer: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Exp.ApprovalTasks.Exp.Layer", "model.Layer"),
				},
				Batch: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Exp.ApprovalTasks.Exp.Batch", "model.Batch"),
				},
				Groups: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Exp.ApprovalTasks.Exp.Groups", "model.Group"),
				},
				ApprovalTasks: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Exp.ApprovalTasks.Exp.ApprovalTasks", "model.ApprovalTask"),
				},
			},
		},
		Layer: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Exp.Layer", "model.Layer"),
		},
		Batch: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Exp.Batch", "model.Batch"),
		},
	}

	_approvalTask.fillFieldMap()

	return _approvalTask
}

// approvalTask 审批任务表
type approvalTask struct {
	approvalTaskDo

	ALL              field.Asterisk
	ID               field.Int64  // 主键ID
	ProjectID        field.Int64  // 项目ID
	RuleID           field.Int64  // 审批规则ID
	ExpID            field.Int64  // 实验ID
	BeforeSnapshotID field.Int64  // 变更前快照ID
	AfterSnapshotID  field.Int64  // 变更后快照ID
	Desc             field.String // 任务描述
	CreatorID        field.Int32  // 创建人ID
	State            field.Int32  // 状态 1: 审批中 2: 审批完成 3: 关闭
	IsEffected       field.Int32  // 0：无效，1：有效
	Step             field.Int32  // 当前审批步骤
	Approvers        field.Field  // 当前步骤审批人
	FlowApprovers    field.Field  // 历史审批人
	SearchText       field.Field  // 关键词查询索引
	Log              field.Field  // 审批日志
	CreatedAt        field.Time   // 创建时间
	UpdatedAt        field.Time   // 更新时间
	Changes          field.Field  // 相关改变
	ApprovalRule     approvalTaskBelongsToApprovalRule

	ExpLog approvalTaskBelongsToExpLog

	Exp approvalTaskBelongsToExp

	fieldMap map[string]field.Expr
}

func (a approvalTask) Table(newTableName string) *approvalTask {
	a.approvalTaskDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a approvalTask) As(alias string) *approvalTask {
	a.approvalTaskDo.DO = *(a.approvalTaskDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *approvalTask) updateTableName(table string) *approvalTask {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt64(table, "id")
	a.ProjectID = field.NewInt64(table, "project_id")
	a.RuleID = field.NewInt64(table, "rule_id")
	a.ExpID = field.NewInt64(table, "exp_id")
	a.BeforeSnapshotID = field.NewInt64(table, "before_snapshot_id")
	a.AfterSnapshotID = field.NewInt64(table, "after_snapshot_id")
	a.Desc = field.NewString(table, "desc")
	a.CreatorID = field.NewInt32(table, "creator_id")
	a.State = field.NewInt32(table, "state")
	a.IsEffected = field.NewInt32(table, "is_effected")
	a.Step = field.NewInt32(table, "step")
	a.Approvers = field.NewField(table, "approvers")
	a.FlowApprovers = field.NewField(table, "flow_approvers")
	a.SearchText = field.NewField(table, "search_text")
	a.Log = field.NewField(table, "log")
	a.CreatedAt = field.NewTime(table, "created_at")
	a.UpdatedAt = field.NewTime(table, "updated_at")
	a.Changes = field.NewField(table, "changes")

	a.fillFieldMap()

	return a
}

func (a *approvalTask) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *approvalTask) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 21)
	a.fieldMap["id"] = a.ID
	a.fieldMap["project_id"] = a.ProjectID
	a.fieldMap["rule_id"] = a.RuleID
	a.fieldMap["exp_id"] = a.ExpID
	a.fieldMap["before_snapshot_id"] = a.BeforeSnapshotID
	a.fieldMap["after_snapshot_id"] = a.AfterSnapshotID
	a.fieldMap["desc"] = a.Desc
	a.fieldMap["creator_id"] = a.CreatorID
	a.fieldMap["state"] = a.State
	a.fieldMap["is_effected"] = a.IsEffected
	a.fieldMap["step"] = a.Step
	a.fieldMap["approvers"] = a.Approvers
	a.fieldMap["flow_approvers"] = a.FlowApprovers
	a.fieldMap["search_text"] = a.SearchText
	a.fieldMap["log"] = a.Log
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["changes"] = a.Changes

}

func (a approvalTask) clone(db *gorm.DB) approvalTask {
	a.approvalTaskDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a approvalTask) replaceDB(db *gorm.DB) approvalTask {
	a.approvalTaskDo.ReplaceDB(db)
	return a
}

type approvalTaskBelongsToApprovalRule struct {
	db *gorm.DB

	field.RelationField
}

func (a approvalTaskBelongsToApprovalRule) Where(conds ...field.Expr) *approvalTaskBelongsToApprovalRule {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a approvalTaskBelongsToApprovalRule) WithContext(ctx context.Context) *approvalTaskBelongsToApprovalRule {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a approvalTaskBelongsToApprovalRule) Session(session *gorm.Session) *approvalTaskBelongsToApprovalRule {
	a.db = a.db.Session(session)
	return &a
}

func (a approvalTaskBelongsToApprovalRule) Model(m *model.ApprovalTask) *approvalTaskBelongsToApprovalRuleTx {
	return &approvalTaskBelongsToApprovalRuleTx{a.db.Model(m).Association(a.Name())}
}

type approvalTaskBelongsToApprovalRuleTx struct{ tx *gorm.Association }

func (a approvalTaskBelongsToApprovalRuleTx) Find() (result *model.ApprovalRule, err error) {
	return result, a.tx.Find(&result)
}

func (a approvalTaskBelongsToApprovalRuleTx) Append(values ...*model.ApprovalRule) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a approvalTaskBelongsToApprovalRuleTx) Replace(values ...*model.ApprovalRule) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a approvalTaskBelongsToApprovalRuleTx) Delete(values ...*model.ApprovalRule) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a approvalTaskBelongsToApprovalRuleTx) Clear() error {
	return a.tx.Clear()
}

func (a approvalTaskBelongsToApprovalRuleTx) Count() int64 {
	return a.tx.Count()
}

type approvalTaskBelongsToExpLog struct {
	db *gorm.DB

	field.RelationField
}

func (a approvalTaskBelongsToExpLog) Where(conds ...field.Expr) *approvalTaskBelongsToExpLog {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a approvalTaskBelongsToExpLog) WithContext(ctx context.Context) *approvalTaskBelongsToExpLog {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a approvalTaskBelongsToExpLog) Session(session *gorm.Session) *approvalTaskBelongsToExpLog {
	a.db = a.db.Session(session)
	return &a
}

func (a approvalTaskBelongsToExpLog) Model(m *model.ApprovalTask) *approvalTaskBelongsToExpLogTx {
	return &approvalTaskBelongsToExpLogTx{a.db.Model(m).Association(a.Name())}
}

type approvalTaskBelongsToExpLogTx struct{ tx *gorm.Association }

func (a approvalTaskBelongsToExpLogTx) Find() (result *model.ExpLog, err error) {
	return result, a.tx.Find(&result)
}

func (a approvalTaskBelongsToExpLogTx) Append(values ...*model.ExpLog) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a approvalTaskBelongsToExpLogTx) Replace(values ...*model.ExpLog) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a approvalTaskBelongsToExpLogTx) Delete(values ...*model.ExpLog) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a approvalTaskBelongsToExpLogTx) Clear() error {
	return a.tx.Clear()
}

func (a approvalTaskBelongsToExpLogTx) Count() int64 {
	return a.tx.Count()
}

type approvalTaskBelongsToExp struct {
	db *gorm.DB

	field.RelationField

	Groups struct {
		field.RelationField
	}
	ApprovalTasks struct {
		field.RelationField
		ApprovalRule struct {
			field.RelationField
		}
		ExpLog struct {
			field.RelationField
		}
		Exp struct {
			field.RelationField
			Layer struct {
				field.RelationField
			}
			Batch struct {
				field.RelationField
			}
			Groups struct {
				field.RelationField
			}
			ApprovalTasks struct {
				field.RelationField
			}
		}
	}
	Layer struct {
		field.RelationField
	}
	Batch struct {
		field.RelationField
	}
}

func (a approvalTaskBelongsToExp) Where(conds ...field.Expr) *approvalTaskBelongsToExp {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a approvalTaskBelongsToExp) WithContext(ctx context.Context) *approvalTaskBelongsToExp {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a approvalTaskBelongsToExp) Session(session *gorm.Session) *approvalTaskBelongsToExp {
	a.db = a.db.Session(session)
	return &a
}

func (a approvalTaskBelongsToExp) Model(m *model.ApprovalTask) *approvalTaskBelongsToExpTx {
	return &approvalTaskBelongsToExpTx{a.db.Model(m).Association(a.Name())}
}

type approvalTaskBelongsToExpTx struct{ tx *gorm.Association }

func (a approvalTaskBelongsToExpTx) Find() (result *model.Exp, err error) {
	return result, a.tx.Find(&result)
}

func (a approvalTaskBelongsToExpTx) Append(values ...*model.Exp) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a approvalTaskBelongsToExpTx) Replace(values ...*model.Exp) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a approvalTaskBelongsToExpTx) Delete(values ...*model.Exp) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a approvalTaskBelongsToExpTx) Clear() error {
	return a.tx.Clear()
}

func (a approvalTaskBelongsToExpTx) Count() int64 {
	return a.tx.Count()
}

type approvalTaskDo struct{ gen.DO }

type IApprovalTaskDo interface {
	gen.SubQuery
	Debug() IApprovalTaskDo
	WithContext(ctx context.Context) IApprovalTaskDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IApprovalTaskDo
	WriteDB() IApprovalTaskDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IApprovalTaskDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IApprovalTaskDo
	Not(conds ...gen.Condition) IApprovalTaskDo
	Or(conds ...gen.Condition) IApprovalTaskDo
	Select(conds ...field.Expr) IApprovalTaskDo
	Where(conds ...gen.Condition) IApprovalTaskDo
	Order(conds ...field.Expr) IApprovalTaskDo
	Distinct(cols ...field.Expr) IApprovalTaskDo
	Omit(cols ...field.Expr) IApprovalTaskDo
	Join(table schema.Tabler, on ...field.Expr) IApprovalTaskDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IApprovalTaskDo
	RightJoin(table schema.Tabler, on ...field.Expr) IApprovalTaskDo
	Group(cols ...field.Expr) IApprovalTaskDo
	Having(conds ...gen.Condition) IApprovalTaskDo
	Limit(limit int) IApprovalTaskDo
	Offset(offset int) IApprovalTaskDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IApprovalTaskDo
	Unscoped() IApprovalTaskDo
	Create(values ...*model.ApprovalTask) error
	CreateInBatches(values []*model.ApprovalTask, batchSize int) error
	Save(values ...*model.ApprovalTask) error
	First() (*model.ApprovalTask, error)
	Take() (*model.ApprovalTask, error)
	Last() (*model.ApprovalTask, error)
	Find() ([]*model.ApprovalTask, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ApprovalTask, err error)
	FindInBatches(result *[]*model.ApprovalTask, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ApprovalTask) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IApprovalTaskDo
	Assign(attrs ...field.AssignExpr) IApprovalTaskDo
	Joins(fields ...field.RelationField) IApprovalTaskDo
	Preload(fields ...field.RelationField) IApprovalTaskDo
	FirstOrInit() (*model.ApprovalTask, error)
	FirstOrCreate() (*model.ApprovalTask, error)
	FindByPage(offset int, limit int) (result []*model.ApprovalTask, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IApprovalTaskDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a approvalTaskDo) Debug() IApprovalTaskDo {
	return a.withDO(a.DO.Debug())
}

func (a approvalTaskDo) WithContext(ctx context.Context) IApprovalTaskDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a approvalTaskDo) ReadDB() IApprovalTaskDo {
	return a.Clauses(dbresolver.Read)
}

func (a approvalTaskDo) WriteDB() IApprovalTaskDo {
	return a.Clauses(dbresolver.Write)
}

func (a approvalTaskDo) Session(config *gorm.Session) IApprovalTaskDo {
	return a.withDO(a.DO.Session(config))
}

func (a approvalTaskDo) Clauses(conds ...clause.Expression) IApprovalTaskDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a approvalTaskDo) Returning(value interface{}, columns ...string) IApprovalTaskDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a approvalTaskDo) Not(conds ...gen.Condition) IApprovalTaskDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a approvalTaskDo) Or(conds ...gen.Condition) IApprovalTaskDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a approvalTaskDo) Select(conds ...field.Expr) IApprovalTaskDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a approvalTaskDo) Where(conds ...gen.Condition) IApprovalTaskDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a approvalTaskDo) Order(conds ...field.Expr) IApprovalTaskDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a approvalTaskDo) Distinct(cols ...field.Expr) IApprovalTaskDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a approvalTaskDo) Omit(cols ...field.Expr) IApprovalTaskDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a approvalTaskDo) Join(table schema.Tabler, on ...field.Expr) IApprovalTaskDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a approvalTaskDo) LeftJoin(table schema.Tabler, on ...field.Expr) IApprovalTaskDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a approvalTaskDo) RightJoin(table schema.Tabler, on ...field.Expr) IApprovalTaskDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a approvalTaskDo) Group(cols ...field.Expr) IApprovalTaskDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a approvalTaskDo) Having(conds ...gen.Condition) IApprovalTaskDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a approvalTaskDo) Limit(limit int) IApprovalTaskDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a approvalTaskDo) Offset(offset int) IApprovalTaskDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a approvalTaskDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IApprovalTaskDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a approvalTaskDo) Unscoped() IApprovalTaskDo {
	return a.withDO(a.DO.Unscoped())
}

func (a approvalTaskDo) Create(values ...*model.ApprovalTask) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a approvalTaskDo) CreateInBatches(values []*model.ApprovalTask, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a approvalTaskDo) Save(values ...*model.ApprovalTask) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a approvalTaskDo) First() (*model.ApprovalTask, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ApprovalTask), nil
	}
}

func (a approvalTaskDo) Take() (*model.ApprovalTask, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ApprovalTask), nil
	}
}

func (a approvalTaskDo) Last() (*model.ApprovalTask, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ApprovalTask), nil
	}
}

func (a approvalTaskDo) Find() ([]*model.ApprovalTask, error) {
	result, err := a.DO.Find()
	return result.([]*model.ApprovalTask), err
}

func (a approvalTaskDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ApprovalTask, err error) {
	buf := make([]*model.ApprovalTask, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a approvalTaskDo) FindInBatches(result *[]*model.ApprovalTask, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a approvalTaskDo) Attrs(attrs ...field.AssignExpr) IApprovalTaskDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a approvalTaskDo) Assign(attrs ...field.AssignExpr) IApprovalTaskDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a approvalTaskDo) Joins(fields ...field.RelationField) IApprovalTaskDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a approvalTaskDo) Preload(fields ...field.RelationField) IApprovalTaskDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a approvalTaskDo) FirstOrInit() (*model.ApprovalTask, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ApprovalTask), nil
	}
}

func (a approvalTaskDo) FirstOrCreate() (*model.ApprovalTask, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ApprovalTask), nil
	}
}

func (a approvalTaskDo) FindByPage(offset int, limit int) (result []*model.ApprovalTask, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a approvalTaskDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a approvalTaskDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a approvalTaskDo) Delete(models ...*model.ApprovalTask) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *approvalTaskDo) withDO(do gen.Dao) *approvalTaskDo {
	a.DO = *do.(*gen.DO)
	return a
}
