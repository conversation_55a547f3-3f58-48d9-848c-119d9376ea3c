// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.7k7k.com/data/abAdmin/model"
)

func newUser(db *gorm.DB, opts ...gen.DOOption) user {
	_user := user{}

	_user.userDo.UseDB(db, opts...)
	_user.userDo.UseModel(&model.User{})

	tableName := _user.userDo.TableName()
	_user.ALL = field.NewAsterisk(tableName)
	_user.ID = field.NewInt32(tableName, "id")
	_user.Account = field.NewString(tableName, "account")
	_user.Password = field.NewString(tableName, "password")
	_user.Status = field.NewInt32(tableName, "status")
	_user.BiDataManager = field.NewBool(tableName, "bi_data_manager")
	_user.CnOrder = field.NewInt8(tableName, "cn_order")
	_user.HwOrder = field.NewInt8(tableName, "hw_order")
	_user.Realname = field.NewString(tableName, "realname")
	_user.Alias_ = field.NewString(tableName, "alias")
	_user.Email = field.NewString(tableName, "email")
	_user.Telephone = field.NewString(tableName, "telephone")
	_user.Description = field.NewString(tableName, "description")
	_user.RememberToken = field.NewString(tableName, "remember_token")
	_user.CreatedAt = field.NewTime(tableName, "created_at")
	_user.LoginAt = field.NewTime(tableName, "login_at")
	_user.UpdatedAt = field.NewTime(tableName, "updated_at")
	_user.GamePower = field.NewString(tableName, "game_power")
	_user.Type = field.NewInt32(tableName, "type")
	_user.Step = field.NewInt32(tableName, "step")
	_user.FinanceStatus = field.NewInt32(tableName, "finance_status")
	_user.ExpiredAt = field.NewTime(tableName, "expired_at")
	_user.Remark = field.NewString(tableName, "remark")
	_user.FinanceRemark = field.NewString(tableName, "finance_remark")
	_user.TouserOpenid = field.NewString(tableName, "touser_openid")
	_user.BixinOpenid = field.NewString(tableName, "bixin_openid")
	_user.Groups = field.NewString(tableName, "groups")
	_user.IsGroupleader = field.NewInt32(tableName, "is_groupleader")
	_user.IsProjectLeader = field.NewString(tableName, "is_project_leader")
	_user.MobilePhone = field.NewString(tableName, "mobile_phone")

	_user.fillFieldMap()

	return _user
}

type user struct {
	userDo

	ALL             field.Asterisk
	ID              field.Int32
	Account         field.String
	Password        field.String
	Status          field.Int32
	BiDataManager   field.Bool
	CnOrder         field.Int8
	HwOrder         field.Int8
	Realname        field.String
	Alias_          field.String
	Email           field.String
	Telephone       field.String
	Description     field.String
	RememberToken   field.String
	CreatedAt       field.Time
	LoginAt         field.Time
	UpdatedAt       field.Time
	GamePower       field.String
	Type            field.Int32
	Step            field.Int32
	FinanceStatus   field.Int32
	ExpiredAt       field.Time
	Remark          field.String
	FinanceRemark   field.String
	TouserOpenid    field.String
	BixinOpenid     field.String
	Groups          field.String
	IsGroupleader   field.Int32
	IsProjectLeader field.String
	MobilePhone     field.String

	fieldMap map[string]field.Expr
}

func (u user) Table(newTableName string) *user {
	u.userDo.UseTable(newTableName)
	return u.updateTableName(newTableName)
}

func (u user) As(alias string) *user {
	u.userDo.DO = *(u.userDo.As(alias).(*gen.DO))
	return u.updateTableName(alias)
}

func (u *user) updateTableName(table string) *user {
	u.ALL = field.NewAsterisk(table)
	u.ID = field.NewInt32(table, "id")
	u.Account = field.NewString(table, "account")
	u.Password = field.NewString(table, "password")
	u.Status = field.NewInt32(table, "status")
	u.BiDataManager = field.NewBool(table, "bi_data_manager")
	u.CnOrder = field.NewInt8(table, "cn_order")
	u.HwOrder = field.NewInt8(table, "hw_order")
	u.Realname = field.NewString(table, "realname")
	u.Alias_ = field.NewString(table, "alias")
	u.Email = field.NewString(table, "email")
	u.Telephone = field.NewString(table, "telephone")
	u.Description = field.NewString(table, "description")
	u.RememberToken = field.NewString(table, "remember_token")
	u.CreatedAt = field.NewTime(table, "created_at")
	u.LoginAt = field.NewTime(table, "login_at")
	u.UpdatedAt = field.NewTime(table, "updated_at")
	u.GamePower = field.NewString(table, "game_power")
	u.Type = field.NewInt32(table, "type")
	u.Step = field.NewInt32(table, "step")
	u.FinanceStatus = field.NewInt32(table, "finance_status")
	u.ExpiredAt = field.NewTime(table, "expired_at")
	u.Remark = field.NewString(table, "remark")
	u.FinanceRemark = field.NewString(table, "finance_remark")
	u.TouserOpenid = field.NewString(table, "touser_openid")
	u.BixinOpenid = field.NewString(table, "bixin_openid")
	u.Groups = field.NewString(table, "groups")
	u.IsGroupleader = field.NewInt32(table, "is_groupleader")
	u.IsProjectLeader = field.NewString(table, "is_project_leader")
	u.MobilePhone = field.NewString(table, "mobile_phone")

	u.fillFieldMap()

	return u
}

func (u *user) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := u.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (u *user) fillFieldMap() {
	u.fieldMap = make(map[string]field.Expr, 29)
	u.fieldMap["id"] = u.ID
	u.fieldMap["account"] = u.Account
	u.fieldMap["password"] = u.Password
	u.fieldMap["status"] = u.Status
	u.fieldMap["bi_data_manager"] = u.BiDataManager
	u.fieldMap["cn_order"] = u.CnOrder
	u.fieldMap["hw_order"] = u.HwOrder
	u.fieldMap["realname"] = u.Realname
	u.fieldMap["alias"] = u.Alias_
	u.fieldMap["email"] = u.Email
	u.fieldMap["telephone"] = u.Telephone
	u.fieldMap["description"] = u.Description
	u.fieldMap["remember_token"] = u.RememberToken
	u.fieldMap["created_at"] = u.CreatedAt
	u.fieldMap["login_at"] = u.LoginAt
	u.fieldMap["updated_at"] = u.UpdatedAt
	u.fieldMap["game_power"] = u.GamePower
	u.fieldMap["type"] = u.Type
	u.fieldMap["step"] = u.Step
	u.fieldMap["finance_status"] = u.FinanceStatus
	u.fieldMap["expired_at"] = u.ExpiredAt
	u.fieldMap["remark"] = u.Remark
	u.fieldMap["finance_remark"] = u.FinanceRemark
	u.fieldMap["touser_openid"] = u.TouserOpenid
	u.fieldMap["bixin_openid"] = u.BixinOpenid
	u.fieldMap["groups"] = u.Groups
	u.fieldMap["is_groupleader"] = u.IsGroupleader
	u.fieldMap["is_project_leader"] = u.IsProjectLeader
	u.fieldMap["mobile_phone"] = u.MobilePhone
}

func (u user) clone(db *gorm.DB) user {
	u.userDo.ReplaceConnPool(db.Statement.ConnPool)
	return u
}

func (u user) replaceDB(db *gorm.DB) user {
	u.userDo.ReplaceDB(db)
	return u
}

type userDo struct{ gen.DO }

type IUserDo interface {
	gen.SubQuery
	Debug() IUserDo
	WithContext(ctx context.Context) IUserDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IUserDo
	WriteDB() IUserDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IUserDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IUserDo
	Not(conds ...gen.Condition) IUserDo
	Or(conds ...gen.Condition) IUserDo
	Select(conds ...field.Expr) IUserDo
	Where(conds ...gen.Condition) IUserDo
	Order(conds ...field.Expr) IUserDo
	Distinct(cols ...field.Expr) IUserDo
	Omit(cols ...field.Expr) IUserDo
	Join(table schema.Tabler, on ...field.Expr) IUserDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IUserDo
	RightJoin(table schema.Tabler, on ...field.Expr) IUserDo
	Group(cols ...field.Expr) IUserDo
	Having(conds ...gen.Condition) IUserDo
	Limit(limit int) IUserDo
	Offset(offset int) IUserDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IUserDo
	Unscoped() IUserDo
	Create(values ...*model.User) error
	CreateInBatches(values []*model.User, batchSize int) error
	Save(values ...*model.User) error
	First() (*model.User, error)
	Take() (*model.User, error)
	Last() (*model.User, error)
	Find() ([]*model.User, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.User, err error)
	FindInBatches(result *[]*model.User, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.User) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IUserDo
	Assign(attrs ...field.AssignExpr) IUserDo
	Joins(fields ...field.RelationField) IUserDo
	Preload(fields ...field.RelationField) IUserDo
	FirstOrInit() (*model.User, error)
	FirstOrCreate() (*model.User, error)
	FindByPage(offset int, limit int) (result []*model.User, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IUserDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (u userDo) Debug() IUserDo {
	return u.withDO(u.DO.Debug())
}

func (u userDo) WithContext(ctx context.Context) IUserDo {
	return u.withDO(u.DO.WithContext(ctx))
}

func (u userDo) ReadDB() IUserDo {
	return u.Clauses(dbresolver.Read)
}

func (u userDo) WriteDB() IUserDo {
	return u.Clauses(dbresolver.Write)
}

func (u userDo) Session(config *gorm.Session) IUserDo {
	return u.withDO(u.DO.Session(config))
}

func (u userDo) Clauses(conds ...clause.Expression) IUserDo {
	return u.withDO(u.DO.Clauses(conds...))
}

func (u userDo) Returning(value interface{}, columns ...string) IUserDo {
	return u.withDO(u.DO.Returning(value, columns...))
}

func (u userDo) Not(conds ...gen.Condition) IUserDo {
	return u.withDO(u.DO.Not(conds...))
}

func (u userDo) Or(conds ...gen.Condition) IUserDo {
	return u.withDO(u.DO.Or(conds...))
}

func (u userDo) Select(conds ...field.Expr) IUserDo {
	return u.withDO(u.DO.Select(conds...))
}

func (u userDo) Where(conds ...gen.Condition) IUserDo {
	return u.withDO(u.DO.Where(conds...))
}

func (u userDo) Order(conds ...field.Expr) IUserDo {
	return u.withDO(u.DO.Order(conds...))
}

func (u userDo) Distinct(cols ...field.Expr) IUserDo {
	return u.withDO(u.DO.Distinct(cols...))
}

func (u userDo) Omit(cols ...field.Expr) IUserDo {
	return u.withDO(u.DO.Omit(cols...))
}

func (u userDo) Join(table schema.Tabler, on ...field.Expr) IUserDo {
	return u.withDO(u.DO.Join(table, on...))
}

func (u userDo) LeftJoin(table schema.Tabler, on ...field.Expr) IUserDo {
	return u.withDO(u.DO.LeftJoin(table, on...))
}

func (u userDo) RightJoin(table schema.Tabler, on ...field.Expr) IUserDo {
	return u.withDO(u.DO.RightJoin(table, on...))
}

func (u userDo) Group(cols ...field.Expr) IUserDo {
	return u.withDO(u.DO.Group(cols...))
}

func (u userDo) Having(conds ...gen.Condition) IUserDo {
	return u.withDO(u.DO.Having(conds...))
}

func (u userDo) Limit(limit int) IUserDo {
	return u.withDO(u.DO.Limit(limit))
}

func (u userDo) Offset(offset int) IUserDo {
	return u.withDO(u.DO.Offset(offset))
}

func (u userDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IUserDo {
	return u.withDO(u.DO.Scopes(funcs...))
}

func (u userDo) Unscoped() IUserDo {
	return u.withDO(u.DO.Unscoped())
}

func (u userDo) Create(values ...*model.User) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Create(values)
}

func (u userDo) CreateInBatches(values []*model.User, batchSize int) error {
	return u.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (u userDo) Save(values ...*model.User) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Save(values)
}

func (u userDo) First() (*model.User, error) {
	if result, err := u.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.User), nil
	}
}

func (u userDo) Take() (*model.User, error) {
	if result, err := u.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.User), nil
	}
}

func (u userDo) Last() (*model.User, error) {
	if result, err := u.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.User), nil
	}
}

func (u userDo) Find() ([]*model.User, error) {
	result, err := u.DO.Find()
	return result.([]*model.User), err
}

func (u userDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.User, err error) {
	buf := make([]*model.User, 0, batchSize)
	err = u.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (u userDo) FindInBatches(result *[]*model.User, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return u.DO.FindInBatches(result, batchSize, fc)
}

func (u userDo) Attrs(attrs ...field.AssignExpr) IUserDo {
	return u.withDO(u.DO.Attrs(attrs...))
}

func (u userDo) Assign(attrs ...field.AssignExpr) IUserDo {
	return u.withDO(u.DO.Assign(attrs...))
}

func (u userDo) Joins(fields ...field.RelationField) IUserDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Joins(_f))
	}
	return &u
}

func (u userDo) Preload(fields ...field.RelationField) IUserDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Preload(_f))
	}
	return &u
}

func (u userDo) FirstOrInit() (*model.User, error) {
	if result, err := u.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.User), nil
	}
}

func (u userDo) FirstOrCreate() (*model.User, error) {
	if result, err := u.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.User), nil
	}
}

func (u userDo) FindByPage(offset int, limit int) (result []*model.User, count int64, err error) {
	result, err = u.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = u.Offset(-1).Limit(-1).Count()
	return
}

func (u userDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = u.Count()
	if err != nil {
		return
	}

	err = u.Offset(offset).Limit(limit).Scan(result)
	return
}

func (u userDo) Scan(result interface{}) (err error) {
	return u.DO.Scan(result)
}

func (u userDo) Delete(models ...*model.User) (result gen.ResultInfo, err error) {
	return u.DO.Delete(models)
}

func (u *userDo) withDO(do gen.Dao) *userDo {
	u.DO = *do.(*gen.DO)
	return u
}
