// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.7k7k.com/data/abAdmin/model"
)

func newBatch(db *gorm.DB, opts ...gen.DOOption) batch {
	_batch := batch{}

	_batch.batchDo.UseDB(db, opts...)
	_batch.batchDo.UseModel(&model.Batch{})

	tableName := _batch.batchDo.TableName()
	_batch.ALL = field.NewAsterisk(tableName)
	_batch.ID = field.NewInt64(tableName, "id")
	_batch.Name = field.NewString(tableName, "name")
	_batch.ProjectID = field.NewInt64(tableName, "project_id")
	_batch.Desc = field.NewString(tableName, "desc")
	_batch.CreateTime = field.NewTime(tableName, "create_time")
	_batch.UpdateTime = field.NewTime(tableName, "update_time")
	_batch.IsDeleted = field.NewInt32(tableName, "is_deleted")

	_batch.fillFieldMap()

	return _batch
}

// batch 批次表
type batch struct {
	batchDo

	ALL        field.Asterisk
	ID         field.Int64
	Name       field.String // 批次名称
	ProjectID  field.Int64  // ab_project关联id
	Desc       field.String // 批次描述
	CreateTime field.Time   // 创建时间
	UpdateTime field.Time   // 更新时间
	IsDeleted  field.Int32  // 状态：1已删除 2未删除

	fieldMap map[string]field.Expr
}

func (b batch) Table(newTableName string) *batch {
	b.batchDo.UseTable(newTableName)
	return b.updateTableName(newTableName)
}

func (b batch) As(alias string) *batch {
	b.batchDo.DO = *(b.batchDo.As(alias).(*gen.DO))
	return b.updateTableName(alias)
}

func (b *batch) updateTableName(table string) *batch {
	b.ALL = field.NewAsterisk(table)
	b.ID = field.NewInt64(table, "id")
	b.Name = field.NewString(table, "name")
	b.ProjectID = field.NewInt64(table, "project_id")
	b.Desc = field.NewString(table, "desc")
	b.CreateTime = field.NewTime(table, "create_time")
	b.UpdateTime = field.NewTime(table, "update_time")
	b.IsDeleted = field.NewInt32(table, "is_deleted")

	b.fillFieldMap()

	return b
}

func (b *batch) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := b.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (b *batch) fillFieldMap() {
	b.fieldMap = make(map[string]field.Expr, 7)
	b.fieldMap["id"] = b.ID
	b.fieldMap["name"] = b.Name
	b.fieldMap["project_id"] = b.ProjectID
	b.fieldMap["desc"] = b.Desc
	b.fieldMap["create_time"] = b.CreateTime
	b.fieldMap["update_time"] = b.UpdateTime
	b.fieldMap["is_deleted"] = b.IsDeleted
}

func (b batch) clone(db *gorm.DB) batch {
	b.batchDo.ReplaceConnPool(db.Statement.ConnPool)
	return b
}

func (b batch) replaceDB(db *gorm.DB) batch {
	b.batchDo.ReplaceDB(db)
	return b
}

type batchDo struct{ gen.DO }

type IBatchDo interface {
	gen.SubQuery
	Debug() IBatchDo
	WithContext(ctx context.Context) IBatchDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IBatchDo
	WriteDB() IBatchDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IBatchDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IBatchDo
	Not(conds ...gen.Condition) IBatchDo
	Or(conds ...gen.Condition) IBatchDo
	Select(conds ...field.Expr) IBatchDo
	Where(conds ...gen.Condition) IBatchDo
	Order(conds ...field.Expr) IBatchDo
	Distinct(cols ...field.Expr) IBatchDo
	Omit(cols ...field.Expr) IBatchDo
	Join(table schema.Tabler, on ...field.Expr) IBatchDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IBatchDo
	RightJoin(table schema.Tabler, on ...field.Expr) IBatchDo
	Group(cols ...field.Expr) IBatchDo
	Having(conds ...gen.Condition) IBatchDo
	Limit(limit int) IBatchDo
	Offset(offset int) IBatchDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IBatchDo
	Unscoped() IBatchDo
	Create(values ...*model.Batch) error
	CreateInBatches(values []*model.Batch, batchSize int) error
	Save(values ...*model.Batch) error
	First() (*model.Batch, error)
	Take() (*model.Batch, error)
	Last() (*model.Batch, error)
	Find() ([]*model.Batch, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Batch, err error)
	FindInBatches(result *[]*model.Batch, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.Batch) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IBatchDo
	Assign(attrs ...field.AssignExpr) IBatchDo
	Joins(fields ...field.RelationField) IBatchDo
	Preload(fields ...field.RelationField) IBatchDo
	FirstOrInit() (*model.Batch, error)
	FirstOrCreate() (*model.Batch, error)
	FindByPage(offset int, limit int) (result []*model.Batch, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IBatchDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (b batchDo) Debug() IBatchDo {
	return b.withDO(b.DO.Debug())
}

func (b batchDo) WithContext(ctx context.Context) IBatchDo {
	return b.withDO(b.DO.WithContext(ctx))
}

func (b batchDo) ReadDB() IBatchDo {
	return b.Clauses(dbresolver.Read)
}

func (b batchDo) WriteDB() IBatchDo {
	return b.Clauses(dbresolver.Write)
}

func (b batchDo) Session(config *gorm.Session) IBatchDo {
	return b.withDO(b.DO.Session(config))
}

func (b batchDo) Clauses(conds ...clause.Expression) IBatchDo {
	return b.withDO(b.DO.Clauses(conds...))
}

func (b batchDo) Returning(value interface{}, columns ...string) IBatchDo {
	return b.withDO(b.DO.Returning(value, columns...))
}

func (b batchDo) Not(conds ...gen.Condition) IBatchDo {
	return b.withDO(b.DO.Not(conds...))
}

func (b batchDo) Or(conds ...gen.Condition) IBatchDo {
	return b.withDO(b.DO.Or(conds...))
}

func (b batchDo) Select(conds ...field.Expr) IBatchDo {
	return b.withDO(b.DO.Select(conds...))
}

func (b batchDo) Where(conds ...gen.Condition) IBatchDo {
	return b.withDO(b.DO.Where(conds...))
}

func (b batchDo) Order(conds ...field.Expr) IBatchDo {
	return b.withDO(b.DO.Order(conds...))
}

func (b batchDo) Distinct(cols ...field.Expr) IBatchDo {
	return b.withDO(b.DO.Distinct(cols...))
}

func (b batchDo) Omit(cols ...field.Expr) IBatchDo {
	return b.withDO(b.DO.Omit(cols...))
}

func (b batchDo) Join(table schema.Tabler, on ...field.Expr) IBatchDo {
	return b.withDO(b.DO.Join(table, on...))
}

func (b batchDo) LeftJoin(table schema.Tabler, on ...field.Expr) IBatchDo {
	return b.withDO(b.DO.LeftJoin(table, on...))
}

func (b batchDo) RightJoin(table schema.Tabler, on ...field.Expr) IBatchDo {
	return b.withDO(b.DO.RightJoin(table, on...))
}

func (b batchDo) Group(cols ...field.Expr) IBatchDo {
	return b.withDO(b.DO.Group(cols...))
}

func (b batchDo) Having(conds ...gen.Condition) IBatchDo {
	return b.withDO(b.DO.Having(conds...))
}

func (b batchDo) Limit(limit int) IBatchDo {
	return b.withDO(b.DO.Limit(limit))
}

func (b batchDo) Offset(offset int) IBatchDo {
	return b.withDO(b.DO.Offset(offset))
}

func (b batchDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IBatchDo {
	return b.withDO(b.DO.Scopes(funcs...))
}

func (b batchDo) Unscoped() IBatchDo {
	return b.withDO(b.DO.Unscoped())
}

func (b batchDo) Create(values ...*model.Batch) error {
	if len(values) == 0 {
		return nil
	}
	return b.DO.Create(values)
}

func (b batchDo) CreateInBatches(values []*model.Batch, batchSize int) error {
	return b.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (b batchDo) Save(values ...*model.Batch) error {
	if len(values) == 0 {
		return nil
	}
	return b.DO.Save(values)
}

func (b batchDo) First() (*model.Batch, error) {
	if result, err := b.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Batch), nil
	}
}

func (b batchDo) Take() (*model.Batch, error) {
	if result, err := b.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Batch), nil
	}
}

func (b batchDo) Last() (*model.Batch, error) {
	if result, err := b.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Batch), nil
	}
}

func (b batchDo) Find() ([]*model.Batch, error) {
	result, err := b.DO.Find()
	return result.([]*model.Batch), err
}

func (b batchDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Batch, err error) {
	buf := make([]*model.Batch, 0, batchSize)
	err = b.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (b batchDo) FindInBatches(result *[]*model.Batch, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return b.DO.FindInBatches(result, batchSize, fc)
}

func (b batchDo) Attrs(attrs ...field.AssignExpr) IBatchDo {
	return b.withDO(b.DO.Attrs(attrs...))
}

func (b batchDo) Assign(attrs ...field.AssignExpr) IBatchDo {
	return b.withDO(b.DO.Assign(attrs...))
}

func (b batchDo) Joins(fields ...field.RelationField) IBatchDo {
	for _, _f := range fields {
		b = *b.withDO(b.DO.Joins(_f))
	}
	return &b
}

func (b batchDo) Preload(fields ...field.RelationField) IBatchDo {
	for _, _f := range fields {
		b = *b.withDO(b.DO.Preload(_f))
	}
	return &b
}

func (b batchDo) FirstOrInit() (*model.Batch, error) {
	if result, err := b.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Batch), nil
	}
}

func (b batchDo) FirstOrCreate() (*model.Batch, error) {
	if result, err := b.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Batch), nil
	}
}

func (b batchDo) FindByPage(offset int, limit int) (result []*model.Batch, count int64, err error) {
	result, err = b.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = b.Offset(-1).Limit(-1).Count()
	return
}

func (b batchDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = b.Count()
	if err != nil {
		return
	}

	err = b.Offset(offset).Limit(limit).Scan(result)
	return
}

func (b batchDo) Scan(result interface{}) (err error) {
	return b.DO.Scan(result)
}

func (b batchDo) Delete(models ...*model.Batch) (result gen.ResultInfo, err error) {
	return b.DO.Delete(models)
}

func (b *batchDo) withDO(do gen.Dao) *batchDo {
	b.DO = *do.(*gen.DO)
	return b
}
