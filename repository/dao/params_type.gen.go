// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.7k7k.com/data/abAdmin/model"
)

func newParamsType(db *gorm.DB, opts ...gen.DOOption) paramsType {
	_paramsType := paramsType{}

	_paramsType.paramsTypeDo.UseDB(db, opts...)
	_paramsType.paramsTypeDo.UseModel(&model.ParamsType{})

	tableName := _paramsType.paramsTypeDo.TableName()
	_paramsType.ALL = field.NewAsterisk(tableName)
	_paramsType.ID = field.NewInt64(tableName, "id")
	_paramsType.Name = field.NewString(tableName, "name")
	_paramsType.State = field.NewInt32(tableName, "state")
	_paramsType.CreateTime = field.NewTime(tableName, "create_time")
	_paramsType.UpdateTime = field.NewTime(tableName, "update_time")

	_paramsType.fillFieldMap()

	return _paramsType
}

// paramsType 参数类型表
type paramsType struct {
	paramsTypeDo

	ALL        field.Asterisk
	ID         field.Int64
	Name       field.String // 参数名称
	State      field.Int32  // 状态：-1删除 0禁用 1启用
	CreateTime field.Time   // 创建时间
	UpdateTime field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (p paramsType) Table(newTableName string) *paramsType {
	p.paramsTypeDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p paramsType) As(alias string) *paramsType {
	p.paramsTypeDo.DO = *(p.paramsTypeDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *paramsType) updateTableName(table string) *paramsType {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewInt64(table, "id")
	p.Name = field.NewString(table, "name")
	p.State = field.NewInt32(table, "state")
	p.CreateTime = field.NewTime(table, "create_time")
	p.UpdateTime = field.NewTime(table, "update_time")

	p.fillFieldMap()

	return p
}

func (p *paramsType) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *paramsType) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 5)
	p.fieldMap["id"] = p.ID
	p.fieldMap["name"] = p.Name
	p.fieldMap["state"] = p.State
	p.fieldMap["create_time"] = p.CreateTime
	p.fieldMap["update_time"] = p.UpdateTime
}

func (p paramsType) clone(db *gorm.DB) paramsType {
	p.paramsTypeDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p paramsType) replaceDB(db *gorm.DB) paramsType {
	p.paramsTypeDo.ReplaceDB(db)
	return p
}

type paramsTypeDo struct{ gen.DO }

type IParamsTypeDo interface {
	gen.SubQuery
	Debug() IParamsTypeDo
	WithContext(ctx context.Context) IParamsTypeDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IParamsTypeDo
	WriteDB() IParamsTypeDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IParamsTypeDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IParamsTypeDo
	Not(conds ...gen.Condition) IParamsTypeDo
	Or(conds ...gen.Condition) IParamsTypeDo
	Select(conds ...field.Expr) IParamsTypeDo
	Where(conds ...gen.Condition) IParamsTypeDo
	Order(conds ...field.Expr) IParamsTypeDo
	Distinct(cols ...field.Expr) IParamsTypeDo
	Omit(cols ...field.Expr) IParamsTypeDo
	Join(table schema.Tabler, on ...field.Expr) IParamsTypeDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IParamsTypeDo
	RightJoin(table schema.Tabler, on ...field.Expr) IParamsTypeDo
	Group(cols ...field.Expr) IParamsTypeDo
	Having(conds ...gen.Condition) IParamsTypeDo
	Limit(limit int) IParamsTypeDo
	Offset(offset int) IParamsTypeDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IParamsTypeDo
	Unscoped() IParamsTypeDo
	Create(values ...*model.ParamsType) error
	CreateInBatches(values []*model.ParamsType, batchSize int) error
	Save(values ...*model.ParamsType) error
	First() (*model.ParamsType, error)
	Take() (*model.ParamsType, error)
	Last() (*model.ParamsType, error)
	Find() ([]*model.ParamsType, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ParamsType, err error)
	FindInBatches(result *[]*model.ParamsType, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ParamsType) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IParamsTypeDo
	Assign(attrs ...field.AssignExpr) IParamsTypeDo
	Joins(fields ...field.RelationField) IParamsTypeDo
	Preload(fields ...field.RelationField) IParamsTypeDo
	FirstOrInit() (*model.ParamsType, error)
	FirstOrCreate() (*model.ParamsType, error)
	FindByPage(offset int, limit int) (result []*model.ParamsType, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IParamsTypeDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p paramsTypeDo) Debug() IParamsTypeDo {
	return p.withDO(p.DO.Debug())
}

func (p paramsTypeDo) WithContext(ctx context.Context) IParamsTypeDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p paramsTypeDo) ReadDB() IParamsTypeDo {
	return p.Clauses(dbresolver.Read)
}

func (p paramsTypeDo) WriteDB() IParamsTypeDo {
	return p.Clauses(dbresolver.Write)
}

func (p paramsTypeDo) Session(config *gorm.Session) IParamsTypeDo {
	return p.withDO(p.DO.Session(config))
}

func (p paramsTypeDo) Clauses(conds ...clause.Expression) IParamsTypeDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p paramsTypeDo) Returning(value interface{}, columns ...string) IParamsTypeDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p paramsTypeDo) Not(conds ...gen.Condition) IParamsTypeDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p paramsTypeDo) Or(conds ...gen.Condition) IParamsTypeDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p paramsTypeDo) Select(conds ...field.Expr) IParamsTypeDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p paramsTypeDo) Where(conds ...gen.Condition) IParamsTypeDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p paramsTypeDo) Order(conds ...field.Expr) IParamsTypeDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p paramsTypeDo) Distinct(cols ...field.Expr) IParamsTypeDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p paramsTypeDo) Omit(cols ...field.Expr) IParamsTypeDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p paramsTypeDo) Join(table schema.Tabler, on ...field.Expr) IParamsTypeDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p paramsTypeDo) LeftJoin(table schema.Tabler, on ...field.Expr) IParamsTypeDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p paramsTypeDo) RightJoin(table schema.Tabler, on ...field.Expr) IParamsTypeDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p paramsTypeDo) Group(cols ...field.Expr) IParamsTypeDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p paramsTypeDo) Having(conds ...gen.Condition) IParamsTypeDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p paramsTypeDo) Limit(limit int) IParamsTypeDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p paramsTypeDo) Offset(offset int) IParamsTypeDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p paramsTypeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IParamsTypeDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p paramsTypeDo) Unscoped() IParamsTypeDo {
	return p.withDO(p.DO.Unscoped())
}

func (p paramsTypeDo) Create(values ...*model.ParamsType) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p paramsTypeDo) CreateInBatches(values []*model.ParamsType, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p paramsTypeDo) Save(values ...*model.ParamsType) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p paramsTypeDo) First() (*model.ParamsType, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ParamsType), nil
	}
}

func (p paramsTypeDo) Take() (*model.ParamsType, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ParamsType), nil
	}
}

func (p paramsTypeDo) Last() (*model.ParamsType, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ParamsType), nil
	}
}

func (p paramsTypeDo) Find() ([]*model.ParamsType, error) {
	result, err := p.DO.Find()
	return result.([]*model.ParamsType), err
}

func (p paramsTypeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ParamsType, err error) {
	buf := make([]*model.ParamsType, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p paramsTypeDo) FindInBatches(result *[]*model.ParamsType, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p paramsTypeDo) Attrs(attrs ...field.AssignExpr) IParamsTypeDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p paramsTypeDo) Assign(attrs ...field.AssignExpr) IParamsTypeDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p paramsTypeDo) Joins(fields ...field.RelationField) IParamsTypeDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p paramsTypeDo) Preload(fields ...field.RelationField) IParamsTypeDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p paramsTypeDo) FirstOrInit() (*model.ParamsType, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ParamsType), nil
	}
}

func (p paramsTypeDo) FirstOrCreate() (*model.ParamsType, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ParamsType), nil
	}
}

func (p paramsTypeDo) FindByPage(offset int, limit int) (result []*model.ParamsType, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p paramsTypeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p paramsTypeDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p paramsTypeDo) Delete(models ...*model.ParamsType) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *paramsTypeDo) withDO(do gen.Dao) *paramsTypeDo {
	p.DO = *do.(*gen.DO)
	return p
}
