package repository

import "git.7k7k.com/data/abAdmin/repository/dao"

// @autowire(set=dao)
func NewIUserInfoDo(q *dao.Query) dao.IUserInfoDo { return q.UserInfo.Debug() }

// @autowire(set=dao)
func NewIUserDo(q *QueryForBIv1) dao.IUserDo { return q.User.Debug() }

// @autowire(set=dao)
//func NewIDataSourceDo(q *dao2.Query) dao2.IDataSourceDo { return q.DataSource.Debug() }

// @autowire(set=dao)
//func NewIAccountUserDo(q *QueryAd) dao2.IAccountUserDo { return q.AccountUser.Debug() }

// @autowire(set=dao)
//func NewIDataModelDo(q *dao2.Query) dao2.IDataModelDo { return q.DataModel.Debug() }
