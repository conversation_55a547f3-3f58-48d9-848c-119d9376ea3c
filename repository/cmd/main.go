package main

import (
	"log"

	"gorm.io/gen/field"

	"git.7k7k.com/data/abAdmin/model"
	"git.7k7k.com/data/abAdmin/repository/contract"
	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gorm"
)

var models = []any{
	model.User{},
	model.Role{},
	model.Permission{},
	model.PermissionRole{},
	model.UserInfo{},
}

func main() {
	log.SetFlags(log.LstdFlags | log.Llongfile)

	gen_default_dao()
}

func gen_default_dao() {
	g := gen.NewGenerator(gen.Config{
		OutPath: "./repository/dao",
		OutFile: "query_gen.go",
		// Mode:    gen.WithQueryInterface, // generate mode
		Mode:          gen.WithoutContext | gen.WithQueryInterface, // generate mode
		ModelPkgPath:  "./model",
		FieldNullable: true,

		// Mode:    gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface, // generate mode
	})

	dsn := "xxgame_abtest_slt:CJy9p+lh7s4Wc6PG@tcp(106.75.29.167:23306)/abtest_dev?charset=utf8mb4&parseTime=True&loc=Local"
	//dsn := "root:root@tcp(127.0.0.1:3306)/abtest?charset=utf8mb4&parseTime=True&loc=Local"
	gormdb, _ := gorm.Open(mysql.Open(dsn))

	g.UseDB(gormdb)

	list := []any{
		g.GenerateModel("group",
			gen.FieldType("data", "GroupData"), gen.FieldGORMTag("data", AsJSON),
		),
		g.GenerateModel("batch"),
		g.GenerateModel("exp",
			gen.FieldType("data", "ExpData"), gen.FieldGORMTag("data", AsJSON),
			gen.FieldType("strategy", "*Strategy"), gen.FieldGORMTag("strategy", AsJSON),
			gen.FieldType("csr", "[]*CSRData"), gen.FieldGORMTag("csr", AsJSONArray),
			gen.FieldRelateModel(field.HasMany, "Groups", &model.Group{}, &field.RelateConfig{
				RelateSlicePointer: true,
				GORMTag: field.GormTag{
					"foreignKey": []string{"exp_id"},
				},
			}),
			gen.FieldRelateModel(field.HasMany, "ApprovalTasks", &model.ApprovalTask{}, &field.RelateConfig{
				RelateSlicePointer: true,
				GORMTag: field.GormTag{
					"foreignKey": []string{"exp_id"},
				},
			}),
			gen.FieldRelateModel(field.BelongsTo, "Layer", &model.Layer{}, &field.RelateConfig{
				RelateSlicePointer: false,
				GORMTag: field.GormTag{
					"foreignKey": []string{"layer_id"},
				},
			}),
			gen.FieldRelateModel(field.BelongsTo, "Batch", &model.Batch{}, &field.RelateConfig{
				RelateSlicePointer: false,
				GORMTag: field.GormTag{
					"foreignKey": []string{"batch_id"},
				},
			}),
		),
		g.GenerateModel("layer"),
		g.GenerateModel("params_type"),
		g.GenerateModel("project",
			gen.FieldType("data", "ProjectData"), gen.FieldGORMTag("data", AsJSON),
			gen.FieldType("feature_conflict", "FeatureConflictRules"), gen.FieldGORMTag("feature_conflict", AsJSON),
			gen.FieldType("feature_stats", "*FeatureStats"), gen.FieldGORMTag("feature_stats", AsJSON),
		),
		g.GenerateModel("tag"),
		g.GenerateModel("strategy_tpl"),
		g.GenerateModel("exp_log",
			gen.FieldNew("Notify", "NotifyMessage", field.Tag{}.Set("gorm", "-")),
			//gen.FieldType("exp_snapshot", "*dto.Snapshot"), gen.FieldGORMTag("exp_snapshot", AsJSONArray),
		),
		g.GenerateModel("split_group"),
		g.GenerateModel("exp_task"),
		g.GenerateModel("approval_rules",
			gen.FieldType("stages", "[]Stage"), gen.FieldGORMTag("stages", AsJSONArray),
			gen.FieldType("approvers", "[][]int32"), gen.FieldGORMTag("approvers", AsJSONArray),
			gen.FieldType("search_text", "[]string"), gen.FieldGORMTag("search_text", AsJSONArray),
			gen.FieldNewTag("name", field.Tag{"binding": "required"}),      // 添加 Gin 的 binding 标签
			gen.FieldNewTag("approvers", field.Tag{"binding": "required"}), // 添加 Gin 的 binding 标签
			gen.FieldNewTag("stages", field.Tag{"binding": "required"}),    // 添加 Gin 的 binding 标签
		),
		g.GenerateModel("approval_tasks",
			gen.FieldType("approvers", "[]int32"), gen.FieldGORMTag("approvers", AsJSONArray),
			gen.FieldType("flow_approvers", "[]int32"), gen.FieldGORMTag("flow_approvers", AsJSONArray),
			gen.FieldType("search_text", "[]string"), gen.FieldGORMTag("search_text", AsJSONArray),
			gen.FieldType("changes", "*Changes"), gen.FieldGORMTag("changes", AsJSONArray),
			gen.FieldType("log", "[]*ApprovalLog"), gen.FieldGORMTag("log", AsJSONArray),
			gen.FieldRelateModel(field.BelongsTo, "ApprovalRule", &model.ApprovalRule{}, &field.RelateConfig{
				RelateSlicePointer: false,
				GORMTag: field.GormTag{
					"foreignKey": []string{"rule_id"},
				},
			}),
			gen.FieldRelateModel(field.BelongsTo, "ExpLog", &model.ExpLog{}, &field.RelateConfig{
				RelateSlicePointer: false,
				GORMTag: field.GormTag{
					"foreignKey": []string{"after_snapshot_id"},
				},
			}),
			gen.FieldRelateModel(field.BelongsTo, "Exp", &model.Exp{}, &field.RelateConfig{
				RelateSlicePointer: false,
				GORMTag: field.GormTag{
					"foreignKey": []string{"exp_id"},
				},
			}),
		),
	}

	list = append(list, models...)

	g.ApplyBasic(
		list...,
	)

	g.ApplyInterface(func(contract.BaseQuery) {}, models...)

	g.Execute()
}

func AsJSON(tag field.GormTag) field.GormTag {
	tag.Set("serializer", "json")
	tag.Set("default", "'{}'")
	return tag
}

func AsJSONArray(tag field.GormTag) field.GormTag {
	tag.Set("serializer", "json")
	tag.Set("default", "'[]'")
	return tag
}
