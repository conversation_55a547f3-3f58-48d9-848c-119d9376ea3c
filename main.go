package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"log/slog"
	"os"
	"time"

	"git.7k7k.com/data/abAdmin/autowire"
	"git.7k7k.com/data/abAdmin/gopkg/etrack"
	"git.7k7k.com/data/abAdmin/infra"
	"git.7k7k.com/data/abAdmin/infra/config"
	"git.7k7k.com/data/abAdmin/service"
	"git.7k7k.com/pkg/common/debug"
	"git.7k7k.com/pkg/common/logs"
	"git.7k7k.com/pkg/common/metric"
	"github.com/Kretech/xgo/dump"
	"github.com/getsentry/sentry-go"
	"github.com/robfig/cron/v3"
)

var GIT_HEAD string

func init() {
	metric.Init("abtest_admin")

	logs.Init(os.Stdout)

	dump.Disable = !debug.IsDevMode()
}

func main() {
	configPath := flag.String("c", "./resource/config/config_local.toml", "config path")
	assetPath := flag.String("a", "./resource/assets/", "assets path")
	port := flag.Int("p", 0, "port")
	flag.Parse()

	args := flag.Args()

	if len(args) == 0 {
		args = append(args, "serve")
	}

	config.SetGlobalConfig(*configPath, *assetPath)
	app, cleanup, err := autowire.InitializeApplication()
	if err != nil {
		panic(err)
	}
	defer cleanup()

	// 加载静态资源
	err = config.LoadStaticData()
	if err != nil {
		panic(err)
	}

	c := config.GlobalConfig
	if *port != 0 {
		app.Config.Addr = fmt.Sprintf(":%d", *port)
	}
	flush := SentryInit(c.Sentry)
	defer flush(2 * time.Second)

	switch args[0] {
	case "version":
		fmt.Println(GIT_HEAD)
	case "runcode":
		runCode(app)
	case "serve":
		startCron(app)
		startConsumer(app)
		app.RunHTTP()
	default:
		flag.PrintDefaults()
	}
}

type mylog struct {
}

func (l *mylog) Printf(format string, v ...any) {
	slog.Info(fmt.Sprintf(format, v...))
}

func startConsumer(app *infra.Application) {
	app.Listener.Listen(context.Background())
}

func startCron(app *infra.Application) {
	cronLogFile := "cron.log"
	fi, err := os.OpenFile(cronLogFile, os.O_CREATE|os.O_RDWR|os.O_APPEND, 0666)
	if err != nil {
		panic(err)
	}
	CronStdLogger := log.New(fi, "", log.Lshortfile|log.LstdFlags)

	log := cron.VerbosePrintfLogger(CronStdLogger)
	c := cron.New(
		cron.WithSeconds(),
		cron.WithLogger(log),
		cron.WithChain(
			// cron.Recover(log),
			func(j cron.Job) cron.Job {
				return cron.FuncJob(func() {
					defer func() {
						if err := recover(); err != nil {
							sentry.CurrentHub().Recover(err)
						}
					}()

					j.Run()
				})
			},
		),
	)

	// 每10min刷新权限缓存
	etrack.Fatal(context.Background(), "refresh_auth_cache", app.Auth.RefreshCache())
	c.AddFunc("0 * * * * *", func() {
		etrack.Catch(context.Background(), "refresh_auth_cache", app.Auth.RefreshCache())
	})

	// 每小时同步一次BI账号
	c.AddFunc("0 0 */1 * * *", func() {
		ctx := context.Background()
		out, err := app.Auth.SyncUser(ctx, service.SyncUserIn{})
		etrack.Catch(ctx, "sync_user", err, out)
	})

	// 每10分钟同步一次实验配置
	c.AddFunc("0 */10 * * * *", func() {
		ctx := context.Background()
		etrack.Catch(ctx, "sync_exp_config", app.ExpSyncService.SyncFullExps(ctx, 14))
	})

	c.Start()
}

var runLocal func(app *infra.Application)

func runCode(app *infra.Application) {
	if runLocal != nil {
		runLocal(app)
	}

	ctx := context.Background()
	app.ExpSyncService.SyncFullExps(ctx, 90)

	fmt.Println("runcode done")
}
